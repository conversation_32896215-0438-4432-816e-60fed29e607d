{"version": 3, "file": "evm.js", "sourceRoot": "", "sources": ["../../../client/evm/evm.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,QAAQ,CAAC;AAElD,OAAO,EAAgB,uBAAuB,EAAE,MAAM,MAAM,CAAC;AAE7D,OAAO,EAAE,4BAA4B,EAAE,MAAM,gBAAgB,CAAC;AAiC9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,0CAA0C,CAAC;AAC9E,OAAO,EAAE,iBAAiB,EAAE,MAAM,yCAAyC,CAAC;AAC5E,OAAO,EAAE,gBAAgB,EAAE,MAAM,uCAAuC,CAAC;AACzE,OAAO,EACL,iBAAiB,GAGlB,MAAM,wCAAwC,CAAC;AAChD,OAAO,EACL,aAAa,GAGd,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAAE,eAAe,EAAE,MAAM,sCAAsC,CAAC;AACvE,OAAO,EACL,iBAAiB,GAGlB,MAAM,wCAAwC,CAAC;AAChD,OAAO,EAAE,eAAe,EAAE,MAAM,2CAA2C,CAAC;AAC5E,OAAO,EAAE,YAAY,EAAE,MAAM,wCAAwC,CAAC;AACtE,OAAO,EACL,oBAAoB,GAErB,MAAM,2CAA2C,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,gCAAgC,CAAC;AAC1D,OAAO,EACL,gBAAgB,GAEjB,MAAM,+BAA+B,CAAC;AAEvC,OAAO,EAAE,qBAAqB,EAAE,+BAA+B,EAAE,MAAM,uBAAuB,CAAC;AAO/F;;GAEG;AACH,MAAM,OAAO,SAAS;IACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,KAAK,CAAC,aAAa,CAAC,UAAsC,EAAE;QAC1D,MAAM,cAAc,GAAG,MAAM,gBAAgB,CAAC,gBAAgB,CAC5D;YACE,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,aAAa,EAAE,OAAO,CAAC,aAAa;SACrC,EACD,OAAO,CAAC,cAAc,CACvB,CAAC;QAEF,MAAM,OAAO,GAAG,kBAAkB,CAAC,gBAAgB,EAAE;YACnD,OAAO,EAAE,cAAc;SACxB,CAAC,CAAC;QAEH,SAAS,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;QAEtD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAyCG;IACH,KAAK,CAAC,aAAa,CAAC,OAAmC;QACrD,MAAM,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,IAAI,4BAA4B,CAAC;QAExF,MAAM,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC;YACvD,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;QAEvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAE1D,MAAM,mBAAmB,GAAG,aAAa,CACvC;gBACE,GAAG,EAAE,mBAAmB;gBACxB,OAAO,EAAE,SAAS,CAAC,sBAAsB;gBACzC,QAAQ,EAAE,QAAQ;aACnB,EACD,eAAe,CAChB,CAAC;YAEF,MAAM,cAAc,GAAG,MAAM,gBAAgB,CAAC,gBAAgB,CAC5D;gBACE,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,mBAAmB,EAAE,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC;aAC5D,EACD,OAAO,CAAC,cAAc,CACvB,CAAC;YAEF,MAAM,OAAO,GAAG,kBAAkB,CAAC,gBAAgB,EAAE;gBACnD,OAAO,EAAE,cAAc;aACxB,CAAC,CAAC;YAEH,SAAS,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;YAEtD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,KAAK,CAAC,aAAa,CAAC,OAAmC;QACrD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,MAAM,+BAA+B,EAAE,CAAC;QAE1E,MAAM,EAAE,mBAAmB,EAAE,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE;YAChD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,gBAAgB,CAAC,gBAAgB,CACtC,OAAO,CAAC,OAAO,EACf;oBACE,mBAAmB,EAAE,SAAS;iBAC/B,EACD,OAAO,CAAC,cAAc,CACvB,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACjB,OAAO,gBAAgB,CAAC,sBAAsB,CAC5C,OAAO,CAAC,IAAI,EACZ;oBACE,mBAAmB,EAAE,SAAS;iBAC/B,EACD,OAAO,CAAC,cAAc,CACvB,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC,CAAC,EAAE,CAAC;QAEL,OAAO,qBAAqB,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4CG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAAkC;QACzD,MAAM,mBAAmB,GAAG,MAAM,gBAAgB,CAAC,qBAAqB,CACtE;YACE,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;YAC/B,IAAI,EAAE,OAAO,CAAC,IAAI;SACnB,EACD,OAAO,CAAC,cAAc,CACvB,CAAC;QAEF,MAAM,YAAY,GAAG,iBAAiB,CAAC,gBAAgB,EAAE;YACvD,YAAY,EAAE,mBAAmB;YACjC,KAAK,EAAE,OAAO,CAAC,KAAK;SACrB,CAAC,CAAC;QAEH,SAAS,CAAC,kCAAkC,CAAC,YAAY,CAAC,CAAC;QAE3D,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,KAAK,CAAC,UAAU,CAAC,OAAgC;QAC/C,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,EAAE;YACjC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACjB,OAAO,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5D,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC,CAAC,EAAE,CAAC;QAEL,MAAM,OAAO,GAAG,kBAAkB,CAAC,gBAAgB,EAAE;YACnD,OAAO,EAAE,cAAc;SACxB,CAAC,CAAC;QAEH,SAAS,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;QAEtD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,KAAK,CAAC,eAAe,CAAC,OAA+B;QACnD,MAAM,mBAAmB,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE;YAC5C,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,gBAAgB,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9D,CAAC;iBAAM,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACxB,OAAO,gBAAgB,CAAC,wBAAwB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjE,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC,CAAC,EAAE,CAAC;QAEL,MAAM,YAAY,GAAG,iBAAiB,CAAC,gBAAgB,EAAE;YACvD,YAAY,EAAE,mBAAmB;YACjC,KAAK,EAAE,OAAO,CAAC,KAAK;SACrB,CAAC,CAAC;QAEH,SAAS,CAAC,kCAAkC,CAAC,YAAY,CAAC,CAAC;QAE3D,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAAwC;QAC/D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC/C,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,4DAA4D;YAC5D,MAAM,mBAAmB,GAAG,KAAK,YAAY,QAAQ,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC;YAClF,IAAI,mBAAmB,EAAE,CAAC;gBACxB,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;oBAClD,OAAO,OAAO,CAAC;gBACjB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,kEAAkE;oBAClE,MAAM,uBAAuB,GAAG,KAAK,YAAY,QAAQ,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC;oBACtF,IAAI,uBAAuB,EAAE,CAAC;wBAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;wBAC/C,OAAO,OAAO,CAAC;oBACjB,CAAC;oBACD,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,uBAAuB,CAAC,OAAuC;QACnE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,4DAA4D;YAC5D,MAAM,mBAAmB,GAAG,KAAK,YAAY,QAAQ,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC;YAClF,IAAI,mBAAmB,EAAE,CAAC;gBACxB,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;oBACvD,OAAO,OAAO,CAAC;gBACjB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,kEAAkE;oBAClE,MAAM,uBAAuB,GAAG,KAAK,YAAY,QAAQ,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,CAAC;oBACtF,IAAI,uBAAuB,EAAE,CAAC;wBAC5B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;wBACpD,OAAO,OAAO,CAAC;oBACjB,CAAC;oBACD,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,YAAY,CAChB,OAA4B;QAE5B,OAAO,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,eAAe,CACnB,OAA+B;QAE/B,OAAO,eAAe,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,KAAK,CAAC,gBAAgB,CAAC,OAAgC;QACrD,OAAO,gBAAgB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,KAAK,CAAC,YAAY,CAAC,UAAqC,EAAE;QACxD,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,eAAe,CAAC;YACzD,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC;QAEH,OAAO;YACL,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAC3C,MAAM,UAAU,GAAG,kBAAkB,CAAC,gBAAgB,EAAE;oBACtD,OAAO;iBACR,CAAC,CAAC;gBAEH,SAAS,CAAC,kCAAkC,CAAC,UAAU,CAAC,CAAC;gBAEzD,OAAO,UAAU,CAAC;YACpB,CAAC,CAAC;YACF,aAAa,EAAE,WAAW,CAAC,aAAa;SACzC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAAiC;QACvD,OAAO,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,KAAK,CAAC,iBAAiB,CAAC,UAAoC,EAAE;QAC5D,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAAC,oBAAoB,CAAC;YAChE,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC;QAEH,OAAO;YACL,QAAQ,EAAE,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC/C,OAAO,EAAE,OAAO,CAAC,OAAkB;gBACnC,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAY,CAAC;gBACtC,IAAI,EAAE,WAAW;aAClB,CAAC,CAAC;YACH,aAAa,EAAE,aAAa,CAAC,aAAa;SAC3C,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,KAAK,CAAC,oBAAoB,CAAC,OAAoC;QAC7D,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE;YACvF,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChC,EAAE,EAAE,IAAI,CAAC,EAAa;gBACtB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBAC5B,IAAI,EAAE,IAAI,CAAC,IAAW;aACvB,CAAC,CAAC;YACH,YAAY,EAAE,OAAO,CAAC,YAAY;SACnC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,UAAU,EAAE,MAAM,CAAC,UAAiB;YACpC,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC/B,EAAE,EAAE,IAAI,CAAC,EAAa;gBACtB,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBACzB,IAAI,EAAE,IAAI,CAAC,IAAW;aACvB,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACH,KAAK,CAAC,aAAa,CAAC,OAA6B;QAC/C,OAAO,aAAa,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6CG;IACH,KAAK,CAAC,eAAe,CAAC,OAA+B;QACnD,OAAO,eAAe,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,KAAK,CAAC,iBAAiB,CACrB,OAA4C;QAE5C,OAAO,iBAAiB,CAAC,gBAAgB,EAAE;YACzC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,cAAc,EAAE,OAAO,CAAC,cAAc;SACvC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,KAAK,CAAC,QAAQ,CAAC,OAAwB;QACrC,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,WAAW,CAClD,OAAO,CAAC,OAAO,EACf;YACE,IAAI,EAAE,OAAO,CAAC,IAAI;SACnB,EACD,OAAO,CAAC,cAAc,CACvB,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,SAAS,CAAC,SAAgB;SACtC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,KAAK,CAAC,WAAW,CAAC,OAA2B;QAC3C,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,cAAc,CACrD,OAAO,CAAC,OAAO,EACf;YACE,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,EACD,OAAO,CAAC,cAAc,CACvB,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,SAAS,CAAC,SAAgB;SACtC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4CG;IACH,KAAK,CAAC,aAAa,CAAC,OAA6B;QAC/C,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QACjD,MAAM,KAAK,GAAG;YACZ,YAAY,EAAE,uBAAuB,CAAC,EAAE,MAAM,EAAE,CAAC;YACjD,GAAG,OAAO,CAAC,KAAK;SACjB,CAAC;QAEF,MAAM,cAAc,GAAyB;YAC3C,MAAM;YACN,KAAK;YACL,WAAW;YACX,OAAO;SACR,CAAC;QAEF,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,gBAAgB,CACvD,OAAO,CAAC,OAAO,EACf,cAAc,EACd,OAAO,CAAC,cAAc,CACvB,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,SAAS,CAAC,SAAgB;SACtC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACH,KAAK,CAAC,eAAe,CAAC,OAA+B;QACnD,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,kBAAkB,CACzD,OAAO,CAAC,OAAO,EACf;YACE,WAAW,EAAE,OAAO,CAAC,WAAW;SACjC,EACD,OAAO,CAAC,cAAc,CACvB,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,SAAS,CAAC,iBAAwB;SAC9C,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAwCG;IACH,KAAK,CAAC,aAAa,CAAC,OAAgC;QAClD,MAAM,cAAc,GAAG,MAAM,gBAAgB,CAAC,gBAAgB,CAC5D,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,cAAc,CACvB,CAAC;QAEF,MAAM,OAAO,GAAG,kBAAkB,CAAC,gBAAgB,EAAE;YACnD,OAAO,EAAE,cAAc;SACxB,CAAC,CAAC;QAEH,SAAS,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;QAEtD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,KAAK,CAAC,oBAAoB,CACxB,OAAoC;QAEpC,OAAO,oBAAoB,CAAC,gBAAgB,EAAE;YAC5C,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;CACF"}