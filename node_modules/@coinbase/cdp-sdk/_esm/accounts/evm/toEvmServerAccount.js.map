{"version": 3, "file": "toEvmServerAccount.js", "sourceRoot": "", "sources": ["../../../accounts/evm/toEvmServerAccount.ts"], "names": [], "mappings": "AAAA,OAAO,EAKL,uBAAuB,EACvB,oBAAoB,GACrB,MAAM,MAAM,CAAC;AAEd,OAAO,EAAe,IAAI,EAAE,MAAM,gCAAgC,CAAC;AAEnE,OAAO,EAAoB,SAAS,EAAE,MAAM,qCAAqC,CAAC;AAElF,OAAO,EAGL,2BAA2B,GAC5B,MAAM,uDAAuD,CAAC;AAC/D,OAAO,EACL,iBAAiB,GAGlB,MAAM,wCAAwC,CAAC;AAChD,OAAO,EACL,aAAa,GAGd,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAAE,eAAe,EAAE,MAAM,sCAAsC,CAAC;AACvE,OAAO,EAAE,eAAe,EAAE,MAAM,2CAA2C,CAAC;AAC5E,OAAO,EAAE,mBAAmB,EAAE,MAAM,+CAA+C,CAAC;AACpF,OAAO,EAAE,uBAAuB,EAAE,MAAM,uDAAuD,CAAC;AAChG,OAAO,EAAE,QAAQ,EAAE,MAAM,wCAAwC,CAAC;AAwBlE;;;;;;;;GAQG;AACH,MAAM,UAAU,kBAAkB,CAChC,SAA+B,EAC/B,OAAkC;IAElC,MAAM,OAAO,GAAqB;QAChC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,OAAkB;QAC3C,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE;YAC3B,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;gBACrE,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;aAC5B,CAAC,CAAC;YACH,OAAO,MAAM,CAAC,SAAgB,CAAC;QACjC,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,UAA0B;YACnC,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;gBAClE,IAAI,EAAE,UAAU,CAAC,IAAI;aACtB,CAAC,CAAC;YACH,OAAO,MAAM,CAAC,SAAgB,CAAC;QACjC,CAAC;QAED,KAAK,CAAC,eAAe,CAAC,WAAoC;YACxD,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;gBACzE,WAAW,EAAE,oBAAoB,CAAC,WAAW,CAAC;aAC/C,CAAC,CAAC;YACH,OAAO,MAAM,CAAC,iBAAwB,CAAC;QACzC,CAAC;QAED,KAAK,CAAC,aAAa,CAGjB,UAAuD;YACvD,MAAM,EAAE,MAAM,GAAG,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,UAAqC,CAAC;YACpF,MAAM,KAAK,GAAG;gBACZ,YAAY,EAAE,uBAAuB,CAAC,EAAE,MAAM,EAAE,CAAC;gBACjD,GAAG,UAAU,CAAC,KAAK;aACpB,CAAC;YAEF,MAAM,cAAc,GAAG;gBACrB,MAAM,EAAE,MAAsB;gBAC9B,KAAK;gBACL,WAAW;gBACX,OAAO;aACR,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YACzF,OAAO,MAAM,CAAC,SAAgB,CAAC;QACjC,CAAC;QACD,KAAK,CAAC,QAAQ,CAAC,YAAY;YACzB,OAAO,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,uBAAuB,CAAC,CAAC;QAC7E,CAAC;QACD,KAAK,CAAC,iBAAiB,CACrB,OAAkD;YAElD,OAAO,iBAAiB,CAAC,SAAS,EAAE;gBAClC,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,aAAa,CACjB,OAA8C;YAE9C,OAAO,aAAa,CAAC,SAAS,EAAE;gBAC9B,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,eAAe,CAAC,OAAgD;YACpE,OAAO,eAAe,CAAC,SAAS,EAAE;gBAChC,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,SAAS,CAAC,OAA0C;YACxD,OAAO,SAAS,CAAC,SAAS,EAAE;gBAC1B,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,OAAqC;YAC9C,OAAO,IAAI,CAAC,SAAS,EAAE;gBACrB,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,2BAA2B,CAC/B,OAAoC;YAEpC,OAAO,2BAA2B,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;QACD,KAAK,CAAC,SAAS,CAAC,OAAgC;YAC9C,OAAO,eAAe,CAAC,SAAS,EAAE;gBAChC,GAAG,OAAO;gBACV,KAAK,EAAE,IAAI,CAAC,OAAO;aACpB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,OAA2B;YACpC,OAAO,mBAAmB,CAAC,SAAS,EAAE;gBACpC,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,wCAAwC;aAC9D,CAAC,CAAC;QACL,CAAC;QACD,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;KACnC,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC"}