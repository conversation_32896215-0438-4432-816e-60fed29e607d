{"version": 3, "file": "toEvmSmartAccount.js", "sourceRoot": "", "sources": ["../../../accounts/evm/toEvmSmartAccount.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAe,MAAM,gCAAgC,CAAC;AAEnE,OAAO,EAAE,SAAS,EAAoB,MAAM,qCAAqC,CAAC;AAElF,OAAO,EAEL,2BAA2B,GAE5B,MAAM,uDAAuD,CAAC;AAC/D,OAAO,EAAE,gBAAgB,EAAE,MAAM,uCAAuC,CAAC;AACzE,OAAO,EACL,iBAAiB,GAGlB,MAAM,wCAAwC,CAAC;AAChD,OAAO,EAGL,aAAa,GACd,MAAM,oCAAoC,CAAC;AAC5C,OAAO,EAGL,iBAAiB,GAClB,MAAM,wCAAwC,CAAC;AAChD,OAAO,EAAE,eAAe,EAAE,MAAM,2CAA2C,CAAC;AAC5E,OAAO,EAAE,iBAAiB,EAAE,MAAM,6CAA6C,CAAC;AAChF,OAAO,EAAE,4BAA4B,EAAE,MAAM,4DAA4D,CAAC;AAC1G,OAAO,EAAE,QAAQ,EAAE,MAAM,wCAAwC,CAAC;AAClE,OAAO,EACL,oBAAoB,GAGrB,MAAM,2CAA2C,CAAC;AA0BnD;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,iBAAiB,CAC/B,SAA+B,EAC/B,OAAiC;IAEjC,MAAM,OAAO,GAAoB;QAC/B,OAAO,EAAE,OAAO,CAAC,YAAY,CAAC,OAAkB;QAChD,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QACvB,KAAK,CAAC,QAAQ,CAAC,YAAY;YACzB,OAAO,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,4BAA4B,CAAC,CAAC;QAClF,CAAC;QACD,KAAK,CAAC,iBAAiB,CACrB,OAAkD;YAElD,OAAO,iBAAiB,CAAC,SAAS,EAAE;gBAClC,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,iBAAiB,CACrB,OAAkE;YAElE,OAAO,iBAAiB,CAAC,SAAS,EAAE;gBAClC,GAAG,OAAO;gBACV,YAAY,EAAE,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,oBAAoB,CACxB,OAAiE;YAEjE,OAAO,oBAAoB,CAAC,SAAS,EAAE;gBACrC,GAAG,OAAO;gBACV,mBAAmB,EAAE,OAAO,CAAC,OAAO;aACrC,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,gBAAgB,CACpB,OAAsD;YAEtD,OAAO,gBAAgB,CAAC,SAAS,EAAE;gBACjC,GAAG,OAAO;gBACV,YAAY,EAAE,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,aAAa,CACjB,OAA8C;YAE9C,OAAO,aAAa,CAAC,SAAS,EAAE;gBAC9B,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,SAAS,CAAC,OAA0C;YACxD,OAAO,SAAS,CAAC,SAAS,EAAE;gBAC1B,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,OAAqC;YAC9C,OAAO,IAAI,CAAC,SAAS,EAAE;gBACrB,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,2BAA2B,CAC/B,OAAoC;YAEpC,OAAO,2BAA2B,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;QACD,KAAK,CAAC,SAAS,CAAC,OAAqC;YACnD,OAAO,eAAe,CAAC,SAAS,EAAE;gBAChC,GAAG,OAAO;gBACV,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,8CAA8C;gBACnE,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,uCAAuC;gBAC9E,YAAY,EAAE,OAAO,EAAE,gDAAgD;aACxE,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,OAAgC;YACzC,OAAO,iBAAiB,CAAC,SAAS,EAAE;gBAClC,GAAG,OAAO;gBACV,YAAY,EAAE,OAAO;gBACrB,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,8CAA8C;gBACnE,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,uCAAuC;aAC/E,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI;QAC/B,IAAI,EAAE,WAAW;KAClB,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC"}