import { fund } from "../../actions/evm/fund/fund.js";
import { quoteFund } from "../../actions/evm/fund/quoteFund.js";
import { waitForFundOperationReceipt, } from "../../actions/evm/fund/waitForFundOperationReceipt.js";
import { getUserOperation } from "../../actions/evm/getUserOperation.js";
import { listTokenBalances, } from "../../actions/evm/listTokenBalances.js";
import { requestFaucet, } from "../../actions/evm/requestFaucet.js";
import { sendUserOperation, } from "../../actions/evm/sendUserOperation.js";
import { createSwapQuote } from "../../actions/evm/swap/createSwapQuote.js";
import { sendSwapOperation } from "../../actions/evm/swap/sendSwapOperation.js";
import { smartAccountTransferStrategy } from "../../actions/evm/transfer/smartAccountTransferStrategy.js";
import { transfer } from "../../actions/evm/transfer/transfer.js";
import { waitForUserOperation, } from "../../actions/evm/waitForUserOperation.js";
/**
 * Creates a EvmSmartAccount instance from an existing EvmSmartAccount and owner.
 * Use this to interact with previously deployed EvmSmartAccounts, rather than creating new ones.
 *
 * The owner must be the original owner of the evm smart account.
 *
 * @param {CdpOpenApiClientType} apiClient - The API client.
 * @param {ToEvmSmartAccountOptions} options - Configuration options.
 * @param {EvmSmartAccount} options.smartAccount - The deployed evm smart account.
 * @param {EvmAccount} options.owner - The owner which signs for the smart account.
 * @returns {EvmSmartAccount} A configured EvmSmartAccount instance ready for user operation submission.
 */
export function toEvmSmartAccount(apiClient, options) {
    const account = {
        address: options.smartAccount.address,
        owners: [options.owner],
        async transfer(transferArgs) {
            return transfer(apiClient, account, transferArgs, smartAccountTransferStrategy);
        },
        async listTokenBalances(options) {
            return listTokenBalances(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async sendUserOperation(options) {
            return sendUserOperation(apiClient, {
                ...options,
                smartAccount: account,
            });
        },
        async waitForUserOperation(options) {
            return waitForUserOperation(apiClient, {
                ...options,
                smartAccountAddress: account.address,
            });
        },
        async getUserOperation(options) {
            return getUserOperation(apiClient, {
                ...options,
                smartAccount: account,
            });
        },
        async requestFaucet(options) {
            return requestFaucet(apiClient, {
                ...options,
                address: account.address,
            });
        },
        async quoteFund(options) {
            return quoteFund(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async fund(options) {
            return fund(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async waitForFundOperationReceipt(options) {
            return waitForFundOperationReceipt(apiClient, options);
        },
        async quoteSwap(options) {
            return createSwapQuote(apiClient, {
                ...options,
                taker: this.address, // Always use smart account's address as taker
                signerAddress: this.owners[0].address, // Always use owner's address as signer
                smartAccount: account, // Pass smart account for execute method support
            });
        },
        async swap(options) {
            return sendSwapOperation(apiClient, {
                ...options,
                smartAccount: account,
                taker: this.address, // Always use smart account's address as taker
                signerAddress: this.owners[0].address, // Always use owner's address as signer
            });
        },
        name: options.smartAccount.name,
        type: "evm-smart",
    };
    return account;
}
//# sourceMappingURL=toEvmSmartAccount.js.map