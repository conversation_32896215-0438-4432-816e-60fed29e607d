import { getTypesForEIP712Domain, serializeTransaction, } from "viem";
import { fund } from "../../actions/evm/fund/fund.js";
import { quoteFund } from "../../actions/evm/fund/quoteFund.js";
import { waitForFundOperationReceipt, } from "../../actions/evm/fund/waitForFundOperationReceipt.js";
import { listTokenBalances, } from "../../actions/evm/listTokenBalances.js";
import { requestFaucet, } from "../../actions/evm/requestFaucet.js";
import { sendTransaction } from "../../actions/evm/sendTransaction.js";
import { createSwapQuote } from "../../actions/evm/swap/createSwapQuote.js";
import { sendSwapTransaction } from "../../actions/evm/swap/sendSwapTransaction.js";
import { accountTransferStrategy } from "../../actions/evm/transfer/accountTransferStrategy.js";
import { transfer } from "../../actions/evm/transfer/transfer.js";
/**
 * Creates a Server-managed EvmAccount instance from an existing EvmAccount.
 * Use this to interact with previously deployed EvmAccounts, rather than creating new ones.
 *
 * @param {CdpOpenApiClientType} apiClient - The API client.
 * @param {ToEvmServerAccountOptions} options - Configuration options.
 * @param {EvmAccount} options.account - The EvmAccount that was previously created.
 * @returns {EvmServerAccount} A configured EvmAccount instance ready for signing.
 */
export function toEvmServerAccount(apiClient, options) {
    const account = {
        address: options.account.address,
        async signMessage({ message }) {
            const result = await apiClient.signEvmMessage(options.account.address, {
                message: message.toString(),
            });
            return result.signature;
        },
        async sign(parameters) {
            const result = await apiClient.signEvmHash(options.account.address, {
                hash: parameters.hash,
            });
            return result.signature;
        },
        async signTransaction(transaction) {
            const result = await apiClient.signEvmTransaction(options.account.address, {
                transaction: serializeTransaction(transaction),
            });
            return result.signedTransaction;
        },
        async signTypedData(parameters) {
            const { domain = {}, message, primaryType } = parameters;
            const types = {
                EIP712Domain: getTypesForEIP712Domain({ domain }),
                ...parameters.types,
            };
            const openApiMessage = {
                domain: domain,
                types,
                primaryType,
                message,
            };
            const result = await apiClient.signEvmTypedData(options.account.address, openApiMessage);
            return result.signature;
        },
        async transfer(transferArgs) {
            return transfer(apiClient, account, transferArgs, accountTransferStrategy);
        },
        async listTokenBalances(options) {
            return listTokenBalances(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async requestFaucet(options) {
            return requestFaucet(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async sendTransaction(options) {
            return sendTransaction(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async quoteFund(options) {
            return quoteFund(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async fund(options) {
            return fund(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async waitForFundOperationReceipt(options) {
            return waitForFundOperationReceipt(apiClient, options);
        },
        async quoteSwap(options) {
            return createSwapQuote(apiClient, {
                ...options,
                taker: this.address,
            });
        },
        async swap(options) {
            return sendSwapTransaction(apiClient, {
                ...options,
                address: this.address,
                taker: this.address, // Always use account's address as taker
            });
        },
        name: options.account.name,
        type: "evm-server",
        policies: options.account.policies,
    };
    return account;
}
//# sourceMappingURL=toEvmServerAccount.js.map