{"version": 3, "file": "toSolanaAccount.js", "sourceRoot": "", "sources": ["../../../accounts/solana/toSolanaAccount.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,uCAAuC,CAAC;AACtE,OAAO,EAAE,WAAW,EAAE,MAAM,qCAAqC,CAAC;AAClE,OAAO,EAAE,eAAe,EAAE,MAAM,yCAAyC,CAAC;AAC1E,OAAO,EAAE,QAAQ,EAAwB,MAAM,kCAAkC,CAAC;AAgBlF;;;;;;;;GAQG;AACH,MAAM,UAAU,eAAe,CAC7B,SAA+B,EAC/B,OAA+B;IAE/B,MAAM,OAAO,GAAkB;QAC7B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO;QAChC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,KAAK,CAAC,aAAa,CAAC,OAA8C;YAChE,OAAO,aAAa,CAAC,SAAS,EAAE;gBAC9B,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,OAA4C;YAC5D,OAAO,WAAW,CAAC,SAAS,EAAE;gBAC5B,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,eAAe,CACnB,OAAgD;YAEhD,OAAO,eAAe,CAAC,SAAS,EAAE;gBAChC,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QACD,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;QAClC,KAAK,CAAC,QAAQ,CAAC,OAAsC;YACnD,OAAO,QAAQ,CAAC,SAAS,EAAE;gBACzB,GAAG,OAAO;gBACV,IAAI,EAAE,OAAO,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;KACF,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC"}