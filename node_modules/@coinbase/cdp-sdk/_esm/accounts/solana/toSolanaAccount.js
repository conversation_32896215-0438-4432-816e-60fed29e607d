import { requestFaucet } from "../../actions/solana/requestFaucet.js";
import { signMessage } from "../../actions/solana/signMessage.js";
import { signTransaction } from "../../actions/solana/signTransaction.js";
import { transfer } from "../../actions/solana/transfer.js";
/**
 * Creates a Solana account instance with actions from an existing Solana account.
 * Use this to interact with previously deployed Solana accounts, rather than creating new ones.
 *
 * @param {CdpOpenApiClientType} apiClient - The API client.
 * @param {ToSolanaAccountOptions} options - Configuration options.
 * @param {Account} options.account - The Solana account that was previously created.
 * @returns {SolanaAccount} A configured SolanaAccount instance ready for signing.
 */
export function toSolanaAccount(apiClient, options) {
    const account = {
        address: options.account.address,
        name: options.account.name,
        async requestFaucet(options) {
            return requestFaucet(apiClient, {
                ...options,
                address: account.address,
            });
        },
        async signMessage(options) {
            return signMessage(apiClient, {
                ...options,
                address: account.address,
            });
        },
        async signTransaction(options) {
            return signTransaction(apiClient, {
                ...options,
                address: account.address,
            });
        },
        policies: options.account.policies,
        async transfer(options) {
            return transfer(apiClient, {
                ...options,
                from: account.address,
            });
        },
    };
    return account;
}
//# sourceMappingURL=toSolanaAccount.js.map