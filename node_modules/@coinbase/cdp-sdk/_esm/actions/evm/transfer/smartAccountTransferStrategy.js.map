{"version": 3, "file": "smartAccountTransferStrategy.js", "sourceRoot": "", "sources": ["../../../../actions/evm/transfer/smartAccountTransferStrategy.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAEpD,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAK5D,MAAM,CAAC,MAAM,4BAA4B,GAA+C;IACtF,eAAe,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,EAAE;QACtF,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;YACpB,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,SAAS,EAAE;gBAChD,YAAY,EAAE,IAAI;gBAClB,YAAY;gBACZ,OAAO;gBACP,KAAK,EAAE;oBACL;wBACE,EAAE;wBACF,KAAK;wBACL,IAAI,EAAE,IAAI;qBACX;iBACF;aACF,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,SAAS,EAAE;gBAChD,YAAY,EAAE,IAAI;gBAClB,YAAY;gBACZ,OAAO;gBACP,KAAK,EAAE;oBACL;wBACE,EAAE,EAAE,YAAY;wBAChB,IAAI,EAAE,kBAAkB,CAAC;4BACvB,GAAG,EAAE,QAAQ;4BACb,YAAY,EAAE,SAAS;4BACvB,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC;yBAClB,CAAC;qBACH;oBACD;wBACE,EAAE,EAAE,YAAY;wBAChB,IAAI,EAAE,kBAAkB,CAAC;4BACvB,GAAG,EAAE,QAAQ;4BACb,YAAY,EAAE,UAAU;4BACxB,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC;yBAClB,CAAC;qBACH;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;CACF,CAAC"}