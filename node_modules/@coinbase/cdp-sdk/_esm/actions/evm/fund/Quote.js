/**
 * A class representing a funding quote that can be executed.
 */
export class Quote {
    /** Quote for the transfer. */
    quoteId;
    /** Network to transfer the funds to. */
    network;
    /** The amount in fiat currency. */
    fiatAmount;
    /** The fiat currency. */
    fiatCurrency;
    /** The amount in the token to transfer. */
    tokenAmount;
    /** The token to transfer. */
    token;
    /** Fees in the token to transfer. */
    fees;
    apiClient;
    /**
     * Creates a new Quote instance.
     *
     * @param apiClient - The API client.
     * @param quoteId - The quote ID.
     * @param network - The network to transfer funds to.
     * @param fiatAmount - The amount in fiat currency.
     * @param fiatCurrency - The fiat currency.
     * @param tokenAmount - The amount in the token to transfer.
     * @param token - The token to transfer.
     * @param fees - Fees for the transfer.
     */
    constructor(apiClient, quoteId, network, fiatAmount, fiatCurrency, tokenAmount, token, fees) {
        this.apiClient = apiClient;
        this.quoteId = quoteId;
        this.network = network;
        this.fiatAmount = fiatAmount;
        this.fiatCurrency = fiatCurrency;
        this.tokenAmount = tokenAmount;
        this.token = token;
        this.fees = fees;
    }
    /**
     * Executes the quote to perform the actual fund transfer.
     *
     * @returns A promise that resolves to the result of the executed quote.
     */
    async execute() {
        const transfer = await this.apiClient.executePaymentTransferQuote(this.quoteId);
        return {
            id: transfer.id,
            network: transfer.target.network,
            targetAmount: transfer.targetAmount,
            targetCurrency: transfer.targetCurrency,
            status: transfer.status,
            transactionHash: transfer.transactionHash,
        };
    }
}
//# sourceMappingURL=Quote.js.map