import { getMint, getAssociatedTokenAddress, getAccount, createAssociatedTokenAccountInstruction, createTransferCheckedInstruction, } from "@solana/spl-token";
import { MessageV0, <PERSON>Key, SystemProgram, TransactionMessage, VersionedTransaction, } from "@solana/web3.js";
import { getConnectedNetwork, getOrCreateConnection, getUsdcMintAddress, } from "./utils.js";
/**
 * Transfers SOL or SPL tokens between accounts
 *
 * @param apiClient - The API client to use
 * @param options - The transfer options
 *
 * @returns The transfer result
 */
export async function transfer(apiClient, options) {
    const connection = getOrCreateConnection({ networkOrConnection: options.network });
    const tx = options.token === "sol"
        ? await getNativeTransfer({
            connection,
            from: options.from,
            to: options.to,
            amount: options.amount,
        })
        : await getSplTransfer({
            connection,
            from: options.from,
            to: options.to,
            mintAddress: options.token === "usdc"
                ? getUsdcMintAddress(await getConnectedNetwork(connection))
                : options.token,
            amount: options.amount,
        });
    const serializedTx = Buffer.from(tx.serialize()).toString("base64");
    const signedTxResponse = await apiClient.signSolanaTransaction(options.from, {
        transaction: serializedTx,
    });
    const decodedSignedTx = Buffer.from(signedTxResponse.signedTransaction, "base64");
    const signature = await connection.sendRawTransaction(decodedSignedTx, {
        skipPreflight: false,
        maxRetries: 3,
    });
    return { signature };
}
/**
 * Gets the instructions for a native SOL transfer
 *
 * @param options - The options for the native SOL transfer
 *
 * @param options.connection - The Solana connection
 * @param options.from - The source address
 * @param options.to - The destination address
 * @param options.amount - The amount to transfer
 *
 * @returns The native SOL transfer transaction
 */
async function getNativeTransfer({ connection, from, to, amount, }) {
    const { blockhash } = await connection.getLatestBlockhash();
    const instructions = [
        SystemProgram.transfer({
            fromPubkey: new PublicKey(from),
            toPubkey: new PublicKey(to),
            lamports: amount,
        }),
    ];
    const messageV0 = new TransactionMessage({
        payerKey: new PublicKey(from),
        recentBlockhash: blockhash,
        instructions,
    }).compileToV0Message();
    return new VersionedTransaction(messageV0);
}
/**
 * Gets the instructions for a SPL token transfer
 *
 * @param options - The options for the SPL token transfer
 *
 * @param options.connection - The Solana connection
 * @param options.from - The source address
 * @param options.to - The destination address
 * @param options.mintAddress - The mint address of the token
 * @param options.amount - The amount to transfer
 *
 * @returns The SPL token transfer transaction
 */
async function getSplTransfer({ connection, from, to, mintAddress, amount, }) {
    const fromPubkey = new PublicKey(from);
    const toPubkey = new PublicKey(to);
    const mintPubkey = new PublicKey(mintAddress);
    let mintInfo;
    try {
        mintInfo = await getMint(connection, mintPubkey);
    }
    catch (error) {
        throw new Error(`Failed to fetch mint info for mint address ${mintAddress}. Error: ${error}`);
    }
    const sourceAta = await getAssociatedTokenAddress(mintPubkey, fromPubkey);
    const destinationAta = await getAssociatedTokenAddress(mintPubkey, toPubkey);
    const instructions = [];
    const sourceAccount = await getAccount(connection, sourceAta);
    if (sourceAccount.amount < amount) {
        throw new Error(`Insufficient token balance. Have ${sourceAccount.amount}, need ${amount}`);
    }
    // Check if destination account exists, if not create it
    try {
        await getAccount(connection, destinationAta);
    }
    catch {
        instructions.push(createAssociatedTokenAccountInstruction(fromPubkey, destinationAta, toPubkey, mintPubkey));
    }
    instructions.push(createTransferCheckedInstruction(sourceAta, mintPubkey, destinationAta, fromPubkey, amount, mintInfo.decimals));
    return new VersionedTransaction(MessageV0.compile({
        payerKey: fromPubkey,
        instructions: instructions,
        recentBlockhash: (await connection.getLatestBlockhash()).blockhash,
    }));
}
//# sourceMappingURL=transfer.js.map