{"version": 3, "file": "withAuth.js", "sourceRoot": "", "sources": ["../../../../auth/hooks/axios/withAuth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAiB,YAAY,EAAE,MAAM,OAAO,CAAC;AAEpD,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAqCrD;;;;;;GAMG;AACH,MAAM,UAAU,QAAQ,CAAC,WAA0B,EAAE,OAA+B;IAClF,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAC,WAAW,EAAC,EAAE;QACvD,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC;QAErE,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,iBAAiB,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC;QAEjE,iCAAiC;QACjC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEvC,6BAA6B;QAC7B,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC;YACnC,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,aAAa,EAAE,MAAM;YACrB,WAAW,EAAE,GAAG,CAAC,IAAI;YACrB,WAAW,EAAE,GAAG,CAAC,QAAQ;YACzB,WAAW,EAAE,WAAW,CAAC,IAAI;YAC7B,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC;QAEH,gCAAgC;QAChC,WAAW,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC;YACrC,GAAG,WAAW,CAAC,OAAO;YACtB,GAAG,OAAO;SACX,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBACtB,MAAM;gBACN,GAAG,EAAE,iBAAiB;gBACtB,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,IAAI,EAAE,WAAW,CAAC,IAAI;aACvB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,QAAQ,CAAC,EAAE;YACT,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;gBACvB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,IAAI,EAAE,QAAQ,CAAC,IAAI;aACpB,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,KAAK,CAAC,EAAE;YACN,sDAAsD;YACtD,MAAM,YAAY,GAAG;gBACnB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;gBACtC,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,OAAO;gBAChC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC;YAEF,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;YAC/C,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;IACJ,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC"}