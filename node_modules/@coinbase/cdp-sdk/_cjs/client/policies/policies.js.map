{"version": 3, "file": "policies.js", "sourceRoot": "", "sources": ["../../../client/policies/policies.ts"], "names": [], "mappings": ";;;AASA,4DAAiE;AACjE,wDAA0F;AAG1F;;GAEG;AACH,MAAa,cAAc;IACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAoCG;IACH,KAAK,CAAC,YAAY,CAAC,UAA+B,EAAE;QAClD,OAAO,2BAAgB,CAAC,YAAY,CAAC,OAAO,CAAgC,CAAC;IAC/E,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmEG;IACH,KAAK,CAAC,YAAY,CAAC,OAA4B;QAC7C,kCAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC7C,OAAO,2BAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAoB,CAAC;IAClG,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CAAC,aAAa,CAAC,OAA6B;QAC/C,OAAO,2BAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAoB,CAAC;IACvE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,KAAK,CAAC,YAAY,CAAC,OAA4B;QAC7C,OAAO,2BAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmDG;IACH,KAAK,CAAC,YAAY,CAAC,OAA4B;QAC7C,kCAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC7C,OAAO,2BAAgB,CAAC,YAAY,CAClC,OAAO,CAAC,EAAE,EACV,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,cAAc,CACJ,CAAC;IACvB,CAAC;CACF;AArOD,wCAqOC"}