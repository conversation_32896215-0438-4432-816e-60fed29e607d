"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PoliciesClient = void 0;
const index_js_1 = require("../../openapi-client/index.js");
const schema_js_1 = require("../../policies/schema.js");
/**
 * The namespace containing all Policy methods.
 */
class PoliciesClient {
    /**
     * Lists policies belonging to the developer's CDP Project.
     * Can be filtered by scope (project or account).
     *
     * @param {ListPoliciesOptions} [options] - Options for filtering and paginating the results
     * @param {string} [options.scope] - Filter policies by scope ('project' or 'account')
     * @param {number} [options.pageSize] - Maximum number of policies to return
     * @param {string} [options.pageToken] - Pagination cursor for fetching next page of results
     *
     * @returns {Promise<ListPoliciesResult>} A paginated list of policies
     *
     * @example **List all policies**
     *          ```ts
     *          const { policies } = await cdp.policies.listPolicies();
     *          ```
     *
     * @example **Filter by scope**
     *          ```ts
     *          const { policies } = await cdp.policies.listPolicies({
     *            scope: 'project'
     *          });
     *          ```
     *
     * @example **With pagination**
     *          ```ts
     *          // Get first page
     *          const firstPage = await cdp.policies.listPolicies({
     *            pageSize: 10
     *          });
     *
     *          // Get next page using cursor
     *          const nextPage = await cdp.policies.listPolicies({
     *            pageSize: 10,
     *            pageToken: firstPage.pageToken
     *          });
     *          ```
     */
    async listPolicies(options = {}) {
        return index_js_1.CdpOpenApiClient.listPolicies(options);
    }
    /**
     * Creates a new policy that can be used to govern the behavior of projects and accounts.
     *
     * @param {CreatePolicyOptions} options - Options for creating the policy
     * @param {CreatePolicyBody} options.policy - The policy configuration to create
     * @param {string} [options.policy.description] - Description of the policy's purpose
     * @param {Rule[]} options.policy.rules - Rules that define the policy behavior
     * @param {string} [options.idempotencyKey] - An idempotency key to prevent duplicate policy creation
     *
     * @returns {Promise<Policy>} The created policy
     * @throws {ZodError<typeof CreatePolicyBodySchema>} When the policy is invalid
     *
     * @example **Creating a new policy**
     *          ```ts
     *          const policy = await cdp.policies.createPolicy({
     *            policy: {
     *              scope: "account",
     *              description: "Limits the amount of ETH in transaction",
     *              rules: [
     *                {
     *                  action: "reject",
     *                  operation: "signEvmTransaction",
     *                  criteria: [
     *                    {
     *                      type: "ethValue",
     *                      ethValue: "1000000000000000000",
     *                      operator: ">",
     *                    },
     *                  ],
     *                },
     *              ],
     *            }
     *          });
     *          ```
     *
     * @example **With idempotency key**
     *          ```ts
     *          const idempotencyKey = uuidv4();
     *
     *          // First call creates the policy
     *          const policy = await cdp.policies.createPolicy({
     *            policy: {
     *              scope: "account",
     *              description: "Limits the amount of ETH in transaction",
     *              rules: [
     *                {
     *                  action: "reject",
     *                  operation: "signEvmTransaction",
     *                  criteria: [
     *                    {
     *                      type: "ethValue",
     *                      ethValue: "1000000000000000000",
     *                      operator: ">",
     *                    },
     *                  ],
     *                },
     *              ],
     *            },
     *            idempotencyKey
     *          });
     *
     *          // Second call with same key returns the same policy
     *          const samePolicy = await cdp.policies.createPolicy({
     *            policy: { ... },
     *            idempotencyKey
     *          });
     *          ```
     */
    async createPolicy(options) {
        schema_js_1.CreatePolicyBodySchema.parse(options.policy);
        return index_js_1.CdpOpenApiClient.createPolicy(options.policy, options.idempotencyKey);
    }
    /**
     * Retrieves a policy by its unique identifier.
     *
     * @param {GetPolicyByIdOptions} options - Options containing the policy ID to retrieve
     * @param {string} options.id - The unique identifier of the policy to retrieve
     *
     * @returns {Promise<Policy>} The requested policy
     *
     * @example **Retrieving a policy by ID**
     *          ```ts
     *          const policy = await cdp.policies.getPolicyById({
     *            id: "__ID__"
     *          });
     *
     *          console.log(policy.name);
     *          console.log(policy.rules);
     *          ```
     */
    async getPolicyById(options) {
        return index_js_1.CdpOpenApiClient.getPolicyById(options.id);
    }
    /**
     * Deletes a policy by its unique identifier.
     * If a policy is referenced by an active project or account, this operation will fail.
     *
     * @param {DeletePolicyOptions} options - Options containing the policy ID to delete
     * @param {string} options.id - The unique identifier of the policy to delete
     * @param {string} [options.idempotencyKey] - An idempotency key to prevent duplicate deletion
     *
     * @returns {Promise<void>} Void on successful deletion
     *
     * @example **Deleting a policy**
     *          ```ts
     *          await cdp.policies.deletePolicy({
     *            id: "__ID__"
     *          });
     *          ```
     *
     * @example **With idempotency key**
     *          ```ts
     *          const idempotencyKey = uuidv4();
     *
     *          // This operation is idempotent with the key
     *          await cdp.policies.deletePolicy({
     *            id: "__ID__",
     *            idempotencyKey
     *          });
     *          ```
     */
    async deletePolicy(options) {
        return index_js_1.CdpOpenApiClient.deletePolicy(options.id, options.idempotencyKey);
    }
    /**
     * Updates an existing policy by its unique identifier.
     * This will apply the updated policy to any project or accounts that are currently using it.
     *
     * @param {UpdatePolicyOptions} options - Options containing the policy ID and updated policy data
     * @param {string} options.id - The unique identifier of the policy to update
     * @param {UpdatePolicyBody} options.policy - The updated policy configuration
     * @param {string} [options.policy.description] - Updated description of the policy's purpose
     * @param {Rule[]} [options.policy.rules] - Updated rules that define the policy behavior
     * @param {string} [options.idempotencyKey] - An idempotency key to prevent duplicate updates
     *
     * @returns {Promise<Policy>} The updated policy
     * @throws {ZodError<typeof UpdatePolicyBodySchema>} When the updated policy is invalid
     *
     * @example **Updating a policy**
     *          ```ts
     *          const updatedPolicy = await cdp.policies.updatePolicy({
     *            id: "__ID__",
     *            policy: {
     *              description: "Now with lower transaction limits",
     *              rules: [
     *                {
     *                  action: "reject",
     *                  operation: "signEvmTransaction",
     *                  criteria: [
     *                    {
     *                      type: "ethValue",
     *                      ethValue: "1000000000",
     *                      operator: ">",
     *                    },
     *                  ],
     *                },
     *              ],
     *            },
     *          });
     *          ```
     *
     * @example **With idempotency key**
     *          ```ts
     *          const idempotencyKey = uuidv4();
     *
     *          // This operation is idempotent with the key
     *          await cdp.policies.updatePolicy({
     *            id: "__ID__",
     *            policy: {
     *              description: "Modified Policy",
     *              rules: { ... }
     *            },
     *            idempotencyKey
     *          });
     *          ```
     */
    async updatePolicy(options) {
        schema_js_1.UpdatePolicyBodySchema.parse(options.policy);
        return index_js_1.CdpOpenApiClient.updatePolicy(options.id, options.policy, options.idempotencyKey);
    }
}
exports.PoliciesClient = PoliciesClient;
//# sourceMappingURL=policies.js.map