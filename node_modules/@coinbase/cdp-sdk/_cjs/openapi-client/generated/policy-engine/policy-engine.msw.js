"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPolicyEngineMock = exports.getUpdatePolicyMockHandler = exports.getDeletePolicyMockHandler = exports.getGetPolicyByIdMockHandler = exports.getCreatePolicyMockHandler = exports.getListPoliciesMockHandler = exports.getUpdatePolicyResponseMock = exports.getUpdatePolicyResponseSolAddressCriterionMock = exports.getUpdatePolicyResponseEvmMessageCriterionMock = exports.getUpdatePolicyResponseEvmNetworkCriterionMock = exports.getUpdatePolicyResponseEvmAddressCriterionMock = exports.getUpdatePolicyResponseEthValueCriterionMock = exports.getGetPolicyByIdResponseMock = exports.getGetPolicyByIdResponseSolAddressCriterionMock = exports.getGetPolicyByIdResponseEvmMessageCriterionMock = exports.getGetPolicyByIdResponseEvmNetworkCriterionMock = exports.getGetPolicyByIdResponseEvmAddressCriterionMock = exports.getGetPolicyByIdResponseEthValueCriterionMock = exports.getCreatePolicyResponseMock = exports.getCreatePolicyResponseSolAddressCriterionMock = exports.getCreatePolicyResponseEvmMessageCriterionMock = exports.getCreatePolicyResponseEvmNetworkCriterionMock = exports.getCreatePolicyResponseEvmAddressCriterionMock = exports.getCreatePolicyResponseEthValueCriterionMock = exports.getListPoliciesResponseMock = exports.getListPoliciesResponseSolAddressCriterionMock = exports.getListPoliciesResponseEvmMessageCriterionMock = exports.getListPoliciesResponseEvmNetworkCriterionMock = exports.getListPoliciesResponseEvmAddressCriterionMock = exports.getListPoliciesResponseEthValueCriterionMock = void 0;
/**
 * Generated by orval v7.6.0 🍺
 * Do not edit manually.
 * Coinbase Developer Platform APIs
 * The Coinbase Developer Platform APIs - leading the world's transition onchain.
 * OpenAPI spec version: 2.0.0
 */
const faker_1 = require("@faker-js/faker");
const msw_1 = require("msw");
const getListPoliciesResponseEthValueCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["ethValue"]),
        ethValue: faker_1.faker.helpers.fromRegExp("^[0-9]+$"),
        operator: faker_1.faker.helpers.arrayElement([">", ">=", "<", "<=", "=="]),
    },
    ...overrideResponse,
});
exports.getListPoliciesResponseEthValueCriterionMock = getListPoliciesResponseEthValueCriterionMock;
const getListPoliciesResponseEvmAddressCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["evmAddress"]),
        addresses: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$")),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getListPoliciesResponseEvmAddressCriterionMock = getListPoliciesResponseEvmAddressCriterionMock;
const getListPoliciesResponseEvmNetworkCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["evmNetwork"]),
        networks: faker_1.faker.helpers.arrayElements(["base-sepolia", "base"]),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getListPoliciesResponseEvmNetworkCriterionMock = getListPoliciesResponseEvmNetworkCriterionMock;
const getListPoliciesResponseEvmMessageCriterionMock = (overrideResponse = {}) => ({
    ...{ type: faker_1.faker.helpers.arrayElement(["evmMessage"]), match: faker_1.faker.string.alpha(20) },
    ...overrideResponse,
});
exports.getListPoliciesResponseEvmMessageCriterionMock = getListPoliciesResponseEvmMessageCriterionMock;
const getListPoliciesResponseSolAddressCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["solAddress"]),
        addresses: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[1-9A-HJ-NP-Za-km-z]{32,44}$")),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getListPoliciesResponseSolAddressCriterionMock = getListPoliciesResponseSolAddressCriterionMock;
const getListPoliciesResponseMock = () => ({
    ...{
        policies: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
            id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
            description: faker_1.faker.helpers.arrayElement([
                faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9 ,.]{1,50}$"),
                undefined,
            ]),
            scope: faker_1.faker.helpers.arrayElement(["project", "account"]),
            rules: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                {
                    action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
                    operation: faker_1.faker.helpers.arrayElement(["signEvmTransaction"]),
                    criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                        { ...(0, exports.getListPoliciesResponseEthValueCriterionMock)() },
                        { ...(0, exports.getListPoliciesResponseEvmAddressCriterionMock)() },
                    ])),
                },
                {
                    action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
                    operation: faker_1.faker.helpers.arrayElement(["sendEvmTransaction"]),
                    criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                        { ...(0, exports.getListPoliciesResponseEthValueCriterionMock)() },
                        { ...(0, exports.getListPoliciesResponseEvmAddressCriterionMock)() },
                        { ...(0, exports.getListPoliciesResponseEvmNetworkCriterionMock)() },
                    ])),
                },
                {
                    action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
                    operation: faker_1.faker.helpers.arrayElement(["signEvmMessage"]),
                    criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                        { ...(0, exports.getListPoliciesResponseEvmMessageCriterionMock)() },
                    ])),
                },
                {
                    action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
                    operation: faker_1.faker.helpers.arrayElement(["signSolTransaction"]),
                    criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                        { ...(0, exports.getListPoliciesResponseSolAddressCriterionMock)() },
                    ])),
                },
                {
                    action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
                    operation: faker_1.faker.helpers.arrayElement(["signEvmHash"]),
                },
            ])),
            createdAt: faker_1.faker.string.alpha(20),
            updatedAt: faker_1.faker.string.alpha(20),
        })),
    },
    ...{ nextPageToken: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]) },
});
exports.getListPoliciesResponseMock = getListPoliciesResponseMock;
const getCreatePolicyResponseEthValueCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["ethValue"]),
        ethValue: faker_1.faker.helpers.fromRegExp("^[0-9]+$"),
        operator: faker_1.faker.helpers.arrayElement([">", ">=", "<", "<=", "=="]),
    },
    ...overrideResponse,
});
exports.getCreatePolicyResponseEthValueCriterionMock = getCreatePolicyResponseEthValueCriterionMock;
const getCreatePolicyResponseEvmAddressCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["evmAddress"]),
        addresses: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$")),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getCreatePolicyResponseEvmAddressCriterionMock = getCreatePolicyResponseEvmAddressCriterionMock;
const getCreatePolicyResponseEvmNetworkCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["evmNetwork"]),
        networks: faker_1.faker.helpers.arrayElements(["base-sepolia", "base"]),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getCreatePolicyResponseEvmNetworkCriterionMock = getCreatePolicyResponseEvmNetworkCriterionMock;
const getCreatePolicyResponseEvmMessageCriterionMock = (overrideResponse = {}) => ({
    ...{ type: faker_1.faker.helpers.arrayElement(["evmMessage"]), match: faker_1.faker.string.alpha(20) },
    ...overrideResponse,
});
exports.getCreatePolicyResponseEvmMessageCriterionMock = getCreatePolicyResponseEvmMessageCriterionMock;
const getCreatePolicyResponseSolAddressCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["solAddress"]),
        addresses: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[1-9A-HJ-NP-Za-km-z]{32,44}$")),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getCreatePolicyResponseSolAddressCriterionMock = getCreatePolicyResponseSolAddressCriterionMock;
const getCreatePolicyResponseMock = (overrideResponse = {}) => ({
    id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
    description: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9 ,.]{1,50}$"),
        undefined,
    ]),
    scope: faker_1.faker.helpers.arrayElement(["project", "account"]),
    rules: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signEvmTransaction"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                { ...(0, exports.getCreatePolicyResponseEthValueCriterionMock)() },
                { ...(0, exports.getCreatePolicyResponseEvmAddressCriterionMock)() },
            ])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["sendEvmTransaction"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                { ...(0, exports.getCreatePolicyResponseEthValueCriterionMock)() },
                { ...(0, exports.getCreatePolicyResponseEvmAddressCriterionMock)() },
                { ...(0, exports.getCreatePolicyResponseEvmNetworkCriterionMock)() },
            ])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signEvmMessage"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([{ ...(0, exports.getCreatePolicyResponseEvmMessageCriterionMock)() }])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signSolTransaction"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([{ ...(0, exports.getCreatePolicyResponseSolAddressCriterionMock)() }])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signEvmHash"]),
        },
    ])),
    createdAt: faker_1.faker.string.alpha(20),
    updatedAt: faker_1.faker.string.alpha(20),
    ...overrideResponse,
});
exports.getCreatePolicyResponseMock = getCreatePolicyResponseMock;
const getGetPolicyByIdResponseEthValueCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["ethValue"]),
        ethValue: faker_1.faker.helpers.fromRegExp("^[0-9]+$"),
        operator: faker_1.faker.helpers.arrayElement([">", ">=", "<", "<=", "=="]),
    },
    ...overrideResponse,
});
exports.getGetPolicyByIdResponseEthValueCriterionMock = getGetPolicyByIdResponseEthValueCriterionMock;
const getGetPolicyByIdResponseEvmAddressCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["evmAddress"]),
        addresses: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$")),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getGetPolicyByIdResponseEvmAddressCriterionMock = getGetPolicyByIdResponseEvmAddressCriterionMock;
const getGetPolicyByIdResponseEvmNetworkCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["evmNetwork"]),
        networks: faker_1.faker.helpers.arrayElements(["base-sepolia", "base"]),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getGetPolicyByIdResponseEvmNetworkCriterionMock = getGetPolicyByIdResponseEvmNetworkCriterionMock;
const getGetPolicyByIdResponseEvmMessageCriterionMock = (overrideResponse = {}) => ({
    ...{ type: faker_1.faker.helpers.arrayElement(["evmMessage"]), match: faker_1.faker.string.alpha(20) },
    ...overrideResponse,
});
exports.getGetPolicyByIdResponseEvmMessageCriterionMock = getGetPolicyByIdResponseEvmMessageCriterionMock;
const getGetPolicyByIdResponseSolAddressCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["solAddress"]),
        addresses: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[1-9A-HJ-NP-Za-km-z]{32,44}$")),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getGetPolicyByIdResponseSolAddressCriterionMock = getGetPolicyByIdResponseSolAddressCriterionMock;
const getGetPolicyByIdResponseMock = (overrideResponse = {}) => ({
    id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
    description: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9 ,.]{1,50}$"),
        undefined,
    ]),
    scope: faker_1.faker.helpers.arrayElement(["project", "account"]),
    rules: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signEvmTransaction"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                { ...(0, exports.getGetPolicyByIdResponseEthValueCriterionMock)() },
                { ...(0, exports.getGetPolicyByIdResponseEvmAddressCriterionMock)() },
            ])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["sendEvmTransaction"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                { ...(0, exports.getGetPolicyByIdResponseEthValueCriterionMock)() },
                { ...(0, exports.getGetPolicyByIdResponseEvmAddressCriterionMock)() },
                { ...(0, exports.getGetPolicyByIdResponseEvmNetworkCriterionMock)() },
            ])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signEvmMessage"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([{ ...(0, exports.getGetPolicyByIdResponseEvmMessageCriterionMock)() }])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signSolTransaction"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([{ ...(0, exports.getGetPolicyByIdResponseSolAddressCriterionMock)() }])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signEvmHash"]),
        },
    ])),
    createdAt: faker_1.faker.string.alpha(20),
    updatedAt: faker_1.faker.string.alpha(20),
    ...overrideResponse,
});
exports.getGetPolicyByIdResponseMock = getGetPolicyByIdResponseMock;
const getUpdatePolicyResponseEthValueCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["ethValue"]),
        ethValue: faker_1.faker.helpers.fromRegExp("^[0-9]+$"),
        operator: faker_1.faker.helpers.arrayElement([">", ">=", "<", "<=", "=="]),
    },
    ...overrideResponse,
});
exports.getUpdatePolicyResponseEthValueCriterionMock = getUpdatePolicyResponseEthValueCriterionMock;
const getUpdatePolicyResponseEvmAddressCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["evmAddress"]),
        addresses: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$")),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getUpdatePolicyResponseEvmAddressCriterionMock = getUpdatePolicyResponseEvmAddressCriterionMock;
const getUpdatePolicyResponseEvmNetworkCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["evmNetwork"]),
        networks: faker_1.faker.helpers.arrayElements(["base-sepolia", "base"]),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getUpdatePolicyResponseEvmNetworkCriterionMock = getUpdatePolicyResponseEvmNetworkCriterionMock;
const getUpdatePolicyResponseEvmMessageCriterionMock = (overrideResponse = {}) => ({
    ...{ type: faker_1.faker.helpers.arrayElement(["evmMessage"]), match: faker_1.faker.string.alpha(20) },
    ...overrideResponse,
});
exports.getUpdatePolicyResponseEvmMessageCriterionMock = getUpdatePolicyResponseEvmMessageCriterionMock;
const getUpdatePolicyResponseSolAddressCriterionMock = (overrideResponse = {}) => ({
    ...{
        type: faker_1.faker.helpers.arrayElement(["solAddress"]),
        addresses: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[1-9A-HJ-NP-Za-km-z]{32,44}$")),
        operator: faker_1.faker.helpers.arrayElement(["in", "not in"]),
    },
    ...overrideResponse,
});
exports.getUpdatePolicyResponseSolAddressCriterionMock = getUpdatePolicyResponseSolAddressCriterionMock;
const getUpdatePolicyResponseMock = (overrideResponse = {}) => ({
    id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
    description: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9 ,.]{1,50}$"),
        undefined,
    ]),
    scope: faker_1.faker.helpers.arrayElement(["project", "account"]),
    rules: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signEvmTransaction"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                { ...(0, exports.getUpdatePolicyResponseEthValueCriterionMock)() },
                { ...(0, exports.getUpdatePolicyResponseEvmAddressCriterionMock)() },
            ])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["sendEvmTransaction"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([
                { ...(0, exports.getUpdatePolicyResponseEthValueCriterionMock)() },
                { ...(0, exports.getUpdatePolicyResponseEvmAddressCriterionMock)() },
                { ...(0, exports.getUpdatePolicyResponseEvmNetworkCriterionMock)() },
            ])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signEvmMessage"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([{ ...(0, exports.getUpdatePolicyResponseEvmMessageCriterionMock)() }])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signSolTransaction"]),
            criteria: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.arrayElement([{ ...(0, exports.getUpdatePolicyResponseSolAddressCriterionMock)() }])),
        },
        {
            action: faker_1.faker.helpers.arrayElement(["reject", "accept"]),
            operation: faker_1.faker.helpers.arrayElement(["signEvmHash"]),
        },
    ])),
    createdAt: faker_1.faker.string.alpha(20),
    updatedAt: faker_1.faker.string.alpha(20),
    ...overrideResponse,
});
exports.getUpdatePolicyResponseMock = getUpdatePolicyResponseMock;
const getListPoliciesMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/policy-engine/policies", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getListPoliciesResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getListPoliciesMockHandler = getListPoliciesMockHandler;
const getCreatePolicyMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/policy-engine/policies", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getCreatePolicyResponseMock)()), { status: 201, headers: { "Content-Type": "application/json" } });
    });
};
exports.getCreatePolicyMockHandler = getCreatePolicyMockHandler;
const getGetPolicyByIdMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/policy-engine/policies/:policyId", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetPolicyByIdResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetPolicyByIdMockHandler = getGetPolicyByIdMockHandler;
const getDeletePolicyMockHandler = (overrideResponse) => {
    return msw_1.http.delete("*/v2/policy-engine/policies/:policyId", async (info) => {
        await (0, msw_1.delay)(0);
        if (typeof overrideResponse === "function") {
            await overrideResponse(info);
        }
        return new msw_1.HttpResponse(null, { status: 204 });
    });
};
exports.getDeletePolicyMockHandler = getDeletePolicyMockHandler;
const getUpdatePolicyMockHandler = (overrideResponse) => {
    return msw_1.http.put("*/v2/policy-engine/policies/:policyId", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getUpdatePolicyResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getUpdatePolicyMockHandler = getUpdatePolicyMockHandler;
const getPolicyEngineMock = () => [
    (0, exports.getListPoliciesMockHandler)(),
    (0, exports.getCreatePolicyMockHandler)(),
    (0, exports.getGetPolicyByIdMockHandler)(),
    (0, exports.getDeletePolicyMockHandler)(),
    (0, exports.getUpdatePolicyMockHandler)(),
];
exports.getPolicyEngineMock = getPolicyEngineMock;
//# sourceMappingURL=policy-engine.msw.js.map