"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSolanaAccountsMock = exports.getSignSolanaMessageMockHandler = exports.getSignSolanaTransactionMockHandler = exports.getExportSolanaAccountByNameMockHandler = exports.getExportSolanaAccountMockHandler = exports.getGetSolanaAccountByNameMockHandler = exports.getUpdateSolanaAccountMockHandler = exports.getGetSolanaAccountMockHandler = exports.getCreateSolanaAccountMockHandler = exports.getListSolanaAccountsMockHandler = exports.getSignSolanaMessageResponseMock = exports.getSignSolanaTransactionResponseMock = exports.getExportSolanaAccountByNameResponseMock = exports.getExportSolanaAccountResponseMock = exports.getGetSolanaAccountByNameResponseMock = exports.getUpdateSolanaAccountResponseMock = exports.getGetSolanaAccountResponseMock = exports.getCreateSolanaAccountResponseMock = exports.getListSolanaAccountsResponseMock = void 0;
/**
 * Generated by orval v7.6.0 🍺
 * Do not edit manually.
 * Coinbase Developer Platform APIs
 * The Coinbase Developer Platform APIs - leading the world's transition onchain.
 * OpenAPI spec version: 2.0.0
 */
const faker_1 = require("@faker-js/faker");
const msw_1 = require("msw");
const getListSolanaAccountsResponseMock = () => ({
    ...{
        accounts: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
            address: faker_1.faker.helpers.fromRegExp("^[1-9A-HJ-NP-Za-km-z]{32,44}$"),
            name: faker_1.faker.helpers.arrayElement([
                faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
                undefined,
            ]),
            policies: faker_1.faker.helpers.arrayElement([
                Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
                undefined,
            ]),
        })),
    },
    ...{ nextPageToken: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]) },
});
exports.getListSolanaAccountsResponseMock = getListSolanaAccountsResponseMock;
const getCreateSolanaAccountResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^[1-9A-HJ-NP-Za-km-z]{32,44}$"),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    policies: faker_1.faker.helpers.arrayElement([
        Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getCreateSolanaAccountResponseMock = getCreateSolanaAccountResponseMock;
const getGetSolanaAccountResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^[1-9A-HJ-NP-Za-km-z]{32,44}$"),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    policies: faker_1.faker.helpers.arrayElement([
        Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getGetSolanaAccountResponseMock = getGetSolanaAccountResponseMock;
const getUpdateSolanaAccountResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^[1-9A-HJ-NP-Za-km-z]{32,44}$"),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    policies: faker_1.faker.helpers.arrayElement([
        Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getUpdateSolanaAccountResponseMock = getUpdateSolanaAccountResponseMock;
const getGetSolanaAccountByNameResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^[1-9A-HJ-NP-Za-km-z]{32,44}$"),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    policies: faker_1.faker.helpers.arrayElement([
        Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getGetSolanaAccountByNameResponseMock = getGetSolanaAccountByNameResponseMock;
const getExportSolanaAccountResponseMock = (overrideResponse = {}) => ({ encryptedPrivateKey: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getExportSolanaAccountResponseMock = getExportSolanaAccountResponseMock;
const getExportSolanaAccountByNameResponseMock = (overrideResponse = {}) => ({
    encryptedPrivateKey: faker_1.faker.string.alpha(20),
    ...overrideResponse,
});
exports.getExportSolanaAccountByNameResponseMock = getExportSolanaAccountByNameResponseMock;
const getSignSolanaTransactionResponseMock = (overrideResponse = {}) => ({ signedTransaction: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getSignSolanaTransactionResponseMock = getSignSolanaTransactionResponseMock;
const getSignSolanaMessageResponseMock = (overrideResponse = {}) => ({ signature: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getSignSolanaMessageResponseMock = getSignSolanaMessageResponseMock;
const getListSolanaAccountsMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/solana/accounts", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getListSolanaAccountsResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getListSolanaAccountsMockHandler = getListSolanaAccountsMockHandler;
const getCreateSolanaAccountMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/solana/accounts", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getCreateSolanaAccountResponseMock)()), { status: 201, headers: { "Content-Type": "application/json" } });
    });
};
exports.getCreateSolanaAccountMockHandler = getCreateSolanaAccountMockHandler;
const getGetSolanaAccountMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/solana/accounts/:address", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetSolanaAccountResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetSolanaAccountMockHandler = getGetSolanaAccountMockHandler;
const getUpdateSolanaAccountMockHandler = (overrideResponse) => {
    return msw_1.http.put("*/v2/solana/accounts/:address", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getUpdateSolanaAccountResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getUpdateSolanaAccountMockHandler = getUpdateSolanaAccountMockHandler;
const getGetSolanaAccountByNameMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/solana/accounts/by-name/:name", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetSolanaAccountByNameResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetSolanaAccountByNameMockHandler = getGetSolanaAccountByNameMockHandler;
const getExportSolanaAccountMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/solana/accounts/:address/export", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getExportSolanaAccountResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getExportSolanaAccountMockHandler = getExportSolanaAccountMockHandler;
const getExportSolanaAccountByNameMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/solana/accounts/export/by-name/:name", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getExportSolanaAccountByNameResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getExportSolanaAccountByNameMockHandler = getExportSolanaAccountByNameMockHandler;
const getSignSolanaTransactionMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/solana/accounts/:address/sign/transaction", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getSignSolanaTransactionResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getSignSolanaTransactionMockHandler = getSignSolanaTransactionMockHandler;
const getSignSolanaMessageMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/solana/accounts/:address/sign/message", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getSignSolanaMessageResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getSignSolanaMessageMockHandler = getSignSolanaMessageMockHandler;
const getSolanaAccountsMock = () => [
    (0, exports.getListSolanaAccountsMockHandler)(),
    (0, exports.getCreateSolanaAccountMockHandler)(),
    (0, exports.getGetSolanaAccountMockHandler)(),
    (0, exports.getUpdateSolanaAccountMockHandler)(),
    (0, exports.getGetSolanaAccountByNameMockHandler)(),
    (0, exports.getExportSolanaAccountMockHandler)(),
    (0, exports.getExportSolanaAccountByNameMockHandler)(),
    (0, exports.getSignSolanaTransactionMockHandler)(),
    (0, exports.getSignSolanaMessageMockHandler)(),
];
exports.getSolanaAccountsMock = getSolanaAccountsMock;
//# sourceMappingURL=solana-accounts.msw.js.map