"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPaymentsAlphaMock = exports.getGetPaymentTransferMockHandler = exports.getExecutePaymentTransferQuoteMockHandler = exports.getCreatePaymentTransferQuoteMockHandler = exports.getGetCryptoRailsMockHandler = exports.getGetPaymentMethodsMockHandler = exports.getGetPaymentTransferResponseMock = exports.getGetPaymentTransferResponseCryptoRailAddressMock = exports.getGetPaymentTransferResponsePaymentMethodRequestMock = exports.getExecutePaymentTransferQuoteResponseMock = exports.getExecutePaymentTransferQuoteResponseCryptoRailAddressMock = exports.getExecutePaymentTransferQuoteResponsePaymentMethodRequestMock = exports.getCreatePaymentTransferQuoteResponseMock = exports.getCreatePaymentTransferQuoteResponseCryptoRailAddressMock = exports.getCreatePaymentTransferQuoteResponsePaymentMethodRequestMock = exports.getGetCryptoRailsResponseMock = exports.getGetPaymentMethodsResponseMock = void 0;
/**
 * Generated by orval v7.6.0 🍺
 * Do not edit manually.
 * Coinbase Developer Platform APIs
 * The Coinbase Developer Platform APIs - leading the world's transition onchain.
 * OpenAPI spec version: 2.0.0
 */
const faker_1 = require("@faker-js/faker");
const msw_1 = require("msw");
const coinbaseDeveloperPlatformAPIs_schemas_js_1 = require("../coinbaseDeveloperPlatformAPIs.schemas.js");
const getGetPaymentMethodsResponseMock = () => Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
    id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
    type: faker_1.faker.helpers.arrayElement(["card", "fiat_account"]),
    currency: faker_1.faker.string.alpha(20),
    actions: faker_1.faker.helpers.arrayElements(Object.values(coinbaseDeveloperPlatformAPIs_schemas_js_1.PaymentRailAction)),
    limits: faker_1.faker.helpers.arrayElement([
        {
            sourceLimit: faker_1.faker.helpers.arrayElement([
                {
                    amount: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
                    currency: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
                },
                undefined,
            ]),
            targetLimit: faker_1.faker.helpers.arrayElement([
                {
                    amount: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
                    currency: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
                },
                undefined,
            ]),
        },
        undefined,
    ]),
}));
exports.getGetPaymentMethodsResponseMock = getGetPaymentMethodsResponseMock;
const getGetCryptoRailsResponseMock = () => Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
    currency: faker_1.faker.string.alpha(20),
    name: faker_1.faker.string.alpha(20),
    networks: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
        name: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
        chainId: faker_1.faker.helpers.arrayElement([
            faker_1.faker.number.int({ min: undefined, max: undefined }),
            undefined,
        ]),
        contractAddress: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
    })),
    actions: faker_1.faker.helpers.arrayElements(Object.values(coinbaseDeveloperPlatformAPIs_schemas_js_1.PaymentRailAction)),
}));
exports.getGetCryptoRailsResponseMock = getGetCryptoRailsResponseMock;
const getCreatePaymentTransferQuoteResponsePaymentMethodRequestMock = (overrideResponse = {}) => ({
    ...{
        id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
    },
    ...overrideResponse,
});
exports.getCreatePaymentTransferQuoteResponsePaymentMethodRequestMock = getCreatePaymentTransferQuoteResponsePaymentMethodRequestMock;
const getCreatePaymentTransferQuoteResponseCryptoRailAddressMock = (overrideResponse = {}) => ({
    ...{
        currency: faker_1.faker.string.alpha(20),
        network: faker_1.faker.string.alpha(20),
        address: faker_1.faker.string.alpha(20),
    },
    ...overrideResponse,
});
exports.getCreatePaymentTransferQuoteResponseCryptoRailAddressMock = getCreatePaymentTransferQuoteResponseCryptoRailAddressMock;
const getCreatePaymentTransferQuoteResponseMock = (overrideResponse = {}) => ({
    transfer: {
        id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
        sourceType: faker_1.faker.helpers.arrayElement(["payment_method"]),
        source: faker_1.faker.helpers.arrayElement([
            { ...(0, exports.getCreatePaymentTransferQuoteResponsePaymentMethodRequestMock)() },
        ]),
        targetType: faker_1.faker.helpers.arrayElement(["crypto_rail"]),
        target: faker_1.faker.helpers.arrayElement([
            { ...(0, exports.getCreatePaymentTransferQuoteResponseCryptoRailAddressMock)() },
        ]),
        sourceAmount: faker_1.faker.string.alpha(20),
        sourceCurrency: faker_1.faker.string.alpha(20),
        targetAmount: faker_1.faker.string.alpha(20),
        targetCurrency: faker_1.faker.string.alpha(20),
        userAmount: faker_1.faker.string.alpha(20),
        userCurrency: faker_1.faker.string.alpha(20),
        fees: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
            type: faker_1.faker.helpers.arrayElement(["exchange_fee", "network_fee"]),
            amount: faker_1.faker.string.alpha(20),
            currency: faker_1.faker.string.alpha(20),
            description: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
        })),
        status: faker_1.faker.helpers.arrayElement([
            "created",
            "pending",
            "started",
            "completed",
            "failed",
        ]),
        createdAt: faker_1.faker.string.alpha(20),
        updatedAt: faker_1.faker.string.alpha(20),
        transactionHash: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
    },
    ...overrideResponse,
});
exports.getCreatePaymentTransferQuoteResponseMock = getCreatePaymentTransferQuoteResponseMock;
const getExecutePaymentTransferQuoteResponsePaymentMethodRequestMock = (overrideResponse = {}) => ({
    ...{
        id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
    },
    ...overrideResponse,
});
exports.getExecutePaymentTransferQuoteResponsePaymentMethodRequestMock = getExecutePaymentTransferQuoteResponsePaymentMethodRequestMock;
const getExecutePaymentTransferQuoteResponseCryptoRailAddressMock = (overrideResponse = {}) => ({
    ...{
        currency: faker_1.faker.string.alpha(20),
        network: faker_1.faker.string.alpha(20),
        address: faker_1.faker.string.alpha(20),
    },
    ...overrideResponse,
});
exports.getExecutePaymentTransferQuoteResponseCryptoRailAddressMock = getExecutePaymentTransferQuoteResponseCryptoRailAddressMock;
const getExecutePaymentTransferQuoteResponseMock = (overrideResponse = {}) => ({
    id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
    sourceType: faker_1.faker.helpers.arrayElement(["payment_method"]),
    source: faker_1.faker.helpers.arrayElement([
        { ...(0, exports.getExecutePaymentTransferQuoteResponsePaymentMethodRequestMock)() },
    ]),
    targetType: faker_1.faker.helpers.arrayElement(["crypto_rail"]),
    target: faker_1.faker.helpers.arrayElement([
        { ...(0, exports.getExecutePaymentTransferQuoteResponseCryptoRailAddressMock)() },
    ]),
    sourceAmount: faker_1.faker.string.alpha(20),
    sourceCurrency: faker_1.faker.string.alpha(20),
    targetAmount: faker_1.faker.string.alpha(20),
    targetCurrency: faker_1.faker.string.alpha(20),
    userAmount: faker_1.faker.string.alpha(20),
    userCurrency: faker_1.faker.string.alpha(20),
    fees: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
        type: faker_1.faker.helpers.arrayElement(["exchange_fee", "network_fee"]),
        amount: faker_1.faker.string.alpha(20),
        currency: faker_1.faker.string.alpha(20),
        description: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
    })),
    status: faker_1.faker.helpers.arrayElement([
        "created",
        "pending",
        "started",
        "completed",
        "failed",
    ]),
    createdAt: faker_1.faker.string.alpha(20),
    updatedAt: faker_1.faker.string.alpha(20),
    transactionHash: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
    ...overrideResponse,
});
exports.getExecutePaymentTransferQuoteResponseMock = getExecutePaymentTransferQuoteResponseMock;
const getGetPaymentTransferResponsePaymentMethodRequestMock = (overrideResponse = {}) => ({
    ...{
        id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
    },
    ...overrideResponse,
});
exports.getGetPaymentTransferResponsePaymentMethodRequestMock = getGetPaymentTransferResponsePaymentMethodRequestMock;
const getGetPaymentTransferResponseCryptoRailAddressMock = (overrideResponse = {}) => ({
    ...{
        currency: faker_1.faker.string.alpha(20),
        network: faker_1.faker.string.alpha(20),
        address: faker_1.faker.string.alpha(20),
    },
    ...overrideResponse,
});
exports.getGetPaymentTransferResponseCryptoRailAddressMock = getGetPaymentTransferResponseCryptoRailAddressMock;
const getGetPaymentTransferResponseMock = (overrideResponse = {}) => ({
    id: faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"),
    sourceType: faker_1.faker.helpers.arrayElement(["payment_method"]),
    source: faker_1.faker.helpers.arrayElement([
        { ...(0, exports.getGetPaymentTransferResponsePaymentMethodRequestMock)() },
    ]),
    targetType: faker_1.faker.helpers.arrayElement(["crypto_rail"]),
    target: faker_1.faker.helpers.arrayElement([{ ...(0, exports.getGetPaymentTransferResponseCryptoRailAddressMock)() }]),
    sourceAmount: faker_1.faker.string.alpha(20),
    sourceCurrency: faker_1.faker.string.alpha(20),
    targetAmount: faker_1.faker.string.alpha(20),
    targetCurrency: faker_1.faker.string.alpha(20),
    userAmount: faker_1.faker.string.alpha(20),
    userCurrency: faker_1.faker.string.alpha(20),
    fees: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
        type: faker_1.faker.helpers.arrayElement(["exchange_fee", "network_fee"]),
        amount: faker_1.faker.string.alpha(20),
        currency: faker_1.faker.string.alpha(20),
        description: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
    })),
    status: faker_1.faker.helpers.arrayElement([
        "created",
        "pending",
        "started",
        "completed",
        "failed",
    ]),
    createdAt: faker_1.faker.string.alpha(20),
    updatedAt: faker_1.faker.string.alpha(20),
    transactionHash: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
    ...overrideResponse,
});
exports.getGetPaymentTransferResponseMock = getGetPaymentTransferResponseMock;
const getGetPaymentMethodsMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/payments/rails/payment-methods", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetPaymentMethodsResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetPaymentMethodsMockHandler = getGetPaymentMethodsMockHandler;
const getGetCryptoRailsMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/payments/rails/crypto", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetCryptoRailsResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetCryptoRailsMockHandler = getGetCryptoRailsMockHandler;
const getCreatePaymentTransferQuoteMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/payments/transfers", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getCreatePaymentTransferQuoteResponseMock)()), { status: 201, headers: { "Content-Type": "application/json" } });
    });
};
exports.getCreatePaymentTransferQuoteMockHandler = getCreatePaymentTransferQuoteMockHandler;
const getExecutePaymentTransferQuoteMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/payments/transfers/:transferId/execute", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getExecutePaymentTransferQuoteResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getExecutePaymentTransferQuoteMockHandler = getExecutePaymentTransferQuoteMockHandler;
const getGetPaymentTransferMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/payments/transfers/:transferId", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetPaymentTransferResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetPaymentTransferMockHandler = getGetPaymentTransferMockHandler;
const getPaymentsAlphaMock = () => [
    (0, exports.getGetPaymentMethodsMockHandler)(),
    (0, exports.getGetCryptoRailsMockHandler)(),
    (0, exports.getCreatePaymentTransferQuoteMockHandler)(),
    (0, exports.getExecutePaymentTransferQuoteMockHandler)(),
    (0, exports.getGetPaymentTransferMockHandler)(),
];
exports.getPaymentsAlphaMock = getPaymentsAlphaMock;
//# sourceMappingURL=payments-alpha.msw.js.map