"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePaymentTransferQuoteBodyTargetType = exports.CreatePaymentTransferQuoteBodySourceType = exports.RequestSolanaFaucetBodyToken = exports.CreatePolicyBodyScope = exports.ListPoliciesScope = exports.RequestEvmFaucetBodyToken = exports.RequestEvmFaucetBodyNetwork = exports.PrepareUserOperationBodyNetwork = exports.SendEvmTransactionBodyNetwork = exports.TransferStatus = exports.TransferTargetType = exports.TransferSourceType = exports.FeeType = exports.PaymentMethodType = exports.PaymentRailAction = exports.PolicyScope = exports.SignEvmHashRuleOperation = exports.SignEvmHashRuleAction = exports.SignSolTransactionRuleOperation = exports.SignSolTransactionRuleAction = exports.SolAddressCriterionOperator = exports.SolAddressCriterionType = exports.SignEvmMessageRuleOperation = exports.SignEvmMessageRuleAction = exports.EvmMessageCriterionType = exports.SendEvmTransactionRuleOperation = exports.SendEvmTransactionRuleAction = exports.EvmNetworkCriterionOperator = exports.EvmNetworkCriterionNetworksItem = exports.EvmNetworkCriterionType = exports.SignEvmTransactionRuleOperation = exports.SignEvmTransactionRuleAction = exports.EvmAddressCriterionOperator = exports.EvmAddressCriterionType = exports.EthValueCriterionOperator = exports.EthValueCriterionType = exports.ListEvmTokenBalancesNetwork = exports.EvmSwapsNetwork = exports.EvmUserOperationStatus = exports.EvmUserOperationNetwork = exports.ErrorType = void 0;
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.ErrorType = {
    already_exists: "already_exists",
    bad_gateway: "bad_gateway",
    faucet_limit_exceeded: "faucet_limit_exceeded",
    forbidden: "forbidden",
    idempotency_error: "idempotency_error",
    internal_server_error: "internal_server_error",
    invalid_request: "invalid_request",
    invalid_signature: "invalid_signature",
    malformed_transaction: "malformed_transaction",
    not_found: "not_found",
    rate_limit_exceeded: "rate_limit_exceeded",
    request_canceled: "request_canceled",
    service_unavailable: "service_unavailable",
    timed_out: "timed_out",
    unauthorized: "unauthorized",
    policy_violation: "policy_violation",
    policy_in_use: "policy_in_use",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EvmUserOperationNetwork = {
    "base-sepolia": "base-sepolia",
    base: "base",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EvmUserOperationStatus = {
    pending: "pending",
    signed: "signed",
    broadcast: "broadcast",
    complete: "complete",
    failed: "failed",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EvmSwapsNetwork = {
    base: "base",
    ethereum: "ethereum",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.ListEvmTokenBalancesNetwork = {
    base: "base",
    "base-sepolia": "base-sepolia",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EthValueCriterionType = {
    ethValue: "ethValue",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EthValueCriterionOperator = {
    ">": ">",
    ">=": ">=",
    "<": "<",
    "<=": "<=",
    "==": "==",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EvmAddressCriterionType = {
    evmAddress: "evmAddress",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EvmAddressCriterionOperator = {
    in: "in",
    not_in: "not in",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SignEvmTransactionRuleAction = {
    reject: "reject",
    accept: "accept",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SignEvmTransactionRuleOperation = {
    signEvmTransaction: "signEvmTransaction",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EvmNetworkCriterionType = {
    evmNetwork: "evmNetwork",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EvmNetworkCriterionNetworksItem = {
    "base-sepolia": "base-sepolia",
    base: "base",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EvmNetworkCriterionOperator = {
    in: "in",
    not_in: "not in",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SendEvmTransactionRuleAction = {
    reject: "reject",
    accept: "accept",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SendEvmTransactionRuleOperation = {
    sendEvmTransaction: "sendEvmTransaction",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.EvmMessageCriterionType = {
    evmMessage: "evmMessage",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SignEvmMessageRuleAction = {
    reject: "reject",
    accept: "accept",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SignEvmMessageRuleOperation = {
    signEvmMessage: "signEvmMessage",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SolAddressCriterionType = {
    solAddress: "solAddress",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SolAddressCriterionOperator = {
    in: "in",
    not_in: "not in",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SignSolTransactionRuleAction = {
    reject: "reject",
    accept: "accept",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SignSolTransactionRuleOperation = {
    signSolTransaction: "signSolTransaction",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SignEvmHashRuleAction = {
    reject: "reject",
    accept: "accept",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SignEvmHashRuleOperation = {
    signEvmHash: "signEvmHash",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.PolicyScope = {
    project: "project",
    account: "account",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.PaymentRailAction = {
    source: "source",
    target: "target",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.PaymentMethodType = {
    card: "card",
    fiat_account: "fiat_account",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.FeeType = {
    exchange_fee: "exchange_fee",
    network_fee: "network_fee",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.TransferSourceType = {
    payment_method: "payment_method",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.TransferTargetType = {
    crypto_rail: "crypto_rail",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.TransferStatus = {
    created: "created",
    pending: "pending",
    started: "started",
    completed: "completed",
    failed: "failed",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.SendEvmTransactionBodyNetwork = {
    base: "base",
    "base-sepolia": "base-sepolia",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.PrepareUserOperationBodyNetwork = {
    "base-sepolia": "base-sepolia",
    base: "base",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.RequestEvmFaucetBodyNetwork = {
    "base-sepolia": "base-sepolia",
    "ethereum-sepolia": "ethereum-sepolia",
    "ethereum-hoodi": "ethereum-hoodi",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.RequestEvmFaucetBodyToken = {
    eth: "eth",
    usdc: "usdc",
    eurc: "eurc",
    cbbtc: "cbbtc",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.ListPoliciesScope = {
    project: "project",
    account: "account",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.CreatePolicyBodyScope = {
    project: "project",
    account: "account",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.RequestSolanaFaucetBodyToken = {
    sol: "sol",
    usdc: "usdc",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.CreatePaymentTransferQuoteBodySourceType = {
    payment_method: "payment_method",
};
// eslint-disable-next-line @typescript-eslint/no-redeclare
exports.CreatePaymentTransferQuoteBodyTargetType = {
    crypto_rail: "crypto_rail",
};
//# sourceMappingURL=coinbaseDeveloperPlatformAPIs.schemas.js.map