"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEvmAccountsMock = exports.getExportEvmAccountByNameMockHandler = exports.getExportEvmAccountMockHandler = exports.getImportEvmAccountMockHandler = exports.getSignEvmTypedDataMockHandler = exports.getSignEvmMessageMockHandler = exports.getSignEvmHashMockHandler = exports.getSignEvmTransactionMockHandler = exports.getSendEvmTransactionMockHandler = exports.getGetEvmAccountByNameMockHandler = exports.getUpdateEvmAccountMockHandler = exports.getGetEvmAccountMockHandler = exports.getCreateEvmAccountMockHandler = exports.getListEvmAccountsMockHandler = exports.getExportEvmAccountByNameResponseMock = exports.getExportEvmAccountResponseMock = exports.getImportEvmAccountResponseMock = exports.getSignEvmTypedDataResponseMock = exports.getSignEvmMessageResponseMock = exports.getSignEvmHashResponseMock = exports.getSignEvmTransactionResponseMock = exports.getSendEvmTransactionResponseMock = exports.getGetEvmAccountByNameResponseMock = exports.getUpdateEvmAccountResponseMock = exports.getGetEvmAccountResponseMock = exports.getCreateEvmAccountResponseMock = exports.getListEvmAccountsResponseMock = void 0;
/**
 * Generated by orval v7.6.0 🍺
 * Do not edit manually.
 * Coinbase Developer Platform APIs
 * The Coinbase Developer Platform APIs - leading the world's transition onchain.
 * OpenAPI spec version: 2.0.0
 */
const faker_1 = require("@faker-js/faker");
const msw_1 = require("msw");
const getListEvmAccountsResponseMock = () => ({
    ...{
        accounts: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
            address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
            name: faker_1.faker.helpers.arrayElement([
                faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
                undefined,
            ]),
            policies: faker_1.faker.helpers.arrayElement([
                Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
                undefined,
            ]),
        })),
    },
    ...{ nextPageToken: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]) },
});
exports.getListEvmAccountsResponseMock = getListEvmAccountsResponseMock;
const getCreateEvmAccountResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    policies: faker_1.faker.helpers.arrayElement([
        Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getCreateEvmAccountResponseMock = getCreateEvmAccountResponseMock;
const getGetEvmAccountResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    policies: faker_1.faker.helpers.arrayElement([
        Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getGetEvmAccountResponseMock = getGetEvmAccountResponseMock;
const getUpdateEvmAccountResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    policies: faker_1.faker.helpers.arrayElement([
        Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getUpdateEvmAccountResponseMock = getUpdateEvmAccountResponseMock;
const getGetEvmAccountByNameResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    policies: faker_1.faker.helpers.arrayElement([
        Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getGetEvmAccountByNameResponseMock = getGetEvmAccountByNameResponseMock;
const getSendEvmTransactionResponseMock = (overrideResponse = {}) => ({ transactionHash: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getSendEvmTransactionResponseMock = getSendEvmTransactionResponseMock;
const getSignEvmTransactionResponseMock = (overrideResponse = {}) => ({ signedTransaction: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getSignEvmTransactionResponseMock = getSignEvmTransactionResponseMock;
const getSignEvmHashResponseMock = (overrideResponse = {}) => ({ signature: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getSignEvmHashResponseMock = getSignEvmHashResponseMock;
const getSignEvmMessageResponseMock = (overrideResponse = {}) => ({ signature: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getSignEvmMessageResponseMock = getSignEvmMessageResponseMock;
const getSignEvmTypedDataResponseMock = (overrideResponse = {}) => ({ signature: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getSignEvmTypedDataResponseMock = getSignEvmTypedDataResponseMock;
const getImportEvmAccountResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    policies: faker_1.faker.helpers.arrayElement([
        Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getImportEvmAccountResponseMock = getImportEvmAccountResponseMock;
const getExportEvmAccountResponseMock = (overrideResponse = {}) => ({ encryptedPrivateKey: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getExportEvmAccountResponseMock = getExportEvmAccountResponseMock;
const getExportEvmAccountByNameResponseMock = (overrideResponse = {}) => ({
    encryptedPrivateKey: faker_1.faker.string.alpha(20),
    ...overrideResponse,
});
exports.getExportEvmAccountByNameResponseMock = getExportEvmAccountByNameResponseMock;
const getListEvmAccountsMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/evm/accounts", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getListEvmAccountsResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getListEvmAccountsMockHandler = getListEvmAccountsMockHandler;
const getCreateEvmAccountMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/accounts", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getCreateEvmAccountResponseMock)()), { status: 201, headers: { "Content-Type": "application/json" } });
    });
};
exports.getCreateEvmAccountMockHandler = getCreateEvmAccountMockHandler;
const getGetEvmAccountMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/evm/accounts/:address", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetEvmAccountResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetEvmAccountMockHandler = getGetEvmAccountMockHandler;
const getUpdateEvmAccountMockHandler = (overrideResponse) => {
    return msw_1.http.put("*/v2/evm/accounts/:address", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getUpdateEvmAccountResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getUpdateEvmAccountMockHandler = getUpdateEvmAccountMockHandler;
const getGetEvmAccountByNameMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/evm/accounts/by-name/:name", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetEvmAccountByNameResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetEvmAccountByNameMockHandler = getGetEvmAccountByNameMockHandler;
const getSendEvmTransactionMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/accounts/:address/send/transaction", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getSendEvmTransactionResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getSendEvmTransactionMockHandler = getSendEvmTransactionMockHandler;
const getSignEvmTransactionMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/accounts/:address/sign/transaction", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getSignEvmTransactionResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getSignEvmTransactionMockHandler = getSignEvmTransactionMockHandler;
const getSignEvmHashMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/accounts/:address/sign", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getSignEvmHashResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getSignEvmHashMockHandler = getSignEvmHashMockHandler;
const getSignEvmMessageMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/accounts/:address/sign/message", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getSignEvmMessageResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getSignEvmMessageMockHandler = getSignEvmMessageMockHandler;
const getSignEvmTypedDataMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/accounts/:address/sign/typed-data", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getSignEvmTypedDataResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getSignEvmTypedDataMockHandler = getSignEvmTypedDataMockHandler;
const getImportEvmAccountMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/accounts/import", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getImportEvmAccountResponseMock)()), { status: 201, headers: { "Content-Type": "application/json" } });
    });
};
exports.getImportEvmAccountMockHandler = getImportEvmAccountMockHandler;
const getExportEvmAccountMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/accounts/:address/export", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getExportEvmAccountResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getExportEvmAccountMockHandler = getExportEvmAccountMockHandler;
const getExportEvmAccountByNameMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/accounts/export/by-name/:name", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getExportEvmAccountByNameResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getExportEvmAccountByNameMockHandler = getExportEvmAccountByNameMockHandler;
const getEvmAccountsMock = () => [
    (0, exports.getListEvmAccountsMockHandler)(),
    (0, exports.getCreateEvmAccountMockHandler)(),
    (0, exports.getGetEvmAccountMockHandler)(),
    (0, exports.getUpdateEvmAccountMockHandler)(),
    (0, exports.getGetEvmAccountByNameMockHandler)(),
    (0, exports.getSendEvmTransactionMockHandler)(),
    (0, exports.getSignEvmTransactionMockHandler)(),
    (0, exports.getSignEvmHashMockHandler)(),
    (0, exports.getSignEvmMessageMockHandler)(),
    (0, exports.getSignEvmTypedDataMockHandler)(),
    (0, exports.getImportEvmAccountMockHandler)(),
    (0, exports.getExportEvmAccountMockHandler)(),
    (0, exports.getExportEvmAccountByNameMockHandler)(),
];
exports.getEvmAccountsMock = getEvmAccountsMock;
//# sourceMappingURL=evm-accounts.msw.js.map