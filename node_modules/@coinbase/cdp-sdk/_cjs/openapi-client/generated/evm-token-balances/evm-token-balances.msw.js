"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEvmTokenBalancesMock = exports.getListEvmTokenBalancesMockHandler = exports.getListEvmTokenBalancesResponseMock = void 0;
/**
 * Generated by orval v7.6.0 🍺
 * Do not edit manually.
 * Coinbase Developer Platform APIs
 * The Coinbase Developer Platform APIs - leading the world's transition onchain.
 * OpenAPI spec version: 2.0.0
 */
const faker_1 = require("@faker-js/faker");
const msw_1 = require("msw");
const coinbaseDeveloperPlatformAPIs_schemas_js_1 = require("../coinbaseDeveloperPlatformAPIs.schemas.js");
const getListEvmTokenBalancesResponseMock = () => ({
    ...{
        balances: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
            amount: {
                amount: faker_1.faker.helpers.fromRegExp("^[0-9]+$"),
                decimals: faker_1.faker.number.int({ min: undefined, max: undefined }),
            },
            token: {
                network: faker_1.faker.helpers.arrayElement(Object.values(coinbaseDeveloperPlatformAPIs_schemas_js_1.ListEvmTokenBalancesNetwork)),
                symbol: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
                name: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
                contractAddress: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
            },
        })),
    },
    ...{ nextPageToken: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]) },
});
exports.getListEvmTokenBalancesResponseMock = getListEvmTokenBalancesResponseMock;
const getListEvmTokenBalancesMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/evm/token-balances/:network/:address", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getListEvmTokenBalancesResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getListEvmTokenBalancesMockHandler = getListEvmTokenBalancesMockHandler;
const getEvmTokenBalancesMock = () => [(0, exports.getListEvmTokenBalancesMockHandler)()];
exports.getEvmTokenBalancesMock = getEvmTokenBalancesMock;
//# sourceMappingURL=evm-token-balances.msw.js.map