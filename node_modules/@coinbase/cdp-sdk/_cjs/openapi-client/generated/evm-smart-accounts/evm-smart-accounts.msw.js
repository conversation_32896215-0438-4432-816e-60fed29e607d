"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEvmSmartAccountsMock = exports.getSendUserOperationMockHandler = exports.getGetUserOperationMockHandler = exports.getPrepareUserOperationMockHandler = exports.getGetEvmSmartAccountMockHandler = exports.getGetEvmSmartAccountByNameMockHandler = exports.getCreateEvmSmartAccountMockHandler = exports.getListEvmSmartAccountsMockHandler = exports.getSendUserOperationResponseMock = exports.getGetUserOperationResponseMock = exports.getPrepareUserOperationResponseMock = exports.getGetEvmSmartAccountResponseMock = exports.getGetEvmSmartAccountByNameResponseMock = exports.getCreateEvmSmartAccountResponseMock = exports.getListEvmSmartAccountsResponseMock = void 0;
/**
 * Generated by orval v7.6.0 🍺
 * Do not edit manually.
 * Coinbase Developer Platform APIs
 * The Coinbase Developer Platform APIs - leading the world's transition onchain.
 * OpenAPI spec version: 2.0.0
 */
const faker_1 = require("@faker-js/faker");
const msw_1 = require("msw");
const getListEvmSmartAccountsResponseMock = () => ({
    ...{
        accounts: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
            address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
            owners: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$")),
            name: faker_1.faker.helpers.arrayElement([
                faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
                undefined,
            ]),
        })),
    },
    ...{ nextPageToken: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]) },
});
exports.getListEvmSmartAccountsResponseMock = getListEvmSmartAccountsResponseMock;
const getCreateEvmSmartAccountResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
    owners: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$")),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getCreateEvmSmartAccountResponseMock = getCreateEvmSmartAccountResponseMock;
const getGetEvmSmartAccountByNameResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
    owners: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$")),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getGetEvmSmartAccountByNameResponseMock = getGetEvmSmartAccountByNameResponseMock;
const getGetEvmSmartAccountResponseMock = (overrideResponse = {}) => ({
    address: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
    owners: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$")),
    name: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^[A-Za-z0-9][A-Za-z0-9-]{0,34}[A-Za-z0-9]$"),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getGetEvmSmartAccountResponseMock = getGetEvmSmartAccountResponseMock;
const getPrepareUserOperationResponseMock = (overrideResponse = {}) => ({
    network: faker_1.faker.helpers.arrayElement(["base-sepolia", "base"]),
    userOpHash: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{64}$"),
    calls: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
        to: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
        value: faker_1.faker.string.alpha(20),
        data: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]*$"),
    })),
    status: faker_1.faker.helpers.arrayElement([
        "pending",
        "signed",
        "broadcast",
        "complete",
        "failed",
    ]),
    transactionHash: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{64}$|^$"),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getPrepareUserOperationResponseMock = getPrepareUserOperationResponseMock;
const getGetUserOperationResponseMock = (overrideResponse = {}) => ({
    network: faker_1.faker.helpers.arrayElement(["base-sepolia", "base"]),
    userOpHash: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{64}$"),
    calls: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
        to: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
        value: faker_1.faker.string.alpha(20),
        data: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]*$"),
    })),
    status: faker_1.faker.helpers.arrayElement([
        "pending",
        "signed",
        "broadcast",
        "complete",
        "failed",
    ]),
    transactionHash: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{64}$|^$"),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getGetUserOperationResponseMock = getGetUserOperationResponseMock;
const getSendUserOperationResponseMock = (overrideResponse = {}) => ({
    network: faker_1.faker.helpers.arrayElement(["base-sepolia", "base"]),
    userOpHash: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{64}$"),
    calls: Array.from({ length: faker_1.faker.number.int({ min: 1, max: 10 }) }, (_, i) => i + 1).map(() => ({
        to: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{40}$"),
        value: faker_1.faker.string.alpha(20),
        data: faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]*$"),
    })),
    status: faker_1.faker.helpers.arrayElement([
        "pending",
        "signed",
        "broadcast",
        "complete",
        "failed",
    ]),
    transactionHash: faker_1.faker.helpers.arrayElement([
        faker_1.faker.helpers.fromRegExp("^0x[0-9a-fA-F]{64}$|^$"),
        undefined,
    ]),
    ...overrideResponse,
});
exports.getSendUserOperationResponseMock = getSendUserOperationResponseMock;
const getListEvmSmartAccountsMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/evm/smart-accounts", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getListEvmSmartAccountsResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getListEvmSmartAccountsMockHandler = getListEvmSmartAccountsMockHandler;
const getCreateEvmSmartAccountMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/smart-accounts", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getCreateEvmSmartAccountResponseMock)()), { status: 201, headers: { "Content-Type": "application/json" } });
    });
};
exports.getCreateEvmSmartAccountMockHandler = getCreateEvmSmartAccountMockHandler;
const getGetEvmSmartAccountByNameMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/evm/smart-accounts/by-name/:name", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetEvmSmartAccountByNameResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetEvmSmartAccountByNameMockHandler = getGetEvmSmartAccountByNameMockHandler;
const getGetEvmSmartAccountMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/evm/smart-accounts/:address", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetEvmSmartAccountResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetEvmSmartAccountMockHandler = getGetEvmSmartAccountMockHandler;
const getPrepareUserOperationMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/smart-accounts/:address/user-operations", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getPrepareUserOperationResponseMock)()), { status: 201, headers: { "Content-Type": "application/json" } });
    });
};
exports.getPrepareUserOperationMockHandler = getPrepareUserOperationMockHandler;
const getGetUserOperationMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/evm/smart-accounts/:address/user-operations/:userOpHash", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetUserOperationResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetUserOperationMockHandler = getGetUserOperationMockHandler;
const getSendUserOperationMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/smart-accounts/:address/user-operations/:userOpHash/send", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getSendUserOperationResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getSendUserOperationMockHandler = getSendUserOperationMockHandler;
const getEvmSmartAccountsMock = () => [
    (0, exports.getListEvmSmartAccountsMockHandler)(),
    (0, exports.getCreateEvmSmartAccountMockHandler)(),
    (0, exports.getGetEvmSmartAccountByNameMockHandler)(),
    (0, exports.getGetEvmSmartAccountMockHandler)(),
    (0, exports.getPrepareUserOperationMockHandler)(),
    (0, exports.getGetUserOperationMockHandler)(),
    (0, exports.getSendUserOperationMockHandler)(),
];
exports.getEvmSmartAccountsMock = getEvmSmartAccountsMock;
//# sourceMappingURL=evm-smart-accounts.msw.js.map