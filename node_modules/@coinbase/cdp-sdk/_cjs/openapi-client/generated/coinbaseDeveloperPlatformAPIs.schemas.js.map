{"version": 3, "file": "coinbaseDeveloperPlatformAPIs.schemas.js", "sourceRoot": "", "sources": ["../../../openapi-client/generated/coinbaseDeveloperPlatformAPIs.schemas.ts"], "names": [], "mappings": ";;;AAkCA,2DAA2D;AAC9C,QAAA,SAAS,GAAG;IACvB,cAAc,EAAE,gBAAgB;IAChC,WAAW,EAAE,aAAa;IAC1B,qBAAqB,EAAE,uBAAuB;IAC9C,SAAS,EAAE,WAAW;IACtB,iBAAiB,EAAE,mBAAmB;IACtC,qBAAqB,EAAE,uBAAuB;IAC9C,eAAe,EAAE,iBAAiB;IAClC,iBAAiB,EAAE,mBAAmB;IACtC,qBAAqB,EAAE,uBAAuB;IAC9C,SAAS,EAAE,WAAW;IACtB,mBAAmB,EAAE,qBAAqB;IAC1C,gBAAgB,EAAE,kBAAkB;IACpC,mBAAmB,EAAE,qBAAqB;IAC1C,SAAS,EAAE,WAAW;IACtB,YAAY,EAAE,cAAc;IAC5B,gBAAgB,EAAE,kBAAkB;IACpC,aAAa,EAAE,eAAe;CACtB,CAAC;AAqGX,2DAA2D;AAC9C,QAAA,uBAAuB,GAAG;IACrC,cAAc,EAAE,cAAc;IAC9B,IAAI,EAAE,MAAM;CACJ,CAAC;AAQX,2DAA2D;AAC9C,QAAA,sBAAsB,GAAG;IACpC,OAAO,EAAE,SAAS;IAClB,MAAM,EAAE,QAAQ;IAChB,SAAS,EAAE,WAAW;IACtB,QAAQ,EAAE,UAAU;IACpB,MAAM,EAAE,QAAQ;CACR,CAAC;AA0BX,2DAA2D;AAC9C,QAAA,eAAe,GAAG;IAC7B,IAAI,EAAE,MAAM;IACZ,QAAQ,EAAE,UAAU;CACZ,CAAC;AAgRX,2DAA2D;AAC9C,QAAA,2BAA2B,GAAG;IACzC,IAAI,EAAE,MAAM;IACZ,cAAc,EAAE,cAAc;CACtB,CAAC;AAsDX,2DAA2D;AAC9C,QAAA,qBAAqB,GAAG;IACnC,QAAQ,EAAE,UAAU;CACZ,CAAC;AAQX,2DAA2D;AAC9C,QAAA,yBAAyB,GAAG;IACvC,GAAG,EAAE,GAAG;IACR,IAAI,EAAE,IAAI;IACV,GAAG,EAAE,GAAG;IACR,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;CACF,CAAC;AAuBX,2DAA2D;AAC9C,QAAA,uBAAuB,GAAG;IACrC,UAAU,EAAE,YAAY;CAChB,CAAC;AAQX,2DAA2D;AAC9C,QAAA,2BAA2B,GAAG;IACzC,EAAE,EAAE,IAAI;IACR,MAAM,EAAE,QAAQ;CACR,CAAC;AA2BX,2DAA2D;AAC9C,QAAA,4BAA4B,GAAG;IAC1C,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;CACR,CAAC;AAQX,2DAA2D;AAC9C,QAAA,+BAA+B,GAAG;IAC7C,kBAAkB,EAAE,oBAAoB;CAChC,CAAC;AAgBX,2DAA2D;AAC9C,QAAA,uBAAuB,GAAG;IACrC,UAAU,EAAE,YAAY;CAChB,CAAC;AAQX,2DAA2D;AAC9C,QAAA,+BAA+B,GAAG;IAC7C,cAAc,EAAE,cAAc;IAC9B,IAAI,EAAE,MAAM;CACJ,CAAC;AAQX,2DAA2D;AAC9C,QAAA,2BAA2B,GAAG;IACzC,EAAE,EAAE,IAAI;IACR,MAAM,EAAE,QAAQ;CACR,CAAC;AA8BX,2DAA2D;AAC9C,QAAA,4BAA4B,GAAG;IAC1C,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;CACR,CAAC;AAQX,2DAA2D;AAC9C,QAAA,+BAA+B,GAAG;IAC7C,kBAAkB,EAAE,oBAAoB;CAChC,CAAC;AAgBX,2DAA2D;AAC9C,QAAA,uBAAuB,GAAG;IACrC,UAAU,EAAE,YAAY;CAChB,CAAC;AAuBX,2DAA2D;AAC9C,QAAA,wBAAwB,GAAG;IACtC,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;CACR,CAAC;AAQX,2DAA2D;AAC9C,QAAA,2BAA2B,GAAG;IACzC,cAAc,EAAE,gBAAgB;CACxB,CAAC;AAgBX,2DAA2D;AAC9C,QAAA,uBAAuB,GAAG;IACrC,UAAU,EAAE,YAAY;CAChB,CAAC;AAQX,2DAA2D;AAC9C,QAAA,2BAA2B,GAAG;IACzC,EAAE,EAAE,IAAI;IACR,MAAM,EAAE,QAAQ;CACR,CAAC;AAyBX,2DAA2D;AAC9C,QAAA,4BAA4B,GAAG;IAC1C,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;CACR,CAAC;AAQX,2DAA2D;AAC9C,QAAA,+BAA+B,GAAG;IAC7C,kBAAkB,EAAE,oBAAoB;CAChC,CAAC;AAgBX,2DAA2D;AAC9C,QAAA,qBAAqB,GAAG;IACnC,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;CACR,CAAC;AAQX,2DAA2D;AAC9C,QAAA,wBAAwB,GAAG;IACtC,WAAW,EAAE,aAAa;CAClB,CAAC;AAwBX,2DAA2D;AAC9C,QAAA,WAAW,GAAG;IACzB,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;CACV,CAAC;AA8CX,2DAA2D;AAC9C,QAAA,iBAAiB,GAAG;IAC/B,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;CACR,CAAC;AAOX,2DAA2D;AAC9C,QAAA,iBAAiB,GAAG;IAC/B,IAAI,EAAE,MAAM;IACZ,YAAY,EAAE,cAAc;CACpB,CAAC;AAmHX,2DAA2D;AAC9C,QAAA,OAAO,GAAG;IACrB,YAAY,EAAE,cAAc;IAC5B,WAAW,EAAE,aAAa;CAClB,CAAC;AAqBX,2DAA2D;AAC9C,QAAA,kBAAkB,GAAG;IAChC,cAAc,EAAE,gBAAgB;CACxB,CAAC;AAOX,2DAA2D;AAC9C,QAAA,kBAAkB,GAAG;IAChC,WAAW,EAAE,aAAa;CAClB,CAAC;AAOX,2DAA2D;AAC9C,QAAA,cAAc,GAAG;IAC5B,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;IAClB,SAAS,EAAE,WAAW;IACtB,MAAM,EAAE,QAAQ;CACR,CAAC;AA0IX,2DAA2D;AAC9C,QAAA,6BAA6B,GAAG;IAC3C,IAAI,EAAE,MAAM;IACZ,cAAc,EAAE,cAAc;CACtB,CAAC;AAqHX,2DAA2D;AAC9C,QAAA,+BAA+B,GAAG;IAC7C,cAAc,EAAE,cAAc;IAC9B,IAAI,EAAE,MAAM;CACJ,CAAC;AA2FX,2DAA2D;AAC9C,QAAA,2BAA2B,GAAG;IACzC,cAAc,EAAE,cAAc;IAC9B,kBAAkB,EAAE,kBAAkB;IACtC,gBAAgB,EAAE,gBAAgB;CAC1B,CAAC;AAQX,2DAA2D;AAC9C,QAAA,yBAAyB,GAAG;IACvC,GAAG,EAAE,KAAK;IACV,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,OAAO;CACN,CAAC;AAqCX,2DAA2D;AAC9C,QAAA,iBAAiB,GAAG;IAC/B,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;CACV,CAAC;AAeX,2DAA2D;AAC9C,QAAA,qBAAqB,GAAG;IACnC,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;CACV,CAAC;AAuHX,2DAA2D;AAC9C,QAAA,4BAA4B,GAAG;IAC1C,GAAG,EAAE,KAAK;IACV,IAAI,EAAE,MAAM;CACJ,CAAC;AA8BX,2DAA2D;AAC9C,QAAA,wCAAwC,GAAG;IACtD,cAAc,EAAE,gBAAgB;CACxB,CAAC;AAQX,2DAA2D;AAC9C,QAAA,wCAAwC,GAAG;IACtD,WAAW,EAAE,aAAa;CAClB,CAAC"}