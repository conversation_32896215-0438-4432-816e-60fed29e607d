"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEvmSwapsMock = exports.getCreateEvmSwapQuoteMockHandler = exports.getGetEvmSwapPriceMockHandler = exports.getCreateEvmSwapQuoteResponseMock = exports.getCreateEvmSwapQuoteResponseSwapUnavailableResponseMock = exports.getCreateEvmSwapQuoteResponseCreateSwapQuoteResponseMock = exports.getGetEvmSwapPriceResponseMock = exports.getGetEvmSwapPriceResponseSwapUnavailableResponseMock = exports.getGetEvmSwapPriceResponseGetSwapPriceResponseMock = void 0;
/**
 * Generated by orval v7.6.0 🍺
 * Do not edit manually.
 * Coinbase Developer Platform APIs
 * The Coinbase Developer Platform APIs - leading the world's transition onchain.
 * OpenAPI spec version: 2.0.0
 */
const faker_1 = require("@faker-js/faker");
const msw_1 = require("msw");
const getGetEvmSwapPriceResponseGetSwapPriceResponseMock = (overrideResponse = {}) => ({
    ...{
        ...{
            blockNumber: faker_1.faker.helpers.fromRegExp("^[1-9]\d*$"),
            toAmount: faker_1.faker.helpers.fromRegExp("^(0|[1-9]\d*)$"),
            toToken: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
            fees: {
                gasFee: {
                    ...{
                        amount: faker_1.faker.helpers.fromRegExp("^\d+$"),
                        token: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                    },
                },
                protocolFee: {
                    ...{
                        amount: faker_1.faker.helpers.fromRegExp("^\d+$"),
                        token: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                    },
                },
            },
            issues: {
                allowance: {
                    currentAllowance: faker_1.faker.helpers.fromRegExp("^\d+$"),
                    spender: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                },
                balance: {
                    token: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                    currentBalance: faker_1.faker.helpers.fromRegExp("^\d+$"),
                    requiredBalance: faker_1.faker.helpers.fromRegExp("^\d+$"),
                },
                simulationIncomplete: faker_1.faker.datatype.boolean(),
            },
            liquidityAvailable: faker_1.faker.datatype.boolean(),
            minToAmount: faker_1.faker.helpers.fromRegExp("^(0|[1-9]\d*)$"),
            fromAmount: faker_1.faker.helpers.fromRegExp("^(0|[1-9]\d*)$"),
            fromToken: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
        },
        ...{
            gas: faker_1.faker.helpers.arrayElement([faker_1.faker.helpers.fromRegExp("^\d+$"), null]),
            gasPrice: faker_1.faker.helpers.fromRegExp("^\d+$"),
        },
    },
    ...overrideResponse,
});
exports.getGetEvmSwapPriceResponseGetSwapPriceResponseMock = getGetEvmSwapPriceResponseGetSwapPriceResponseMock;
const getGetEvmSwapPriceResponseSwapUnavailableResponseMock = (overrideResponse = {}) => ({
    ...{ liquidityAvailable: faker_1.faker.datatype.boolean() },
    ...overrideResponse,
});
exports.getGetEvmSwapPriceResponseSwapUnavailableResponseMock = getGetEvmSwapPriceResponseSwapUnavailableResponseMock;
const getGetEvmSwapPriceResponseMock = () => faker_1.faker.helpers.arrayElement([
    { ...(0, exports.getGetEvmSwapPriceResponseGetSwapPriceResponseMock)() },
    { ...(0, exports.getGetEvmSwapPriceResponseSwapUnavailableResponseMock)() },
]);
exports.getGetEvmSwapPriceResponseMock = getGetEvmSwapPriceResponseMock;
const getCreateEvmSwapQuoteResponseCreateSwapQuoteResponseMock = (overrideResponse = {}) => ({
    ...{
        ...{
            permit2: {
                hash: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{64}$"),
                eip712: {
                    domain: {
                        name: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
                        version: faker_1.faker.helpers.arrayElement([faker_1.faker.string.alpha(20), undefined]),
                        chainId: faker_1.faker.helpers.arrayElement([
                            faker_1.faker.number.int({ min: undefined, max: undefined }),
                            undefined,
                        ]),
                        verifyingContract: faker_1.faker.helpers.arrayElement([
                            faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                            undefined,
                        ]),
                        salt: faker_1.faker.helpers.arrayElement([
                            faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{64}$"),
                            undefined,
                        ]),
                    },
                    types: {},
                    primaryType: faker_1.faker.string.alpha(20),
                    message: {},
                },
            },
            transaction: {
                to: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                data: faker_1.faker.string.alpha(20),
                gas: faker_1.faker.helpers.fromRegExp("^\d+$"),
                gasPrice: faker_1.faker.helpers.fromRegExp("^\d+$"),
                value: faker_1.faker.helpers.fromRegExp("^\d+$"),
            },
        },
        ...{
            blockNumber: faker_1.faker.helpers.fromRegExp("^[1-9]\d*$"),
            toAmount: faker_1.faker.helpers.fromRegExp("^(0|[1-9]\d*)$"),
            toToken: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
            fees: {
                gasFee: {
                    ...{
                        amount: faker_1.faker.helpers.fromRegExp("^\d+$"),
                        token: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                    },
                },
                protocolFee: {
                    ...{
                        amount: faker_1.faker.helpers.fromRegExp("^\d+$"),
                        token: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                    },
                },
            },
            issues: {
                allowance: {
                    currentAllowance: faker_1.faker.helpers.fromRegExp("^\d+$"),
                    spender: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                },
                balance: {
                    token: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
                    currentBalance: faker_1.faker.helpers.fromRegExp("^\d+$"),
                    requiredBalance: faker_1.faker.helpers.fromRegExp("^\d+$"),
                },
                simulationIncomplete: faker_1.faker.datatype.boolean(),
            },
            liquidityAvailable: faker_1.faker.datatype.boolean(),
            minToAmount: faker_1.faker.helpers.fromRegExp("^(0|[1-9]\d*)$"),
            fromAmount: faker_1.faker.helpers.fromRegExp("^(0|[1-9]\d*)$"),
            fromToken: faker_1.faker.helpers.fromRegExp("^0x[a-fA-F0-9]{40}$"),
        },
    },
    ...overrideResponse,
});
exports.getCreateEvmSwapQuoteResponseCreateSwapQuoteResponseMock = getCreateEvmSwapQuoteResponseCreateSwapQuoteResponseMock;
const getCreateEvmSwapQuoteResponseSwapUnavailableResponseMock = (overrideResponse = {}) => ({
    ...{ liquidityAvailable: faker_1.faker.datatype.boolean() },
    ...overrideResponse,
});
exports.getCreateEvmSwapQuoteResponseSwapUnavailableResponseMock = getCreateEvmSwapQuoteResponseSwapUnavailableResponseMock;
const getCreateEvmSwapQuoteResponseMock = () => faker_1.faker.helpers.arrayElement([
    { ...(0, exports.getCreateEvmSwapQuoteResponseCreateSwapQuoteResponseMock)() },
    { ...(0, exports.getCreateEvmSwapQuoteResponseSwapUnavailableResponseMock)() },
]);
exports.getCreateEvmSwapQuoteResponseMock = getCreateEvmSwapQuoteResponseMock;
const getGetEvmSwapPriceMockHandler = (overrideResponse) => {
    return msw_1.http.get("*/v2/evm/swaps/quote", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getGetEvmSwapPriceResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getGetEvmSwapPriceMockHandler = getGetEvmSwapPriceMockHandler;
const getCreateEvmSwapQuoteMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/swaps", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getCreateEvmSwapQuoteResponseMock)()), { status: 201, headers: { "Content-Type": "application/json" } });
    });
};
exports.getCreateEvmSwapQuoteMockHandler = getCreateEvmSwapQuoteMockHandler;
const getEvmSwapsMock = () => [
    (0, exports.getGetEvmSwapPriceMockHandler)(),
    (0, exports.getCreateEvmSwapQuoteMockHandler)(),
];
exports.getEvmSwapsMock = getEvmSwapsMock;
//# sourceMappingURL=evm-swaps.msw.js.map