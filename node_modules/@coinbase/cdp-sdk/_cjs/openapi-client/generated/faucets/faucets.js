"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestSolanaFaucet = exports.requestEvmFaucet = void 0;
const cdpApiClient_js_1 = require("../../cdpApiClient.js");
/**
 * Request funds from the CDP Faucet on supported EVM test networks.

Faucets are available for ETH, USDC, EURC, and cbBTC on Base Sepolia and Ethereum Sepolia, and for ETH only on Ethereum Hoodi.

To prevent abuse, we enforce rate limits within a rolling 24-hour window to control the amount of funds that can be requested.
These limits are applied at both the CDP User level and the blockchain address level.
A single blockchain address cannot exceed the specified limits, even if multiple users submit requests to the same address.

| Token | Amount per Faucet Request |Rolling 24-hour window Rate Limits|
|:-----:|:-------------------------:|:--------------------------------:|
| ETH   | 0.0001 ETH                | 0.1 ETH                          |
| USDC  | 1 USDC                    | 10 USDC                          |
| EURC  | 1 EURC                    | 10 EURC                          |
| cbBTC | 0.0001 cbBTC              | 0.001 cbBTC                      |

 * @summary Request funds on EVM test networks
 */
const requestEvmFaucet = (requestEvmFaucetBody, options) => {
    return (0, cdpApiClient_js_1.cdpApiClient)({
        url: `/v2/evm/faucet`,
        method: "POST",
        headers: { "Content-Type": "application/json" },
        data: requestEvmFaucetBody,
    }, options);
};
exports.requestEvmFaucet = requestEvmFaucet;
/**
 * Request funds from the CDP Faucet on Solana devnet.

Faucets are available for SOL.

To prevent abuse, we enforce rate limits within a rolling 24-hour window to control the amount of funds that can be requested.
These limits are applied at both the CDP User level and the blockchain address level.
A single blockchain address cannot exceed the specified limits, even if multiple users submit requests to the same address.

| Token | Amount per Faucet Request |Rolling 24-hour window Rate Limits|
|:-----:|:-------------------------:|:--------------------------------:|
| SOL   | 0.00125 SOL               | 0.0125 SOL                       |
| USDC  | 1 USDC                    | 10 USDC                          |

 * @summary Request funds on Solana devnet
 */
const requestSolanaFaucet = (requestSolanaFaucetBody, options) => {
    return (0, cdpApiClient_js_1.cdpApiClient)({
        url: `/v2/solana/faucet`,
        method: "POST",
        headers: { "Content-Type": "application/json" },
        data: requestSolanaFaucetBody,
    }, options);
};
exports.requestSolanaFaucet = requestSolanaFaucet;
//# sourceMappingURL=faucets.js.map