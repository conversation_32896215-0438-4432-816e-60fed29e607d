"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFaucetsMock = exports.getRequestSolanaFaucetMockHandler = exports.getRequestEvmFaucetMockHandler = exports.getRequestSolanaFaucetResponseMock = exports.getRequestEvmFaucetResponseMock = void 0;
/**
 * Generated by orval v7.6.0 🍺
 * Do not edit manually.
 * Coinbase Developer Platform APIs
 * The Coinbase Developer Platform APIs - leading the world's transition onchain.
 * OpenAPI spec version: 2.0.0
 */
const faker_1 = require("@faker-js/faker");
const msw_1 = require("msw");
const getRequestEvmFaucetResponseMock = (overrideResponse = {}) => ({ transactionHash: faker_1.faker.string.alpha(20), ...overrideResponse });
exports.getRequestEvmFaucetResponseMock = getRequestEvmFaucetResponseMock;
const getRequestSolanaFaucetResponseMock = (overrideResponse = {}) => ({
    transactionSignature: faker_1.faker.string.alpha(20),
    ...overrideResponse,
});
exports.getRequestSolanaFaucetResponseMock = getRequestSolanaFaucetResponseMock;
const getRequestEvmFaucetMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/evm/faucet", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getRequestEvmFaucetResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getRequestEvmFaucetMockHandler = getRequestEvmFaucetMockHandler;
const getRequestSolanaFaucetMockHandler = (overrideResponse) => {
    return msw_1.http.post("*/v2/solana/faucet", async (info) => {
        await (0, msw_1.delay)(0);
        return new msw_1.HttpResponse(JSON.stringify(overrideResponse !== undefined
            ? typeof overrideResponse === "function"
                ? await overrideResponse(info)
                : overrideResponse
            : (0, exports.getRequestSolanaFaucetResponseMock)()), { status: 200, headers: { "Content-Type": "application/json" } });
    });
};
exports.getRequestSolanaFaucetMockHandler = getRequestSolanaFaucetMockHandler;
const getFaucetsMock = () => [
    (0, exports.getRequestEvmFaucetMockHandler)(),
    (0, exports.getRequestSolanaFaucetMockHandler)(),
];
exports.getFaucetsMock = getFaucetsMock;
//# sourceMappingURL=faucets.msw.js.map