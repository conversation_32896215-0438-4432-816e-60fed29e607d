"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnknownError = exports.UnknownApiError = exports.APIError = exports.HttpErrorType = void 0;
exports.isOpenAPIError = isOpenAPIError;
exports.HttpErrorType = {
    unexpected_error: "unexpected_error",
    unauthorized: "unauthorized",
    not_found: "not_found",
    bad_gateway: "bad_gateway",
    service_unavailable: "service_unavailable",
    unknown: "unknown",
};
/**
 * Extended API error that encompasses both OpenAPI errors and other API-related errors
 */
class APIError extends Error {
    statusCode;
    errorType;
    errorMessage;
    correlationId;
    errorLink;
    /**
     * Constructor for the APIError class
     *
     * @param statusCode - The HTTP status code
     * @param errorType - The type of error
     * @param errorMessage - The error message
     * @param correlationId - The correlation ID
     * @param errorLink - URL to documentation about this error
     * @param cause - The cause of the error
     */
    constructor(statusCode, errorType, errorMessage, correlationId, errorLink, cause) {
        super(errorMessage, { cause });
        this.name = "APIError";
        this.statusCode = statusCode;
        this.errorType = errorType;
        this.errorMessage = errorMessage;
        // Only set correlationId if it's defined
        if (correlationId !== undefined) {
            this.correlationId = correlationId;
        }
        // Only set errorLink if it's defined
        if (errorLink !== undefined) {
            this.errorLink = errorLink;
        }
    }
    /**
     * Convert the error to a JSON object, excluding undefined properties
     *
     * @returns The error as a JSON object
     */
    toJSON() {
        return {
            name: this.name,
            statusCode: this.statusCode,
            errorType: this.errorType,
            errorMessage: this.errorMessage,
            ...(this.correlationId && { correlationId: this.correlationId }),
            ...(this.errorLink && { errorLink: this.errorLink }),
        };
    }
}
exports.APIError = APIError;
/**
 * Error thrown when an Axios request is made but no response is received
 */
class UnknownApiError extends APIError {
    /**
     * Constructor for the UnknownApiError class
     *
     * @param errorType - The type of error
     * @param errorMessage - The error message
     * @param cause - The cause of the error
     */
    constructor(errorType, errorMessage, cause) {
        super(0, errorType, errorMessage, undefined, undefined, cause);
        this.name = "UnknownApiError";
    }
}
exports.UnknownApiError = UnknownApiError;
/**
 * Error thrown when an error is not known
 */
class UnknownError extends Error {
    /**
     * Constructor for the UnknownError class
     *
     * @param message - The error message
     * @param cause - The cause of the error
     */
    constructor(message, cause) {
        super(message, { cause });
        this.name = "UnknownError";
    }
}
exports.UnknownError = UnknownError;
/**
 * Type guard to check if an object is an OpenAPIError
 *
 * @param obj - The object to check
 * @returns True if the object is an OpenAPIError
 */
function isOpenAPIError(obj) {
    return (obj !== null &&
        typeof obj === "object" &&
        "errorType" in obj &&
        typeof obj.errorType === "string" &&
        "errorMessage" in obj &&
        typeof obj.errorMessage === "string");
}
//# sourceMappingURL=errors.js.map