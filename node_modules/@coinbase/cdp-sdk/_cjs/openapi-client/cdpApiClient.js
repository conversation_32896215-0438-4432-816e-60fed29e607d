"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cdpApiClient = exports.configure = void 0;
// eslint-disable-next-line import/no-named-as-default
const axios_1 = __importDefault(require("axios"));
const index_js_1 = require("../auth/hooks/axios/index.js");
const constants_js_1 = require("../constants.js");
const errors_js_1 = require("./errors.js");
let axiosInstance;
/**
 * Configures the CDP client with the given options.
 *
 * @param {CdpOptions} options - The CDP options.
 */
const configure = (options) => {
    axiosInstance = axios_1.default.create({
        baseURL: options.basePath || "https://api.cdp.coinbase.com/platform",
    });
    axiosInstance = (0, index_js_1.withAuth)(axiosInstance, {
        apiKeyId: options.apiKeyId,
        apiKeySecret: options.apiKeySecret,
        source: options.source || "sdk-openapi-client",
        sourceVersion: options.sourceVersion,
        walletSecret: options.walletSecret,
        expiresIn: options.expiresIn,
        debug: options.debugging,
    });
};
exports.configure = configure;
/**
 * Adds an idempotency key to request config if provided
 *
 * @param config - The Axios request configuration.
 * @param idempotencyKey - The idempotency key.
 * @returns The Axios request configuration with the idempotency key.
 */
const addIdempotencyKey = (config, idempotencyKey) => {
    if (!idempotencyKey) {
        return config;
    }
    return {
        ...config,
        headers: {
            ...(config.headers || {}),
            "X-Idempotency-Key": idempotencyKey,
        },
    };
};
/**
 * Mutates the given Axios request configuration to add the CDP API key signature
 * to the request headers.
 *
 * @param {AxiosRequestConfig} config - The Axios request configuration.
 * @param idempotencyKey - The idempotency key.
 * @returns {Promise<T>} A promise that resolves to the response data.
 * @throws {APIError} If the request fails.
 */
const cdpApiClient = async (config, idempotencyKey) => {
    validateCall(config);
    // Add idempotency key to the request headers if provided
    const configWithIdempotencyKey = addIdempotencyKey(config, idempotencyKey);
    try {
        const response = await axiosInstance(configWithIdempotencyKey);
        return response.data;
    }
    catch (error) {
        // eslint-disable-next-line import/no-named-as-default-member
        if (axios_1.default.isAxiosError(error) && !error.response) {
            throw new errors_js_1.UnknownApiError(errors_js_1.HttpErrorType.unknown, error.cause instanceof Error ? error.cause.message : error.message, error.cause);
        }
        // eslint-disable-next-line import/no-named-as-default-member
        if (axios_1.default.isAxiosError(error) && error.response) {
            if ((0, errors_js_1.isOpenAPIError)(error.response.data)) {
                throw new errors_js_1.APIError(error.response.status, error.response.data.errorType, error.response.data.errorMessage, error.response.data.correlationId, error.response.data.errorLink, error.cause);
            }
            else {
                const statusCode = error.response.status;
                switch (statusCode) {
                    case 401:
                        throw new errors_js_1.APIError(statusCode, errors_js_1.HttpErrorType.unauthorized, "Unauthorized.", undefined, `${constants_js_1.ERROR_DOCS_PAGE_URL}#unauthorized`, error.cause);
                    case 404:
                        throw new errors_js_1.APIError(statusCode, errors_js_1.HttpErrorType.not_found, "API not found.", undefined, `${constants_js_1.ERROR_DOCS_PAGE_URL}#not_found`, error.cause);
                    case 502:
                        throw new errors_js_1.APIError(statusCode, errors_js_1.HttpErrorType.bad_gateway, "Bad gateway.", undefined, `${constants_js_1.ERROR_DOCS_PAGE_URL}`, error.cause);
                    case 503:
                        throw new errors_js_1.APIError(statusCode, errors_js_1.HttpErrorType.service_unavailable, "Service unavailable. Please try again later.", undefined, `${constants_js_1.ERROR_DOCS_PAGE_URL}`, error.cause);
                    default: {
                        let errorText = "";
                        if (error.response.data) {
                            try {
                                errorText = JSON.stringify(error.response.data);
                            }
                            catch {
                                errorText = String(error.response.data);
                            }
                        }
                        const errorMessage = errorText
                            ? `An unexpected error occurred: ${errorText}`
                            : "An unexpected error occurred.";
                        throw new errors_js_1.APIError(statusCode, errors_js_1.HttpErrorType.unexpected_error, errorMessage, undefined, `${constants_js_1.ERROR_DOCS_PAGE_URL}`, error.cause);
                    }
                }
            }
        }
        throw new errors_js_1.UnknownError("Something went wrong. Please reach out at https://discord.com/channels/1220414409550336183/1271495764580896789 for help.", error instanceof Error ? error : undefined);
    }
};
exports.cdpApiClient = cdpApiClient;
/**
 * Validates the call to the cdpApiClient.
 *
 * @param {AxiosRequestConfig} config - The Axios request configuration.
 * @throws {Error} If the call is not valid.
 */
const validateCall = (config) => {
    if (!axiosInstance.getUri() || axiosInstance.getUri() === "") {
        throw new Error("CDP client URI not configured. Call configure() first.");
    }
    if (!config.url || config.url === "") {
        throw new Error("AxiosRequestConfig URL is empty. This should never happen.");
    }
    if (!config.method || config.method === "") {
        throw new Error("AxiosRequestConfig method is empty. This should never happen.");
    }
};
//# sourceMappingURL=cdpApiClient.js.map