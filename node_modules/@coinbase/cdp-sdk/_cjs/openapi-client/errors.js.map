{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../openapi-client/errors.ts"], "names": [], "mappings": ";;;AA0HA,wCASC;AA9HY,QAAA,aAAa,GAAG;IAC3B,gBAAgB,EAAE,kBAAkB;IACpC,YAAY,EAAE,cAAc;IAC5B,SAAS,EAAE,WAAW;IACtB,WAAW,EAAE,aAAa;IAC1B,mBAAmB,EAAE,qBAAqB;IAC1C,OAAO,EAAE,SAAS;CACV,CAAC;AASX;;GAEG;AACH,MAAa,QAAS,SAAQ,KAAK;IACjC,UAAU,CAAS;IACnB,SAAS,CAAe;IACxB,YAAY,CAAS;IACrB,aAAa,CAAU;IACvB,SAAS,CAAU;IAEnB;;;;;;;;;OASG;IACH,YACE,UAAkB,EAClB,SAAuB,EACvB,YAAoB,EACpB,aAAsB,EACtB,SAAkB,EAClB,KAAa;QAEb,KAAK,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,yCAAyC;QACzC,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACrC,CAAC;QAED,qCAAqC;QACrC,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM;QACJ,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,GAAG,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;YAChE,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;SACrD,CAAC;IACJ,CAAC;CACF;AAzDD,4BAyDC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,QAAQ;IAC3C;;;;;;OAMG;IACH,YAAY,SAAuB,EAAE,YAAoB,EAAE,KAAa;QACtE,KAAK,CAAC,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF;AAZD,0CAYC;AAED;;GAEG;AACH,MAAa,YAAa,SAAQ,KAAK;IACrC;;;;;OAKG;IACH,YAAY,OAAe,EAAE,KAAa;QACxC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC1B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;IAC7B,CAAC;CACF;AAXD,oCAWC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAAC,GAAY;IACzC,OAAO,CACL,GAAG,KAAK,IAAI;QACZ,OAAO,GAAG,KAAK,QAAQ;QACvB,WAAW,IAAI,GAAG;QAClB,OAAQ,GAAoB,CAAC,SAAS,KAAK,QAAQ;QACnD,cAAc,IAAI,GAAG;QACrB,OAAQ,GAAoB,CAAC,YAAY,KAAK,QAAQ,CACvD,CAAC;AACJ,CAAC"}