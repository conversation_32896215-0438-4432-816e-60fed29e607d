{"version": 3, "file": "toEvmSmartAccount.js", "sourceRoot": "", "sources": ["../../../accounts/evm/toEvmSmartAccount.ts"], "names": [], "mappings": ";;AAuEA,8CAyFC;AAhKD,4DAAmE;AAEnE,sEAAkF;AAElF,0GAI+D;AAC/D,+EAAyE;AACzE,iFAIgD;AAChD,yEAI4C;AAC5C,iFAIgD;AAChD,kFAA4E;AAC5E,sFAAgF;AAChF,gHAA0G;AAC1G,wEAAkE;AAClE,uFAImD;AA0BnD;;;;;;;;;;;GAWG;AACH,SAAgB,iBAAiB,CAC/B,SAA+B,EAC/B,OAAiC;IAEjC,MAAM,OAAO,GAAoB;QAC/B,OAAO,EAAE,OAAO,CAAC,YAAY,CAAC,OAAkB;QAChD,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QACvB,KAAK,CAAC,QAAQ,CAAC,YAAY;YACzB,OAAO,IAAA,sBAAQ,EAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,8DAA4B,CAAC,CAAC;QAClF,CAAC;QACD,KAAK,CAAC,iBAAiB,CACrB,OAAkD;YAElD,OAAO,IAAA,wCAAiB,EAAC,SAAS,EAAE;gBAClC,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,iBAAiB,CACrB,OAAkE;YAElE,OAAO,IAAA,wCAAiB,EAAC,SAAS,EAAE;gBAClC,GAAG,OAAO;gBACV,YAAY,EAAE,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,oBAAoB,CACxB,OAAiE;YAEjE,OAAO,IAAA,8CAAoB,EAAC,SAAS,EAAE;gBACrC,GAAG,OAAO;gBACV,mBAAmB,EAAE,OAAO,CAAC,OAAO;aACrC,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,gBAAgB,CACpB,OAAsD;YAEtD,OAAO,IAAA,sCAAgB,EAAC,SAAS,EAAE;gBACjC,GAAG,OAAO;gBACV,YAAY,EAAE,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,aAAa,CACjB,OAA8C;YAE9C,OAAO,IAAA,gCAAa,EAAC,SAAS,EAAE;gBAC9B,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,SAAS,CAAC,OAA0C;YACxD,OAAO,IAAA,wBAAS,EAAC,SAAS,EAAE;gBAC1B,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,OAAqC;YAC9C,OAAO,IAAA,cAAI,EAAC,SAAS,EAAE;gBACrB,GAAG,OAAO;gBACV,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,2BAA2B,CAC/B,OAAoC;YAEpC,OAAO,IAAA,4DAA2B,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;QACD,KAAK,CAAC,SAAS,CAAC,OAAqC;YACnD,OAAO,IAAA,oCAAe,EAAC,SAAS,EAAE;gBAChC,GAAG,OAAO;gBACV,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,8CAA8C;gBACnE,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,uCAAuC;gBAC9E,YAAY,EAAE,OAAO,EAAE,gDAAgD;aACxE,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,OAAgC;YACzC,OAAO,IAAA,wCAAiB,EAAC,SAAS,EAAE;gBAClC,GAAG,OAAO;gBACV,YAAY,EAAE,OAAO;gBACrB,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,8CAA8C;gBACnE,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,uCAAuC;aAC/E,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI;QAC/B,IAAI,EAAE,WAAW;KAClB,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC"}