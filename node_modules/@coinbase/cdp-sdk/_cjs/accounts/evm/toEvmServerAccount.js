"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toEvmServerAccount = toEvmServerAccount;
const viem_1 = require("viem");
const fund_js_1 = require("../../actions/evm/fund/fund.js");
const quoteFund_js_1 = require("../../actions/evm/fund/quoteFund.js");
const waitForFundOperationReceipt_js_1 = require("../../actions/evm/fund/waitForFundOperationReceipt.js");
const listTokenBalances_js_1 = require("../../actions/evm/listTokenBalances.js");
const requestFaucet_js_1 = require("../../actions/evm/requestFaucet.js");
const sendTransaction_js_1 = require("../../actions/evm/sendTransaction.js");
const createSwapQuote_js_1 = require("../../actions/evm/swap/createSwapQuote.js");
const sendSwapTransaction_js_1 = require("../../actions/evm/swap/sendSwapTransaction.js");
const accountTransferStrategy_js_1 = require("../../actions/evm/transfer/accountTransferStrategy.js");
const transfer_js_1 = require("../../actions/evm/transfer/transfer.js");
/**
 * Creates a Server-managed EvmAccount instance from an existing EvmAccount.
 * Use this to interact with previously deployed EvmAccounts, rather than creating new ones.
 *
 * @param {CdpOpenApiClientType} apiClient - The API client.
 * @param {ToEvmServerAccountOptions} options - Configuration options.
 * @param {EvmAccount} options.account - The EvmAccount that was previously created.
 * @returns {EvmServerAccount} A configured EvmAccount instance ready for signing.
 */
function toEvmServerAccount(apiClient, options) {
    const account = {
        address: options.account.address,
        async signMessage({ message }) {
            const result = await apiClient.signEvmMessage(options.account.address, {
                message: message.toString(),
            });
            return result.signature;
        },
        async sign(parameters) {
            const result = await apiClient.signEvmHash(options.account.address, {
                hash: parameters.hash,
            });
            return result.signature;
        },
        async signTransaction(transaction) {
            const result = await apiClient.signEvmTransaction(options.account.address, {
                transaction: (0, viem_1.serializeTransaction)(transaction),
            });
            return result.signedTransaction;
        },
        async signTypedData(parameters) {
            const { domain = {}, message, primaryType } = parameters;
            const types = {
                EIP712Domain: (0, viem_1.getTypesForEIP712Domain)({ domain }),
                ...parameters.types,
            };
            const openApiMessage = {
                domain: domain,
                types,
                primaryType,
                message,
            };
            const result = await apiClient.signEvmTypedData(options.account.address, openApiMessage);
            return result.signature;
        },
        async transfer(transferArgs) {
            return (0, transfer_js_1.transfer)(apiClient, account, transferArgs, accountTransferStrategy_js_1.accountTransferStrategy);
        },
        async listTokenBalances(options) {
            return (0, listTokenBalances_js_1.listTokenBalances)(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async requestFaucet(options) {
            return (0, requestFaucet_js_1.requestFaucet)(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async sendTransaction(options) {
            return (0, sendTransaction_js_1.sendTransaction)(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async quoteFund(options) {
            return (0, quoteFund_js_1.quoteFund)(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async fund(options) {
            return (0, fund_js_1.fund)(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async waitForFundOperationReceipt(options) {
            return (0, waitForFundOperationReceipt_js_1.waitForFundOperationReceipt)(apiClient, options);
        },
        async quoteSwap(options) {
            return (0, createSwapQuote_js_1.createSwapQuote)(apiClient, {
                ...options,
                taker: this.address,
            });
        },
        async swap(options) {
            return (0, sendSwapTransaction_js_1.sendSwapTransaction)(apiClient, {
                ...options,
                address: this.address,
                taker: this.address, // Always use account's address as taker
            });
        },
        name: options.account.name,
        type: "evm-server",
        policies: options.account.policies,
    };
    return account;
}
//# sourceMappingURL=toEvmServerAccount.js.map