"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toEvmSmartAccount = toEvmSmartAccount;
const fund_js_1 = require("../../actions/evm/fund/fund.js");
const quoteFund_js_1 = require("../../actions/evm/fund/quoteFund.js");
const waitForFundOperationReceipt_js_1 = require("../../actions/evm/fund/waitForFundOperationReceipt.js");
const getUserOperation_js_1 = require("../../actions/evm/getUserOperation.js");
const listTokenBalances_js_1 = require("../../actions/evm/listTokenBalances.js");
const requestFaucet_js_1 = require("../../actions/evm/requestFaucet.js");
const sendUserOperation_js_1 = require("../../actions/evm/sendUserOperation.js");
const createSwapQuote_js_1 = require("../../actions/evm/swap/createSwapQuote.js");
const sendSwapOperation_js_1 = require("../../actions/evm/swap/sendSwapOperation.js");
const smartAccountTransferStrategy_js_1 = require("../../actions/evm/transfer/smartAccountTransferStrategy.js");
const transfer_js_1 = require("../../actions/evm/transfer/transfer.js");
const waitForUserOperation_js_1 = require("../../actions/evm/waitForUserOperation.js");
/**
 * Creates a EvmSmartAccount instance from an existing EvmSmartAccount and owner.
 * Use this to interact with previously deployed EvmSmartAccounts, rather than creating new ones.
 *
 * The owner must be the original owner of the evm smart account.
 *
 * @param {CdpOpenApiClientType} apiClient - The API client.
 * @param {ToEvmSmartAccountOptions} options - Configuration options.
 * @param {EvmSmartAccount} options.smartAccount - The deployed evm smart account.
 * @param {EvmAccount} options.owner - The owner which signs for the smart account.
 * @returns {EvmSmartAccount} A configured EvmSmartAccount instance ready for user operation submission.
 */
function toEvmSmartAccount(apiClient, options) {
    const account = {
        address: options.smartAccount.address,
        owners: [options.owner],
        async transfer(transferArgs) {
            return (0, transfer_js_1.transfer)(apiClient, account, transferArgs, smartAccountTransferStrategy_js_1.smartAccountTransferStrategy);
        },
        async listTokenBalances(options) {
            return (0, listTokenBalances_js_1.listTokenBalances)(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async sendUserOperation(options) {
            return (0, sendUserOperation_js_1.sendUserOperation)(apiClient, {
                ...options,
                smartAccount: account,
            });
        },
        async waitForUserOperation(options) {
            return (0, waitForUserOperation_js_1.waitForUserOperation)(apiClient, {
                ...options,
                smartAccountAddress: account.address,
            });
        },
        async getUserOperation(options) {
            return (0, getUserOperation_js_1.getUserOperation)(apiClient, {
                ...options,
                smartAccount: account,
            });
        },
        async requestFaucet(options) {
            return (0, requestFaucet_js_1.requestFaucet)(apiClient, {
                ...options,
                address: account.address,
            });
        },
        async quoteFund(options) {
            return (0, quoteFund_js_1.quoteFund)(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async fund(options) {
            return (0, fund_js_1.fund)(apiClient, {
                ...options,
                address: this.address,
            });
        },
        async waitForFundOperationReceipt(options) {
            return (0, waitForFundOperationReceipt_js_1.waitForFundOperationReceipt)(apiClient, options);
        },
        async quoteSwap(options) {
            return (0, createSwapQuote_js_1.createSwapQuote)(apiClient, {
                ...options,
                taker: this.address, // Always use smart account's address as taker
                signerAddress: this.owners[0].address, // Always use owner's address as signer
                smartAccount: account, // Pass smart account for execute method support
            });
        },
        async swap(options) {
            return (0, sendSwapOperation_js_1.sendSwapOperation)(apiClient, {
                ...options,
                smartAccount: account,
                taker: this.address, // Always use smart account's address as taker
                signerAddress: this.owners[0].address, // Always use owner's address as signer
            });
        },
        name: options.smartAccount.name,
        type: "evm-smart",
    };
    return account;
}
//# sourceMappingURL=toEvmSmartAccount.js.map