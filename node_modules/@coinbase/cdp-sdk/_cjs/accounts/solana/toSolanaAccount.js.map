{"version": 3, "file": "toSolanaAccount.js", "sourceRoot": "", "sources": ["../../../accounts/solana/toSolanaAccount.ts"], "names": [], "mappings": ";;AA6BA,0CAqCC;AAjED,4EAAsE;AACtE,wEAAkE;AAClE,gFAA0E;AAC1E,kEAAkF;AAgBlF;;;;;;;;GAQG;AACH,SAAgB,eAAe,CAC7B,SAA+B,EAC/B,OAA+B;IAE/B,MAAM,OAAO,GAAkB;QAC7B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO;QAChC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,KAAK,CAAC,aAAa,CAAC,OAA8C;YAChE,OAAO,IAAA,gCAAa,EAAC,SAAS,EAAE;gBAC9B,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,OAA4C;YAC5D,OAAO,IAAA,4BAAW,EAAC,SAAS,EAAE;gBAC5B,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,eAAe,CACnB,OAAgD;YAEhD,OAAO,IAAA,oCAAe,EAAC,SAAS,EAAE;gBAChC,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QACD,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;QAClC,KAAK,CAAC,QAAQ,CAAC,OAAsC;YACnD,OAAO,IAAA,sBAAQ,EAAC,SAAS,EAAE;gBACzB,GAAG,OAAO;gBACV,IAAI,EAAE,OAAO,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC;KACF,CAAC;IAEF,OAAO,OAAO,CAAC;AACjB,CAAC"}