"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toSolanaAccount = toSolanaAccount;
const requestFaucet_js_1 = require("../../actions/solana/requestFaucet.js");
const signMessage_js_1 = require("../../actions/solana/signMessage.js");
const signTransaction_js_1 = require("../../actions/solana/signTransaction.js");
const transfer_js_1 = require("../../actions/solana/transfer.js");
/**
 * Creates a Solana account instance with actions from an existing Solana account.
 * Use this to interact with previously deployed Solana accounts, rather than creating new ones.
 *
 * @param {CdpOpenApiClientType} apiClient - The API client.
 * @param {ToSolanaAccountOptions} options - Configuration options.
 * @param {Account} options.account - The Solana account that was previously created.
 * @returns {SolanaAccount} A configured SolanaAccount instance ready for signing.
 */
function toSolanaAccount(apiClient, options) {
    const account = {
        address: options.account.address,
        name: options.account.name,
        async requestFaucet(options) {
            return (0, requestFaucet_js_1.requestFaucet)(apiClient, {
                ...options,
                address: account.address,
            });
        },
        async signMessage(options) {
            return (0, signMessage_js_1.signMessage)(apiClient, {
                ...options,
                address: account.address,
            });
        },
        async signTransaction(options) {
            return (0, signTransaction_js_1.signTransaction)(apiClient, {
                ...options,
                address: account.address,
            });
        },
        policies: options.account.policies,
        async transfer(options) {
            return (0, transfer_js_1.transfer)(apiClient, {
                ...options,
                from: account.address,
            });
        },
    };
    return account;
}
//# sourceMappingURL=toSolanaAccount.js.map