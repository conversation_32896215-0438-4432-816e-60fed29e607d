"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePolicyBodySchema = exports.CreatePolicyBodySchema = exports.PolicyScopeEnum = exports.RuleSchema = exports.SignSolTransactionRuleSchema = exports.SendEvmTransactionRuleSchema = exports.SignEvmMessageRuleSchema = exports.SignEvmHashRuleSchema = exports.SignEvmTransactionRuleSchema = exports.ActionEnum = exports.EvmOperationEnum = exports.SolOperationEnum = exports.SignSolTransactionCriteriaSchema = exports.SendEvmTransactionCriteriaSchema = exports.SignEvmMessageCriteriaSchema = exports.SignEvmTransactionCriteriaSchema = exports.SolAddressCriterionSchema = exports.EvmMessageCriterionSchema = exports.EvmNetworkCriterionSchema = exports.EvmNetworkEnum = exports.EvmAddressCriterionSchema = exports.EthValueCriterionSchema = exports.SolAddressOperatorEnum = exports.EvmNetworkOperatorEnum = exports.EvmAddressOperatorEnum = exports.EthValueOperatorEnum = void 0;
const zod_1 = require("abitype/zod");
const zod_2 = require("zod");
/**
 * Enum for EthValueOperator values
 */
exports.EthValueOperatorEnum = zod_2.z.enum([">", ">=", "<", "<=", "=="]);
/**
 * Enum for EvmAddressOperator values
 */
exports.EvmAddressOperatorEnum = zod_2.z.enum(["in", "not in"]);
/**
 * Enum for EvmNetworkOperator values
 */
exports.EvmNetworkOperatorEnum = zod_2.z.enum(["in", "not in"]);
/**
 * Enum for SolAddressOperator values
 */
exports.SolAddressOperatorEnum = zod_2.z.enum(["in", "not in"]);
/**
 * Schema for ETH value criterions
 */
exports.EthValueCriterionSchema = zod_2.z.object({
    /** The type of criterion, must be "ethValue" for Ethereum value-based rules. */
    type: zod_2.z.literal("ethValue"),
    /**
     * The ETH value amount in wei to compare against, as a string.
     * Must contain only digits.
     */
    ethValue: zod_2.z.string().regex(/^[0-9]+$/),
    /** The comparison operator to use for evaluating transaction values against the threshold. */
    operator: exports.EthValueOperatorEnum,
});
/**
 * Schema for EVM address criterions
 */
exports.EvmAddressCriterionSchema = zod_2.z.object({
    /** The type of criterion, must be "evmAddress" for EVM address-based rules. */
    type: zod_2.z.literal("evmAddress"),
    /**
     * Array of EVM addresses to compare against.
     * Each address must be a 0x-prefixed 40-character hexadecimal string.
     * Limited to a maximum of 100 addresses per criterion.
     */
    addresses: zod_2.z.array(zod_1.Address).max(100),
    /**
     * The operator to use for evaluating transaction addresses.
     * "in" checks if an address is in the provided list.
     * "not in" checks if an address is not in the provided list.
     */
    operator: exports.EvmAddressOperatorEnum,
});
/**
 * Enum for EVM Network values
 */
exports.EvmNetworkEnum = zod_2.z.enum(["base", "base-sepolia"]);
/**
 * Schema for EVM network criterions
 */
exports.EvmNetworkCriterionSchema = zod_2.z.object({
    /** The type of criterion, must be "evmAddress" for EVM address-based rules. */
    type: zod_2.z.literal("evmNetwork"),
    /**
     * Array of EVM network identifiers to compare against.
     * Either "base" or "base-sepolia"
     */
    networks: zod_2.z.array(exports.EvmNetworkEnum),
    /**
     * The operator to use for evaluating transaction network.
     * "in" checks if a network is in the provided list.
     * "not in" checks if a network is not in the provided list.
     */
    operator: exports.EvmNetworkOperatorEnum,
});
/**
 * Schema for EVM message criterions
 */
exports.EvmMessageCriterionSchema = zod_2.z.object({
    /** The type of criterion, must be "evmMessage" for EVM message-based rules. */
    type: zod_2.z.literal("evmMessage"),
    /**
     * A regular expression the message is matched against.
     * Accepts valid regular expression syntax described by [RE2](https://github.com/google/re2/wiki/Syntax).
     */
    match: zod_2.z.string().min(1),
});
/**
 * Schema for Solana address criterions
 */
exports.SolAddressCriterionSchema = zod_2.z.object({
    /** The type of criterion, must be "solAddress" for Solana address-based rules. */
    type: zod_2.z.literal("solAddress"),
    /**
     * Array of Solana addresses to compare against.
     * Each address must be a valid Base58-encoded Solana address (32-44 characters).
     */
    addresses: zod_2.z.array(zod_2.z.string().regex(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/)),
    /**
     * The operator to use for evaluating transaction addresses.
     * "in" checks if an address is in the provided list.
     * "not in" checks if an address is not in the provided list.
     */
    operator: exports.SolAddressOperatorEnum,
});
/**
 * Schema for criteria used in SignEvmTransaction operations
 */
exports.SignEvmTransactionCriteriaSchema = zod_2.z
    .array(zod_2.z.discriminatedUnion("type", [exports.EthValueCriterionSchema, exports.EvmAddressCriterionSchema]))
    .max(10)
    .min(1);
/**
 * Schema for criteria used in SignEvmMessage operations
 */
exports.SignEvmMessageCriteriaSchema = zod_2.z
    .array(zod_2.z.discriminatedUnion("type", [exports.EvmMessageCriterionSchema]))
    .max(10)
    .min(1);
/**
 * Schema for criteria used in SendEvmTransaction operations
 */
exports.SendEvmTransactionCriteriaSchema = zod_2.z
    .array(zod_2.z.discriminatedUnion("type", [
    exports.EthValueCriterionSchema,
    exports.EvmAddressCriterionSchema,
    exports.EvmNetworkCriterionSchema,
]))
    .max(10)
    .min(1);
/**
 * Schema for criteria used in SignSolTransaction operations
 */
exports.SignSolTransactionCriteriaSchema = zod_2.z
    .array(zod_2.z.discriminatedUnion("type", [exports.SolAddressCriterionSchema]))
    .max(10)
    .min(1);
/**
 * Enum for Solana Operation types
 */
exports.SolOperationEnum = zod_2.z.enum(["signSolTransaction"]);
/**
 * Enum for Evm Operation types
 */
exports.EvmOperationEnum = zod_2.z.enum(["signEvmTransaction"]);
/**
 * Enum for Action types
 */
exports.ActionEnum = zod_2.z.enum(["reject", "accept"]);
/**
 * Type representing a 'signEvmTransaction' policy rule that can accept or reject specific operations
 * based on a set of criteria.
 */
exports.SignEvmTransactionRuleSchema = zod_2.z.object({
    /**
     * Determines whether matching the rule will cause a request to be rejected or accepted.
     * "accept" will allow the transaction, "reject" will block it.
     */
    action: exports.ActionEnum,
    /**
     * The operation to which this rule applies.
     * Must be "signEvmTransaction".
     */
    operation: zod_2.z.literal("signEvmTransaction"),
    /**
     * The set of criteria that must be matched for this rule to apply.
     * Must be compatible with the specified operation type.
     */
    criteria: exports.SignEvmTransactionCriteriaSchema,
});
/**
 * Type representing a 'signEvmHash' policy rule that can accept or reject specific operations
 * based on a set of criteria.
 */
exports.SignEvmHashRuleSchema = zod_2.z.object({
    /**
     * Determines whether matching the rule will cause a request to be rejected or accepted.
     * "accept" will allow the signing, "reject" will block it.
     */
    action: exports.ActionEnum,
    /**
     * The operation to which this rule applies.
     * Must be "signEvmHash".
     */
    operation: zod_2.z.literal("signEvmHash"),
});
/**
 * Type representing a 'signEvmMessage' policy rule that can accept or reject specific operations
 * based on a set of criteria.
 */
exports.SignEvmMessageRuleSchema = zod_2.z.object({
    /**
     * Determines whether matching the rule will cause a request to be rejected or accepted.
     * "accept" will allow the signing, "reject" will block it.
     */
    action: exports.ActionEnum,
    /**
     * The operation to which this rule applies.
     * Must be "signEvmMessage".
     */
    operation: zod_2.z.literal("signEvmMessage"),
    /**
     * The set of criteria that must be matched for this rule to apply.
     * Must be compatible with the specified operation type.
     */
    criteria: exports.SignEvmMessageCriteriaSchema,
});
/**
 * Type representing a 'sendEvmTransaction' policy rule that can accept or reject specific operations
 * based on a set of criteria.
 */
exports.SendEvmTransactionRuleSchema = zod_2.z.object({
    /**
     * Determines whether matching the rule will cause a request to be rejected or accepted.
     * "accept" will allow the transaction, "reject" will block it.
     */
    action: exports.ActionEnum,
    /**
     * The operation to which this rule applies.
     * Must be "sendEvmTransaction".
     */
    operation: zod_2.z.literal("sendEvmTransaction"),
    /**
     * The set of criteria that must be matched for this rule to apply.
     * Must be compatible with the specified operation type.
     */
    criteria: exports.SendEvmTransactionCriteriaSchema,
});
/**
 * Type representing a 'signSolTransaction' policy rule that can accept or reject specific operations
 * based on a set of criteria.
 */
exports.SignSolTransactionRuleSchema = zod_2.z.object({
    /**
     * Determines whether matching the rule will cause a request to be rejected or accepted.
     * "accept" will allow the transaction, "reject" will block it.
     */
    action: exports.ActionEnum,
    /**
     * The operation to which this rule applies.
     * Must be "signSolTransaction".
     */
    operation: zod_2.z.literal("signSolTransaction"),
    /**
     * The set of criteria that must be matched for this rule to apply.
     * Must be compatible with the specified operation type.
     */
    criteria: exports.SignSolTransactionCriteriaSchema,
});
/**
 * Schema for policy rules
 */
exports.RuleSchema = zod_2.z.discriminatedUnion("operation", [
    exports.SignEvmTransactionRuleSchema,
    exports.SignEvmHashRuleSchema,
    exports.SignEvmMessageRuleSchema,
    exports.SendEvmTransactionRuleSchema,
    exports.SignSolTransactionRuleSchema,
]);
/**
 * Enum for policy scopes
 */
exports.PolicyScopeEnum = zod_2.z.enum(["project", "account"]);
/**
 * Schema for creating or updating a Policy.
 */
exports.CreatePolicyBodySchema = zod_2.z.object({
    /**
     * The scope of the policy.
     * "project" applies to the entire project, "account" applies to specific accounts.
     */
    scope: exports.PolicyScopeEnum,
    /**
     * An optional human-readable description for the policy.
     * Limited to 50 characters of alphanumeric characters, spaces, commas, and periods.
     */
    description: zod_2.z
        .string()
        .regex(/^[A-Za-z0-9 ,.]{1,50}$/)
        .optional(),
    /**
     * Array of rules that comprise the policy.
     * Limited to a maximum of 10 rules per policy.
     */
    rules: zod_2.z.array(exports.RuleSchema).max(10).min(1),
});
exports.UpdatePolicyBodySchema = zod_2.z.object({
    /**
     * An optional human-readable description for the policy.
     * Limited to 50 characters of alphanumeric characters, spaces, commas, and periods.
     */
    description: zod_2.z
        .string()
        .regex(/^[A-Za-z0-9 ,.]{1,50}$/)
        .optional(),
    /**
     * Array of rules that comprise the policy.
     * Limited to a maximum of 10 rules per policy.
     */
    rules: zod_2.z.array(exports.RuleSchema).max(10).min(1),
});
//# sourceMappingURL=schema.js.map