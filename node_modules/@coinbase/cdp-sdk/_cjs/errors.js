"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeoutError = void 0;
/**
 * TimeoutError is thrown when an operation times out.
 */
class TimeoutError extends Error {
    /**
     * Initializes a new TimeoutError instance.
     *
     * @param message - The error message.
     */
    constructor(message = "Timeout Error") {
        super(message);
        this.name = "TimeoutError";
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, TimeoutError);
        }
    }
}
exports.TimeoutError = TimeoutError;
//# sourceMappingURL=errors.js.map