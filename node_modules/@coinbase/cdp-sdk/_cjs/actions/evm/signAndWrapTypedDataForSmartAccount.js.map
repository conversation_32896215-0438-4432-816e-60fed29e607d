{"version": 3, "file": "signAndWrapTypedDataForSmartAccount.js", "sourceRoot": "", "sources": ["../../../actions/evm/signAndWrapTypedDataForSmartAccount.ts"], "names": [], "mappings": ";;AAqGA,kFA8BC;AAmBD,8DAkCC;AAcD,gFAuBC;AA7ND,+BAAkF;AAiClF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmEG;AACI,KAAK,UAAU,mCAAmC,CACvD,MAA4B,EAC5B,OAAmD;IAEnD,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IAEtE,oCAAoC;IACpC,MAAM,mBAAmB,GAAG,yBAAyB,CAAC;QACpD,SAAS;QACT,OAAO;QACP,mBAAmB,EAAE,YAAY,CAAC,OAAc;KACjD,CAAC,CAAC;IAEH,+DAA+D;IAC/D,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;IACtD,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAC7C,KAAK,CAAC,OAAO,EACb,mBAAmB,EACnB,OAAO,CAAC,cAAc,CACvB,CAAC;IAEF,kEAAkE;IAClE,MAAM,gBAAgB,GAAG,kCAAkC,CAAC;QAC1D,YAAY,EAAE,SAAS,CAAC,SAAgB;QACxC,UAAU;KACX,CAAC,CAAC;IAEH,OAAO;QACL,SAAS,EAAE,gBAAuB;KACnC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,yBAAyB,CAAC,EACxC,SAAS,EACT,OAAO,EACP,mBAAmB,GAKpB;IACC,qCAAqC;IACrC,MAAM,YAAY,GAAG,IAAA,oBAAa,EAAC,SAAS,CAAC,CAAC;IAE9C,yDAAyD;IACzD,OAAO;QACL,MAAM,EAAE;YACN,IAAI,EAAE,uBAAuB;YAC7B,OAAO,EAAE,GAAG;YACZ,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;YACxB,iBAAiB,EAAE,mBAAmB;SACvC;QACD,KAAK,EAAE;YACL,YAAY,EAAE;gBACZ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAChC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACnC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;gBACpC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,SAAS,EAAE;aAC/C;YACD,0BAA0B,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;SAChE;QACD,WAAW,EAAE,4BAAqC;QAClD,OAAO,EAAE;YACP,IAAI,EAAE,YAAY;SACnB;KACF,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;GAWG;AACH,SAAgB,kCAAkC,CAAC,EACjD,YAAY,EACZ,UAAU,GAIX;IACC,8EAA8E;IAC9E,MAAM,CAAC,GAAG,IAAA,eAAQ,EAAC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACxC,MAAM,CAAC,GAAG,IAAA,eAAQ,EAAC,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACzC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,oBAAoB;IAE3E,MAAM,aAAa,GAAG,IAAA,mBAAY,EAAC,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAE/E,OAAO,IAAA,0BAAmB,EACxB,CAAC,sBAAsB,CAAC,EACxB;QACE;YACE,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC;YAC9B,aAAa;SACd;KACF,CACK,CAAC;AACX,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,sBAAsB,GAAG;IAC7B,UAAU,EAAE;QACV;YACE,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,OAAO;SACd;QACD;YACE,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,OAAO;SACd;KACF;IACD,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE,OAAO;CACL,CAAC"}