"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.quoteFund = quoteFund;
const viem_1 = require("viem");
const Quote_js_1 = require("./Quote.js");
const index_js_1 = require("../../../openapi-client/index.js");
/**
 * Gets a quote to fund an EVM account.
 *
 * @param apiClient - The API client.
 * @param options - The options for getting a quote to fund an EVM account.
 *
 * @returns A promise that resolves to the quote.
 */
async function quoteFund(apiClient, options) {
    const paymentMethods = await apiClient.getPaymentMethods();
    const cardPaymentMethod = paymentMethods.find(method => method.type === "card" && method.actions.includes("source"));
    if (!cardPaymentMethod) {
        throw new Error("No card found to fund account");
    }
    if (options.token.toLowerCase() !== "eth" && options.token.toLowerCase() !== "usdc") {
        throw new Error("Invalid currency, must be eth or usdc");
    }
    const decimals = options.token === "eth" ? 18 : 6;
    const amount = (0, viem_1.formatUnits)(options.amount, decimals);
    const response = await apiClient.createPaymentTransferQuote({
        sourceType: index_js_1.CreatePaymentTransferQuoteBodySourceType.payment_method,
        source: {
            id: cardPaymentMethod.id,
        },
        targetType: index_js_1.CreatePaymentTransferQuoteBodyTargetType.crypto_rail,
        target: {
            currency: options.token,
            network: options.network,
            address: options.address,
        },
        amount,
        currency: options.token,
    });
    return new Quote_js_1.Quote(apiClient, response.transfer.id, options.network, response.transfer.sourceAmount, response.transfer.sourceCurrency, response.transfer.targetAmount, response.transfer.targetCurrency, response.transfer.fees.map(fee => ({
        type: fee.type,
        amount: fee.amount,
        currency: fee.currency,
    })));
}
//# sourceMappingURL=quoteFund.js.map