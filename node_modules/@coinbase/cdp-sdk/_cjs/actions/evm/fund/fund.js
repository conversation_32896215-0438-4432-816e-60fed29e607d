"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fund = fund;
const viem_1 = require("viem");
const index_js_1 = require("../../../openapi-client/index.js");
/**
 * Funds an EVM account.
 *
 * @param apiClient - The API client.
 * @param options - The options for funding an EVM account.
 *
 * @returns A promise that resolves to the fund operation result.
 */
async function fund(apiClient, options) {
    const paymentMethods = await apiClient.getPaymentMethods();
    const cardPaymentMethod = paymentMethods.find(method => method.type === "card" && method.actions.includes("source"));
    if (!cardPaymentMethod) {
        throw new Error("No card found to fund account");
    }
    if (options.token.toLowerCase() !== "eth" && options.token.toLowerCase() !== "usdc") {
        throw new Error("Invalid currency, must be eth or usdc");
    }
    const decimals = options.token === "eth" ? 18 : 6;
    const amount = (0, viem_1.formatUnits)(options.amount, decimals);
    const response = await apiClient.createPaymentTransferQuote({
        sourceType: index_js_1.CreatePaymentTransferQuoteBodySourceType.payment_method,
        source: {
            id: cardPaymentMethod.id,
        },
        targetType: index_js_1.CreatePaymentTransferQuoteBodyTargetType.crypto_rail,
        target: {
            currency: options.token,
            network: options.network,
            address: options.address,
        },
        amount,
        currency: options.token,
        execute: true,
    });
    return {
        id: response.transfer.id,
        network: response.transfer.target.network,
        status: response.transfer.status,
        targetAmount: response.transfer.targetAmount,
        targetCurrency: response.transfer.targetCurrency,
        transactionHash: response.transfer.transactionHash,
    };
}
//# sourceMappingURL=fund.js.map