{"version": 3, "file": "createSwapQuote.js", "sourceRoot": "", "sources": ["../../../../actions/evm/swap/createSwapQuote.ts"], "names": [], "mappings": ";;AAsCA,0CAsIC;AA5KD,iEAA2D;AAC3D,qEAA+D;AAkB/D;;;;;;;;;;;;;;;;;;GAkBG;AACI,KAAK,UAAU,eAAe,CACnC,MAA4B,EAC5B,OAA+B;IAE/B,+BAA+B;IAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC3D,CAAC;IAED,wCAAwC;IACxC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAgB,CAAC;IAEvC,iFAAiF;IACjF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAC9C;QACE,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,SAAS,EAAE,OAAO,CAAC,SAAS;QAC5B,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE;QACzC,KAAK,EAAE,KAAK;QACZ,aAAa,EAAE,OAAO,CAAC,aAAa;QACpC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE;QACtC,WAAW,EAAE,OAAO,CAAC,WAAW;KACjC,EACD,OAAO,CAAC,cAAc,CACvB,CAAC;IAEF,oCAAoC;IACpC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;QACjC,mCAAmC;QACnC,OAAO;YACL,kBAAkB,EAAE,KAAK;SAC1B,CAAC;IACJ,CAAC;IAED,uFAAuF;IACvF,MAAM,YAAY,GAAG,QAAmC,CAAC;IACzD,MAAM,MAAM,GAA0B;QACpC,kBAAkB,EAAE,IAAI;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,YAAY,CAAC,OAAkB;QACxC,SAAS,EAAE,YAAY,CAAC,SAAoB;QAC5C,UAAU,EAAE,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;QAC3C,QAAQ,EAAE,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;QACvC,WAAW,EAAE,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC;QAC7C,WAAW,EAAE,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC;QAC7C,IAAI,EAAE;YACJ,MAAM,EAAE,YAAY,CAAC,IAAI,CAAC,MAAM;gBAC9B,CAAC,CAAC;oBACE,MAAM,EAAE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;oBAC/C,KAAK,EAAE,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,KAAgB;iBACjD;gBACH,CAAC,CAAC,SAAS;YACb,WAAW,EAAE,YAAY,CAAC,IAAI,CAAC,WAAW;gBACxC,CAAC,CAAC;oBACE,MAAM,EAAE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBACpD,KAAK,EAAE,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,KAAgB;iBACtD;gBACH,CAAC,CAAC,SAAS;SACd;QACD,MAAM,EAAE;YACN,SAAS,EAAE,YAAY,CAAC,MAAM,CAAC,SAAS;gBACtC,CAAC,CAAC;oBACE,gBAAgB,EAAE,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;oBACxE,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,OAAkB;iBAC1D;gBACH,CAAC,CAAC,SAAS;YACb,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,OAAO;gBAClC,CAAC,CAAC;oBACE,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAgB;oBACnD,cAAc,EAAE,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC;oBAClE,eAAe,EAAE,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;iBACrE;gBACH,CAAC,CAAC,SAAS;YACb,oBAAoB,EAAE,YAAY,CAAC,MAAM,CAAC,oBAAoB;SAC/D;QACD,WAAW,EAAE,YAAY,CAAC,WAAW;YACnC,CAAC,CAAC;gBACE,EAAE,EAAE,YAAY,CAAC,WAAW,CAAC,EAAa;gBAC1C,IAAI,EAAE,YAAY,CAAC,WAAW,CAAC,IAAW;gBAC1C,KAAK,EAAE,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC;gBAC7C,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC;gBACzC,QAAQ,EAAE,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC;aACpD;YACH,CAAC,CAAC,SAAS;QACb,OAAO,EAAE,YAAY,CAAC,OAAO;YAC3B,CAAC,CAAC;gBACE,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM;wBACrC,iBAAiB,EAAE,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,iBAEzC;wBACb,IAAI,EAAE,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAuB;qBACjE;oBACD,KAAK,EAAE,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;oBACxC,WAAW,EAAE,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW;oBACpD,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO;iBAC7C;aACF;YACH,CAAC,CAAC,SAAS;QACb,yBAAyB;QACzB,OAAO,EAAE,KAAK,EACZ,iBAA0C,EAAE,EACX,EAAE;YACnC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,kDAAkD;gBAClD,MAAM,YAAY,GAAG,MAAM,IAAA,wCAAiB,EAAC,MAAM,EAAE;oBACnD,YAAY,EAAE,OAAO,CAAC,YAA+B;oBACrD,OAAO,EAAE,MAAM,CAAC,OAAkC;oBAClD,SAAS,EAAE,MAAM;oBACjB,cAAc,EAAE,cAAc,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBAEH,OAAO;oBACL,UAAU,EAAE,YAAY,CAAC,UAAU;oBACnC,mBAAmB,EAAE,YAAY,CAAC,mBAAmB;oBACrD,MAAM,EAAE,YAAY,CAAC,MAAM;iBAC5B,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,0CAA0C;gBAC1C,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,IAAA,4CAAmB,EAAC,MAAM,EAAE;oBAC5D,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,MAAM,CAAC,OAAwC;oBACxD,SAAS,EAAE,MAAM;oBACjB,cAAc,EAAE,cAAc,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBAEH,OAAO,EAAE,eAAe,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;KACF,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC"}