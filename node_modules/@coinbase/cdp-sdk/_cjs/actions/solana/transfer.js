"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transfer = transfer;
const spl_token_1 = require("@solana/spl-token");
const web3_js_1 = require("@solana/web3.js");
const utils_js_1 = require("./utils.js");
/**
 * Transfers SOL or SPL tokens between accounts
 *
 * @param apiClient - The API client to use
 * @param options - The transfer options
 *
 * @returns The transfer result
 */
async function transfer(apiClient, options) {
    const connection = (0, utils_js_1.getOrCreateConnection)({ networkOrConnection: options.network });
    const tx = options.token === "sol"
        ? await getNativeTransfer({
            connection,
            from: options.from,
            to: options.to,
            amount: options.amount,
        })
        : await getSplTransfer({
            connection,
            from: options.from,
            to: options.to,
            mintAddress: options.token === "usdc"
                ? (0, utils_js_1.getUsdcMintAddress)(await (0, utils_js_1.getConnectedNetwork)(connection))
                : options.token,
            amount: options.amount,
        });
    const serializedTx = Buffer.from(tx.serialize()).toString("base64");
    const signedTxResponse = await apiClient.signSolanaTransaction(options.from, {
        transaction: serializedTx,
    });
    const decodedSignedTx = Buffer.from(signedTxResponse.signedTransaction, "base64");
    const signature = await connection.sendRawTransaction(decodedSignedTx, {
        skipPreflight: false,
        maxRetries: 3,
    });
    return { signature };
}
/**
 * Gets the instructions for a native SOL transfer
 *
 * @param options - The options for the native SOL transfer
 *
 * @param options.connection - The Solana connection
 * @param options.from - The source address
 * @param options.to - The destination address
 * @param options.amount - The amount to transfer
 *
 * @returns The native SOL transfer transaction
 */
async function getNativeTransfer({ connection, from, to, amount, }) {
    const { blockhash } = await connection.getLatestBlockhash();
    const instructions = [
        web3_js_1.SystemProgram.transfer({
            fromPubkey: new web3_js_1.PublicKey(from),
            toPubkey: new web3_js_1.PublicKey(to),
            lamports: amount,
        }),
    ];
    const messageV0 = new web3_js_1.TransactionMessage({
        payerKey: new web3_js_1.PublicKey(from),
        recentBlockhash: blockhash,
        instructions,
    }).compileToV0Message();
    return new web3_js_1.VersionedTransaction(messageV0);
}
/**
 * Gets the instructions for a SPL token transfer
 *
 * @param options - The options for the SPL token transfer
 *
 * @param options.connection - The Solana connection
 * @param options.from - The source address
 * @param options.to - The destination address
 * @param options.mintAddress - The mint address of the token
 * @param options.amount - The amount to transfer
 *
 * @returns The SPL token transfer transaction
 */
async function getSplTransfer({ connection, from, to, mintAddress, amount, }) {
    const fromPubkey = new web3_js_1.PublicKey(from);
    const toPubkey = new web3_js_1.PublicKey(to);
    const mintPubkey = new web3_js_1.PublicKey(mintAddress);
    let mintInfo;
    try {
        mintInfo = await (0, spl_token_1.getMint)(connection, mintPubkey);
    }
    catch (error) {
        throw new Error(`Failed to fetch mint info for mint address ${mintAddress}. Error: ${error}`);
    }
    const sourceAta = await (0, spl_token_1.getAssociatedTokenAddress)(mintPubkey, fromPubkey);
    const destinationAta = await (0, spl_token_1.getAssociatedTokenAddress)(mintPubkey, toPubkey);
    const instructions = [];
    const sourceAccount = await (0, spl_token_1.getAccount)(connection, sourceAta);
    if (sourceAccount.amount < amount) {
        throw new Error(`Insufficient token balance. Have ${sourceAccount.amount}, need ${amount}`);
    }
    // Check if destination account exists, if not create it
    try {
        await (0, spl_token_1.getAccount)(connection, destinationAta);
    }
    catch {
        instructions.push((0, spl_token_1.createAssociatedTokenAccountInstruction)(fromPubkey, destinationAta, toPubkey, mintPubkey));
    }
    instructions.push((0, spl_token_1.createTransferCheckedInstruction)(sourceAta, mintPubkey, destinationAta, fromPubkey, amount, mintInfo.decimals));
    return new web3_js_1.VersionedTransaction(web3_js_1.MessageV0.compile({
        payerKey: fromPubkey,
        instructions: instructions,
        recentBlockhash: (await connection.getLatestBlockhash()).blockhash,
    }));
}
//# sourceMappingURL=transfer.js.map