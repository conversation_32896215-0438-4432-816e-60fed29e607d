"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOrCreateConnection = getOrCreateConnection;
exports.getConnectedNetwork = getConnectedNetwork;
exports.getUsdcMintAddress = getUsdcMintAddress;
const web3_js_1 = require("@solana/web3.js");
const constants_js_1 = require("./constants.js");
/**
 * Get a connection to the Solana network
 *
 * @param options - The options for the connection
 *
 * @param options.networkOrConnection - The network to use or a connection
 *
 * @returns The connection
 */
function getOrCreateConnection({ networkOrConnection, }) {
    if (typeof networkOrConnection !== "string") {
        return networkOrConnection;
    }
    return new web3_js_1.Connection(networkOrConnection === "mainnet"
        ? "https://api.mainnet-beta.solana.com"
        : networkOrConnection === "devnet"
            ? "https://api.devnet.solana.com"
            : "https://api.testnet.solana.com");
}
/**
 * Get the network of the connected Solana node
 *
 * @param connection - The connection to the Solana network
 * @throws {Error} If the network is not mainnet, devnet, or testnet
 *
 * @returns The network of the connected Solana node
 */
async function getConnectedNetwork(connection) {
    const genesisHash = await connection.getGenesisHash();
    if (genesisHash === constants_js_1.GENESIS_HASH_MAINNET) {
        return "mainnet";
    }
    else if (genesisHash === constants_js_1.GENESIS_HASH_DEVNET) {
        return "devnet";
    }
    else if (genesisHash === constants_js_1.GENESIS_HASH_TESTNET) {
        return "testnet";
    }
    throw new Error("Unknown network");
}
/**
 * Get the USDC mint address for the given connection
 *
 * @param network - The network to use
 *
 * @returns The USDC mint address
 */
function getUsdcMintAddress(network) {
    if (network === "mainnet") {
        return constants_js_1.USDC_MAINNET_MINT_ADDRESS;
    }
    else if (network === "devnet") {
        return constants_js_1.USDC_DEVNET_MINT_ADDRESS;
    }
    else {
        throw new Error("Testnet is not supported for USDC");
    }
}
//# sourceMappingURL=utils.js.map