{"version": 3, "file": "analytics.js", "sourceRoot": "", "sources": ["../analytics.ts"], "names": [], "mappings": ";;;;;;AAAA,8CAAsB;AAEtB,0DAAqE;AACrE,6CAAuC;AA0BvC,uDAAuD;AACvD,MAAM,cAAc,GAAG,kCAAkC,CAAC;AAE7C,QAAA,SAAS,GAAG;IACvB,UAAU,EAAE,EAAE,EAAE,gBAAgB;IAChC,0BAA0B;IAC1B,kCAAkC;IAClC,SAAS;CACV,CAAC;AAEF;;;;;GAKG;AACH,KAAK,UAAU,SAAS,CAAC,KAAgB;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM,EAAE,CAAC;QACvD,OAAO;IACT,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,MAAM,aAAa,GAAG;QACpB,OAAO,EAAE,iBAAS,CAAC,UAAU;QAC7B,UAAU,EAAE,KAAK,CAAC,IAAI;QACtB,QAAQ,EAAE,QAAQ;QAClB,SAAS;QACT,gBAAgB,EAAE;YAChB,YAAY,EAAE,SAAS;YACvB,gBAAgB,EAAE,YAAY;YAC9B,OAAO,EAAP,oBAAO;YACP,GAAG,KAAK;SACT;KACF,CAAC;IAEF,MAAM,MAAM,GAAG,CAAC,aAAa,CAAC,CAAC;IAC/B,MAAM,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACpD,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;IAExC,MAAM,QAAQ,GAAG,IAAA,aAAG,EAAC,oBAAoB,GAAG,UAAU,CAAC,CAAC;IAExD,MAAM,oBAAoB,GAAG;QAC3B,MAAM,EAAE,cAAc;QACtB,CAAC,EAAE,oBAAoB;QACvB,QAAQ;KACT,CAAC;IAEF,MAAM,WAAW,GAAG,+BAA+B,CAAC;IACpD,MAAM,SAAS,GAAG,MAAM,CAAC;IACzB,MAAM,aAAa,GAAG,GAAG,WAAW,GAAG,SAAS,EAAE,CAAC;IAEnD,MAAM,KAAK,CAAC,aAAa,EAAE;QACzB,MAAM,EAAE,MAAM;QACd,IAAI,EAAE,SAAS;QACf,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;SACnC;QACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;KAC3C,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,8DAA8D;AAC9D,SAAS,0BAA0B,CAAC,WAAgB;IAClD,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM,EAAE,CAAC;QACvD,OAAO;IACT,CAAC;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,MAAM,CACtE,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,aAAa,IAAI,OAAO,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,UAAU,CACpF,CAAC;IAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,MAAM,cAAc,GAAG,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACrD,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,WAAW,GAAG,IAAe;YAChE,IAAI,CAAC;gBACH,OAAO,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7B,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,KAAc,CAAC;gBAE1C,SAAS,CAAC;oBACR,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,IAAI,EAAE,OAAO;iBACd,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;oBACZ,eAAe;gBACjB,CAAC,CAAC,CAAC;gBAEH,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,8DAA8D;AAC9D,SAAS,kCAAkC,CAAC,MAAW;IACrD,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM,EAAE,CAAC;QACvD,OAAO;IACT,CAAC;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,MAAM,CACvD,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,aAAa,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,UAAU,CACrE,CAAC;IAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QACtC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,WAAW,GAAG,IAAe;YACjD,IAAI,CAAC;gBACH,OAAO,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7B,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,KAAc,CAAC;gBAE1C,SAAS,CAAC;oBACR,MAAM;oBACN,OAAO;oBACP,KAAK;oBACL,IAAI,EAAE,OAAO;iBACd,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;oBACZ,eAAe;gBACjB,CAAC,CAAC,CAAC;gBAEH,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,gBAAgB,CAAC,KAAc;IACtC,IAAI,CAAC,CAAC,KAAK,YAAY,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,KAAK,YAAY,oBAAQ,IAAI,KAAK,CAAC,SAAS,KAAK,yBAAa,CAAC,gBAAgB,EAAE,CAAC;QACpF,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}