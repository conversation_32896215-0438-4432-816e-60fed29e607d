"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePolicyBodySchema = exports.CreatePolicyBodySchema = exports.CdpClient = void 0;
var cdp_js_1 = require("./client/cdp.js");
Object.defineProperty(exports, "CdpClient", { enumerable: true, get: function () { return cdp_js_1.CdpClient; } });
var schema_js_1 = require("./policies/schema.js");
Object.defineProperty(exports, "CreatePolicyBodySchema", { enumerable: true, get: function () { return schema_js_1.CreatePolicyBodySchema; } });
Object.defineProperty(exports, "UpdatePolicyBodySchema", { enumerable: true, get: function () { return schema_js_1.UpdatePolicyBodySchema; } });
//# sourceMappingURL=index.js.map