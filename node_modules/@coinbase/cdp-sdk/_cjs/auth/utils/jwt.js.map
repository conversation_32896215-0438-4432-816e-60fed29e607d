{"version": 3, "file": "jwt.js", "sourceRoot": "", "sources": ["../../../auth/utils/jwt.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuGA,kCAiEC;AAWD,8CAiCC;AApND,+CAAiC;AACjC,mCAA0C;AAE1C,+BAAmE;AAEnE,4CAA0F;AAwF1F;;;;;;;;;GASG;AACI,KAAK,UAAU,WAAW,CAAC,OAAmB;IACnD,+BAA+B;IAC/B,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC1C,CAAC;IACD,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAED,gEAAgE;IAChE,MAAM,mBAAmB,GAAG,OAAO,CACjC,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CACpE,CAAC;IACF,MAAM,kBAAkB,GACtB,CAAC,OAAO,CAAC,aAAa,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,KAAK,IAAI,CAAC;QACvE,CAAC,OAAO,CAAC,WAAW,KAAK,SAAS,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC;QACnE,CAAC,OAAO,CAAC,WAAW,KAAK,SAAS,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,CAAC;IAEtE,uEAAuE;IACvE,IAAI,CAAC,mBAAmB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAChD,MAAM,IAAI,KAAK,CACb,mIAAmI,CACpI,CAAC;IACJ,CAAC;IAED,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1C,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,CAAC,0CAA0C;IAEtF,0BAA0B;IAC1B,MAAM,MAAM,GAAe;QACzB,GAAG,EAAE,OAAO,CAAC,QAAQ;QACrB,GAAG,EAAE,KAAK;QACV,GAAG,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC;KACzC,CAAC;IAEF,gDAAgD;IAChD,IAAI,mBAAmB,EAAE,CAAC;QACxB,MAAM,CAAC,IAAI,GAAG,CAAC,GAAG,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,uCAAuC;IACvC,MAAM,WAAW,GAAG,KAAK,EAAE,CAAC;IAE5B,qEAAqE;IACrE,IAAI,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QACvC,OAAO,MAAM,UAAU,CACrB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,QAAQ,EAChB,MAAM,EACN,GAAG,EACH,SAAS,EACT,WAAW,CACZ,CAAC;IACJ,CAAC;SAAM,IAAI,iBAAiB,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QACnD,OAAO,MAAM,eAAe,CAC1B,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,QAAQ,EAChB,MAAM,EACN,GAAG,EACH,SAAS,EACT,WAAW,CACZ,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;IAC1F,CAAC;AACH,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,iBAAiB,CAAC,OAAyB;IAC/D,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;QAC1B,MAAM,IAAI,sCAA0B,CAAC,8BAA8B,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IACpF,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAE1C,MAAM,MAAM,GAAe;QACzB,IAAI,EAAE,CAAC,GAAG,CAAC;KACZ,CAAC;IAEF,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChD,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC;IACnC,CAAC;IAED,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,IAAA,yBAAgB,EAAC;YAC7B,GAAG,EAAE,OAAO,CAAC,YAAY;YACzB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;QAEH,OAAO,MAAM,IAAI,cAAO,CAAC,MAAM,CAAC;aAC7B,kBAAkB,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;aAChD,WAAW,CAAC,GAAG,CAAC;aAChB,YAAY,CAAC,GAAG,CAAC;aACjB,MAAM,CAAC,KAAK,EAAE,CAAC;aACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,0CAA8B,CAAC,+BAA+B,GAAG,KAAK,CAAC,CAAC;IACpF,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,iBAAiB,CAAC,GAAW;IACpC,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAC3C,OAAO,OAAO,CAAC,MAAM,KAAK,EAAE,CAAC;IAC/B,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,YAAY,CAAC,GAAW;IAC/B,IAAI,CAAC;QACH,iEAAiE;QACjE,MAAM,GAAG,GAAG,IAAA,yBAAgB,EAAC,GAAG,CAAC,CAAC;QAClC,+DAA+D;QAC/D,OAAO,GAAG,CAAC,iBAAiB,KAAK,IAAI,CAAC;IACxC,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;;;;;;;;;GAWG;AACH,KAAK,UAAU,UAAU,CACvB,UAAkB,EAClB,OAAe,EACf,MAAkB,EAClB,GAAW,EACX,SAAiB,EACjB,KAAa;IAEb,IAAI,CAAC;QACH,0BAA0B;QAC1B,MAAM,MAAM,GAAG,IAAA,yBAAgB,EAAC,UAAU,CAAC,CAAC;QAC5C,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;QAE5E,6BAA6B;QAC7B,MAAM,KAAK,GAAG,MAAM,IAAA,kBAAW,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAEnD,0BAA0B;QAC1B,OAAO,MAAM,IAAI,cAAO,CAAC,MAAM,CAAC;aAC7B,kBAAkB,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;aACrE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC5B,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC7B,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC;aAC9C,IAAI,CAAC,KAAK,CAAC,CAAC;IACjB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,8BAA+B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;IAC5E,CAAC;AACH,CAAC;AAED;;;;;;;;;;;GAWG;AACH,KAAK,UAAU,eAAe,CAC5B,UAAkB,EAClB,OAAe,EACf,MAAkB,EAClB,GAAW,EACX,SAAiB,EACjB,KAAa;IAEb,IAAI,CAAC;QACH,8EAA8E;QAC9E,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAClD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrC,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAEvC,qCAAqC;QACrC,MAAM,GAAG,GAAG;YACV,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,SAAS;YACd,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YAC7B,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC;SACnC,CAAC;QAEF,6BAA6B;QAC7B,MAAM,GAAG,GAAG,MAAM,IAAA,gBAAS,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAE1C,0BAA0B;QAC1B,OAAO,MAAM,IAAI,cAAO,CAAC,MAAM,CAAC;aAC7B,kBAAkB,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;aACrE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC5B,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC7B,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC;aAC9C,IAAI,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,mCAAoC,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;IACjF,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,KAAK;IACZ,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChD,CAAC"}