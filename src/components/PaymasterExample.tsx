"use client";

import React, { useState } from 'react';
import { Transaction, TransactionButton } from './Transaction';
import { PayButton } from './PayButton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Zap, CheckCircle, AlertCircle } from 'lucide-react';
import { BASE_SEPOLIA_CHAIN_ID, getPaymasterUrl } from '@/lib/wagmiConfig';

export function PaymasterExample() {
  const [lastTxHash, setLastTxHash] = useState<string>('');
  const [lastError, setLastError] = useState<string>('');

  const handleSuccess = (hash: string) => {
    setLastTxHash(hash);
    setLastError('');
    console.log('Transaction successful:', hash);
  };

  const handleError = (error: string) => {
    setLastError(error);
    setLastTxHash('');
    console.error('Transaction failed:', error);
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5 text-yellow-500" />
            Paymaster Integration Demo
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">What is Paymaster?</h3>
            <p className="text-sm text-blue-700">
              Paymaster allows gas sponsorship for transactions, meaning users don't need to pay gas fees. 
              This is especially useful for CDP Smart Wallets and improves user experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Configuration</h4>
              <div className="text-sm space-y-1">
                <div>Chain: <Badge variant="outline">Base Sepolia</Badge></div>
                <div>Paymaster URL: <code className="text-xs bg-gray-100 px-1 rounded">{getPaymasterUrl()}</code></div>
                <div>Chain ID: <code className="text-xs bg-gray-100 px-1 rounded">{BASE_SEPOLIA_CHAIN_ID}</code></div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Status</h4>
              {lastTxHash && (
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">Last TX: {lastTxHash.slice(0, 10)}...</span>
                </div>
              )}
              {lastError && (
                <div className="flex items-center gap-2 text-red-600">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">{lastError}</span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Example 1: Transaction Wrapper with Paymaster</CardTitle>
        </CardHeader>
        <CardContent>
          <Transaction
            chainId={BASE_SEPOLIA_CHAIN_ID}
            capabilities={{
              paymasterService: { url: getPaymasterUrl() }
            }}
            onSuccess={handleSuccess}
            onError={handleError}
          >
            <TransactionButton 
              text="Send 1 USDC with Gas Sponsorship"
              recipient={process.env.NEXT_PUBLIC_PAYMENT_RECIPIENT}
              amount="1.0"
            />
          </Transaction>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Example 2: Direct PayButton Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <PayButton
            chainId={BASE_SEPOLIA_CHAIN_ID}
            capabilities={{
              paymasterService: { url: getPaymasterUrl() }
            }}
            text="Send 0.1 USDC (Direct)"
            amount="0.1"
            recipient={process.env.NEXT_PUBLIC_PAYMENT_RECIPIENT}
            onSuccess={handleSuccess}
            onError={handleError}
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Example 3: Custom Contract Call with Paymaster</CardTitle>
        </CardHeader>
        <CardContent>
          <Transaction
            contracts={[
              {
                address: '0x036CbD53842c5426634e7929541eC2318f3dCF7e', // USDC Base Sepolia
                abi: [
                  {
                    name: 'transfer',
                    type: 'function',
                    stateMutability: 'nonpayable',
                    inputs: [
                      { name: 'to', type: 'address' },
                      { name: 'amount', type: 'uint256' },
                    ],
                    outputs: [{ name: '', type: 'bool' }],
                  },
                ],
                functionName: 'transfer',
                args: [
                  process.env.NEXT_PUBLIC_PAYMENT_RECIPIENT || '0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6',
                  '500000' // 0.5 USDC (6 decimals)
                ],
              }
            ]}
            chainId={BASE_SEPOLIA_CHAIN_ID}
            capabilities={{
              paymasterService: { url: getPaymasterUrl() }
            }}
            onSuccess={handleSuccess}
            onError={handleError}
          >
            <TransactionButton text="Custom Contract Call (0.5 USDC)" />
          </Transaction>
        </CardContent>
      </Card>

      <div className="text-sm text-gray-600 space-y-2">
        <p><strong>Note:</strong> Paymaster integration is currently in development. The current implementation:</p>
        <ul className="list-disc list-inside space-y-1 ml-4">
          <li>✅ Calls the paymaster API to get sponsorship data</li>
          <li>✅ Shows gas sponsorship indicators in the UI</li>
          <li>⚠️ Falls back to regular transactions (paymaster UserOp sending not yet implemented)</li>
          <li>🔄 Full UserOperation bundler integration coming soon</li>
        </ul>
      </div>
    </div>
  );
}
