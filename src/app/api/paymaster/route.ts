import type { NextApiRequest, NextApiResponse } from 'next'
import { NextRequest, NextResponse } from 'next/server'
import { CdpPaymasterClient } from '@coinbase/cdp-sdk'

const paymaster = new CdpPaymasterClient({
  apiKey: process.env.CDP_API_KEY!,
  serviceUrl: process.env.CDP_PAYMASTER_SERVICE!, // keep secret
})

export async function POST(req: NextRequest) {
  /**
   * Front-end sends a partial UserOp (no gas / no paymasterAndData).
   * We sign it, CDP returns gas estimates + paymasterAndData blob.
   */
  try {
    const { partialUserOp } = await req.json()
    
    if (!partialUserOp) {
      return NextResponse.json(
        { error: 'partialUserOp is required' },
        { status: 400 }
      )
    }

    const sponsored = await paymaster.sponsorUserOperation({
      chainId: 'base-sepolia', // Use base-sepolia for testing
      userOperation: partialUserOp,
    })
    
    return NextResponse.json(sponsored) // { gasLimits, paymasterAndData }
  } catch (e: any) {
    console.error('Paymaster sponsor failed', e)
    return NextResponse.json(
      { error: e.message || 'Paymaster sponsorship failed' },
      { status: 500 }
    )
  }
}
