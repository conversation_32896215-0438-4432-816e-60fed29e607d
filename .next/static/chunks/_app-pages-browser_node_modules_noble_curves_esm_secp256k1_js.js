"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_noble_curves_esm_secp256k1_js"],{

/***/ "(app-pages-browser)/./node_modules/@noble/curves/esm/_shortw_utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/@noble/curves/esm/_shortw_utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCurve: () => (/* binding */ createCurve),\n/* harmony export */   getHash: () => (/* binding */ getHash)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/hmac */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/hmac.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n/** connects noble-curves to noble-hashes */\nfunction getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => (0,_noble_hashes_hmac__WEBPACK_IMPORTED_MODULE_0__.hmac)(hash, key, (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.concatBytes)(...msgs)),\n        randomBytes: _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.randomBytes,\n    };\n}\nfunction createCurve(curveDef, defHash) {\n    const create = (hash) => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_2__.weierstrass)({ ...curveDef, ...getHash(hash) });\n    return { ...create(defHash), create };\n}\n//# sourceMappingURL=_shortw_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Abm9ibGUvY3VydmVzL2VzbS9fc2hvcnR3X3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUMwQztBQUNxQjtBQUNQO0FBQ3hEO0FBQ087QUFDUDtBQUNBO0FBQ0EsZ0NBQWdDLHdEQUFJLFlBQVksZ0VBQVc7QUFDM0QsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDTztBQUNQLDZCQUE2QixxRUFBVyxHQUFHLCtCQUErQjtBQUMxRSxhQUFhO0FBQ2I7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQG5vYmxlL2N1cnZlcy9lc20vX3Nob3J0d191dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFV0aWxpdGllcyBmb3Igc2hvcnQgd2VpZXJzdHJhc3MgY3VydmVzLCBjb21iaW5lZCB3aXRoIG5vYmxlLWhhc2hlcy5cbiAqIEBtb2R1bGVcbiAqL1xuLyohIG5vYmxlLWN1cnZlcyAtIE1JVCBMaWNlbnNlIChjKSAyMDIyIFBhdWwgTWlsbGVyIChwYXVsbWlsbHIuY29tKSAqL1xuaW1wb3J0IHsgaG1hYyB9IGZyb20gJ0Bub2JsZS9oYXNoZXMvaG1hYyc7XG5pbXBvcnQgeyBjb25jYXRCeXRlcywgcmFuZG9tQnl0ZXMgfSBmcm9tICdAbm9ibGUvaGFzaGVzL3V0aWxzJztcbmltcG9ydCB7IHdlaWVyc3RyYXNzIH0gZnJvbSBcIi4vYWJzdHJhY3Qvd2VpZXJzdHJhc3MuanNcIjtcbi8qKiBjb25uZWN0cyBub2JsZS1jdXJ2ZXMgdG8gbm9ibGUtaGFzaGVzICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0SGFzaChoYXNoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgaGFzaCxcbiAgICAgICAgaG1hYzogKGtleSwgLi4ubXNncykgPT4gaG1hYyhoYXNoLCBrZXksIGNvbmNhdEJ5dGVzKC4uLm1zZ3MpKSxcbiAgICAgICAgcmFuZG9tQnl0ZXMsXG4gICAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVDdXJ2ZShjdXJ2ZURlZiwgZGVmSGFzaCkge1xuICAgIGNvbnN0IGNyZWF0ZSA9IChoYXNoKSA9PiB3ZWllcnN0cmFzcyh7IC4uLmN1cnZlRGVmLCAuLi5nZXRIYXNoKGhhc2gpIH0pO1xuICAgIHJldHVybiB7IC4uLmNyZWF0ZShkZWZIYXNoKSwgY3JlYXRlIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1fc2hvcnR3X3V0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@noble/curves/esm/_shortw_utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/curve.js":
/*!**********************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/curve.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pippenger: () => (/* binding */ pippenger),\n/* harmony export */   precomputeMSMUnsafe: () => (/* binding */ precomputeMSMUnsafe),\n/* harmony export */   validateBasic: () => (/* binding */ validateBasic),\n/* harmony export */   wNAF: () => (/* binding */ wNAF)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nfunction constTimeNegate(condition, item) {\n    const neg = item.negate();\n    return condition ? neg : item;\n}\nfunction validateW(W, bits) {\n    if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n        throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\nfunction calcWOpts(W, scalarBits) {\n    validateW(W, scalarBits);\n    const windows = Math.ceil(scalarBits / W) + 1; // W=8 33. Not 32, because we skip zero\n    const windowSize = 2 ** (W - 1); // W=8 128. Not 256, because we skip zero\n    const maxNumber = 2 ** W; // W=8 256\n    const mask = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(W); // W=8 255 == mask 0b11111111\n    const shiftBy = BigInt(W); // W=8 8\n    return { windows, windowSize, mask, maxNumber, shiftBy };\n}\nfunction calcOffsets(n, window, wOpts) {\n    const { windowSize, mask, maxNumber, shiftBy } = wOpts;\n    let wbits = Number(n & mask); // extract W bits.\n    let nextN = n >> shiftBy; // shift number by W bits.\n    // What actually happens here:\n    // const highestBit = Number(mask ^ (mask >> 1n));\n    // let wbits2 = wbits - 1; // skip zero\n    // if (wbits2 & highestBit) { wbits2 ^= Number(mask); // (~);\n    // split if bits > max: +224 => 256-32\n    if (wbits > windowSize) {\n        // we skip zero, which means instead of `>= size-1`, we do `> size`\n        wbits -= maxNumber; // -32, can be maxNumber - wbits, but then we need to set isNeg here.\n        nextN += _1n; // +256 (carry)\n    }\n    const offsetStart = window * windowSize;\n    const offset = offsetStart + Math.abs(wbits) - 1; // -1 because we skip zero\n    const isZero = wbits === 0; // is current window slice a 0?\n    const isNeg = wbits < 0; // is current window slice negative?\n    const isNegF = window % 2 !== 0; // fake random statement for noise\n    const offsetF = offsetStart; // fake offset for noise\n    return { nextN, offset, isZero, isNeg, isNegF, offsetF };\n}\nfunction validateMSMPoints(points, c) {\n    if (!Array.isArray(points))\n        throw new Error('array expected');\n    points.forEach((p, i) => {\n        if (!(p instanceof c))\n            throw new Error('invalid point at index ' + i);\n    });\n}\nfunction validateMSMScalars(scalars, field) {\n    if (!Array.isArray(scalars))\n        throw new Error('array of scalars expected');\n    scalars.forEach((s, i) => {\n        if (!field.isValid(s))\n            throw new Error('invalid scalar at index ' + i);\n    });\n}\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes.\n// Allows to make points frozen / immutable.\nconst pointPrecomputes = new WeakMap();\nconst pointWindowSizes = new WeakMap();\nfunction getW(P) {\n    return pointWindowSizes.get(P) || 1;\n}\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nfunction wNAF(c, bits) {\n    return {\n        constTimeNegate,\n        hasPrecomputes(elm) {\n            return getW(elm) !== 1;\n        },\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n, p = c.ZERO) {\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @param elm Point instance\n         * @param W window size\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = calcWOpts(W, bits);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // i=1, bc we skip 0\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // Smaller version:\n            // https://github.com/paulmillr/noble-secp256k1/blob/47cb1669b6e506ad66b35fe7d76132ae97465da2/index.ts#L502-L541\n            // TODO: check the scalar is less than group order?\n            // wNAF behavior is undefined otherwise. But have to carefully remove\n            // other checks before wNAF. ORDER == bits here.\n            // Accumulators\n            let p = c.ZERO;\n            let f = c.BASE;\n            // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n            // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n            // there is negate now: it is possible that negated element from low value\n            // would be the same as high element, which will create carry into next window.\n            // It's not obvious how this can fail, but still worth investigating later.\n            const wo = calcWOpts(W, bits);\n            for (let window = 0; window < wo.windows; window++) {\n                // (n === _0n) is handled and not early-exited. isEven and offsetF are used for noise\n                const { nextN, offset, isZero, isNeg, isNegF, offsetF } = calcOffsets(n, window, wo);\n                n = nextN;\n                if (isZero) {\n                    // bits are 0: add garbage to fake point\n                    // Important part for const-time getPublicKey: add random \"noise\" point to f.\n                    f = f.add(constTimeNegate(isNegF, precomputes[offsetF]));\n                }\n                else {\n                    // bits are 1: add to result point\n                    p = p.add(constTimeNegate(isNeg, precomputes[offset]));\n                }\n            }\n            // Return both real and fake points: JIT won't eliminate f.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        /**\n         * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @param acc accumulator point to add result of multiplication\n         * @returns point\n         */\n        wNAFUnsafe(W, precomputes, n, acc = c.ZERO) {\n            const wo = calcWOpts(W, bits);\n            for (let window = 0; window < wo.windows; window++) {\n                if (n === _0n)\n                    break; // Early-exit, skip 0 value\n                const { nextN, offset, isZero, isNeg } = calcOffsets(n, window, wo);\n                n = nextN;\n                if (isZero) {\n                    // Window bits are 0: skip processing.\n                    // Move to next window.\n                    continue;\n                }\n                else {\n                    const item = precomputes[offset];\n                    acc = acc.add(isNeg ? item.negate() : item); // Re-using acc allows to save adds in MSM\n                }\n            }\n            return acc;\n        },\n        getPrecomputes(W, P, transform) {\n            // Calculate precomputes on a first run, reuse them after\n            let comp = pointPrecomputes.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1)\n                    pointPrecomputes.set(P, transform(comp));\n            }\n            return comp;\n        },\n        wNAFCached(P, n, transform) {\n            const W = getW(P);\n            return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n        },\n        wNAFCachedUnsafe(P, n, transform, prev) {\n            const W = getW(P);\n            if (W === 1)\n                return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n            return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n        },\n        // We calculate precomputes for elliptic curve point multiplication\n        // using windowed method. This specifies window size and\n        // stores precomputed values. Usually only base point would be precomputed.\n        setWindowSize(P, W) {\n            validateW(W, bits);\n            pointWindowSizes.set(P, W);\n            pointPrecomputes.delete(P);\n        },\n    };\n}\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster than precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nfunction pippenger(c, fieldN, points, scalars) {\n    // If we split scalars by some window (let's say 8 bits), every chunk will only\n    // take 256 buckets even if there are 4096 scalars, also re-uses double.\n    // TODO:\n    // - https://eprint.iacr.org/2024/750.pdf\n    // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n    // 0 is accepted in scalars\n    validateMSMPoints(points, c);\n    validateMSMScalars(scalars, fieldN);\n    const plength = points.length;\n    const slength = scalars.length;\n    if (plength !== slength)\n        throw new Error('arrays of points and scalars must have equal length');\n    // if (plength === 0) throw new Error('array must be of length >= 2');\n    const zero = c.ZERO;\n    const wbits = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitLen)(BigInt(plength));\n    let windowSize = 1; // bits\n    if (wbits > 12)\n        windowSize = wbits - 3;\n    else if (wbits > 4)\n        windowSize = wbits - 2;\n    else if (wbits > 0)\n        windowSize = 2;\n    const MASK = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(windowSize);\n    const buckets = new Array(Number(MASK) + 1).fill(zero); // +1 for zero array\n    const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n    let sum = zero;\n    for (let i = lastBits; i >= 0; i -= windowSize) {\n        buckets.fill(zero);\n        for (let j = 0; j < slength; j++) {\n            const scalar = scalars[j];\n            const wbits = Number((scalar >> BigInt(i)) & MASK);\n            buckets[wbits] = buckets[wbits].add(points[j]);\n        }\n        let resI = zero; // not using this will do small speed-up, but will lose ct\n        // Skip first bucket, because it is zero\n        for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n            sumI = sumI.add(buckets[j]);\n            resI = resI.add(sumI);\n        }\n        sum = sum.add(resI);\n        if (i !== 0)\n            for (let j = 0; j < windowSize; j++)\n                sum = sum.double();\n    }\n    return sum;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nfunction precomputeMSMUnsafe(c, fieldN, points, windowSize) {\n    /**\n     * Performance Analysis of Window-based Precomputation\n     *\n     * Base Case (256-bit scalar, 8-bit window):\n     * - Standard precomputation requires:\n     *   - 31 additions per scalar × 256 scalars = 7,936 ops\n     *   - Plus 255 summary additions = 8,191 total ops\n     *   Note: Summary additions can be optimized via accumulator\n     *\n     * Chunked Precomputation Analysis:\n     * - Using 32 chunks requires:\n     *   - 255 additions per chunk\n     *   - 256 doublings\n     *   - Total: (255 × 32) + 256 = 8,416 ops\n     *\n     * Memory Usage Comparison:\n     * Window Size | Standard Points | Chunked Points\n     * ------------|-----------------|---------------\n     *     4-bit   |     520         |      15\n     *     8-bit   |    4,224        |     255\n     *    10-bit   |   13,824        |   1,023\n     *    16-bit   |  557,056        |  65,535\n     *\n     * Key Advantages:\n     * 1. Enables larger window sizes due to reduced memory overhead\n     * 2. More efficient for smaller scalar counts:\n     *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n     *    - ~2x faster than standard 8,191 ops\n     *\n     * Limitations:\n     * - Not suitable for plain precomputes (requires 256 constant doublings)\n     * - Performance degrades with larger scalar counts:\n     *   - Optimal for ~256 scalars\n     *   - Less efficient for 4096+ scalars (Pippenger preferred)\n     */\n    validateW(windowSize, fieldN.BITS);\n    validateMSMPoints(points, c);\n    const zero = c.ZERO;\n    const tableSize = 2 ** windowSize - 1; // table size (without zero)\n    const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n    const MASK = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(windowSize);\n    const tables = points.map((p) => {\n        const res = [];\n        for (let i = 0, acc = p; i < tableSize; i++) {\n            res.push(acc);\n            acc = acc.add(p);\n        }\n        return res;\n    });\n    return (scalars) => {\n        validateMSMScalars(scalars, fieldN);\n        if (scalars.length > points.length)\n            throw new Error('array of scalars must be smaller than array of points');\n        let res = zero;\n        for (let i = 0; i < chunks; i++) {\n            // No need to double if accumulator is still zero.\n            if (res !== zero)\n                for (let j = 0; j < windowSize; j++)\n                    res = res.double();\n            const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n            for (let j = 0; j < scalars.length; j++) {\n                const n = scalars[j];\n                const curr = Number((n >> shiftBy) & MASK);\n                if (!curr)\n                    continue; // skip zero scalars chunks\n                res = res.add(tables[j][curr - 1]);\n            }\n        }\n        return res;\n    };\n}\nfunction validateBasic(curve) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.validateField)(curve.Fp);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...(0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.nLength)(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/hash-to-curve.js":
/*!******************************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/hash-to-curve.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHasher: () => (/* binding */ createHasher),\n/* harmony export */   expand_message_xmd: () => (/* binding */ expand_message_xmd),\n/* harmony export */   expand_message_xof: () => (/* binding */ expand_message_xof),\n/* harmony export */   hash_to_field: () => (/* binding */ hash_to_field),\n/* harmony export */   isogenyMap: () => (/* binding */ isogenyMap)\n/* harmony export */ });\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = _utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n    anum(value);\n    anum(length);\n    if (value < 0 || value >= 1 << (8 * length))\n        throw new Error('invalid I2OSP input: ' + value);\n    const res = Array.from({ length }).fill(0);\n    for (let i = length - 1; i >= 0; i--) {\n        res[i] = value & 0xff;\n        value >>>= 8;\n    }\n    return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n    const arr = new Uint8Array(a.length);\n    for (let i = 0; i < a.length; i++) {\n        arr[i] = a[i] ^ b[i];\n    }\n    return arr;\n}\nfunction anum(item) {\n    if (!Number.isSafeInteger(item))\n        throw new Error('number expected');\n}\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nfunction expand_message_xmd(msg, DST, lenInBytes, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    if (DST.length > 255)\n        DST = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-'), DST));\n    const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n    const ell = Math.ceil(lenInBytes / b_in_bytes);\n    if (lenInBytes > 65535 || ell > 255)\n        throw new Error('expand_message_xmd: invalid lenInBytes');\n    const DST_prime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(DST, i2osp(DST.length, 1));\n    const Z_pad = i2osp(0, r_in_bytes);\n    const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n    const b = new Array(ell);\n    const b_0 = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n    b[0] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(b_0, i2osp(1, 1), DST_prime));\n    for (let i = 1; i <= ell; i++) {\n        const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n        b[i] = H((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...args));\n    }\n    const pseudo_random_bytes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...b);\n    return pseudo_random_bytes.slice(0, lenInBytes);\n}\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nfunction expand_message_xof(msg, DST, lenInBytes, k, H) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(DST);\n    anum(lenInBytes);\n    // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n    // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n    if (DST.length > 255) {\n        const dkLen = Math.ceil((2 * k) / 8);\n        DST = H.create({ dkLen }).update((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)('H2C-OVERSIZE-DST-')).update(DST).digest();\n    }\n    if (lenInBytes > 65535 || DST.length > 255)\n        throw new Error('expand_message_xof: invalid lenInBytes');\n    return (H.create({ dkLen: lenInBytes })\n        .update(msg)\n        .update(i2osp(lenInBytes, 2))\n        // 2. DST_prime = DST || I2OSP(len(DST), 1)\n        .update(DST)\n        .update(i2osp(DST.length, 1))\n        .digest());\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nfunction hash_to_field(msg, count, options) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(options, {\n        DST: 'stringOrUint8Array',\n        p: 'bigint',\n        m: 'isSafeInteger',\n        k: 'isSafeInteger',\n        hash: 'hash',\n    });\n    const { p, k, m, hash, expand, DST: _DST } = options;\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(msg);\n    anum(count);\n    const DST = typeof _DST === 'string' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.utf8ToBytes)(_DST) : _DST;\n    const log2p = p.toString(2).length;\n    const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n    const len_in_bytes = count * m * L;\n    let prb; // pseudo_random_bytes\n    if (expand === 'xmd') {\n        prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n    }\n    else if (expand === 'xof') {\n        prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n    }\n    else if (expand === '_internal_pass') {\n        // for internal tests only\n        prb = msg;\n    }\n    else {\n        throw new Error('expand must be \"xmd\" or \"xof\"');\n    }\n    const u = new Array(count);\n    for (let i = 0; i < count; i++) {\n        const e = new Array(m);\n        for (let j = 0; j < m; j++) {\n            const elm_offset = L * (j + i * m);\n            const tv = prb.subarray(elm_offset, elm_offset + L);\n            e[j] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.mod)(os2ip(tv), p);\n        }\n        u[i] = e;\n    }\n    return u;\n}\nfunction isogenyMap(field, map) {\n    // Make same order as in spec\n    const coeff = map.map((i) => Array.from(i).reverse());\n    return (x, y) => {\n        const [xn, xd, yn, yd] = coeff.map((val) => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));\n        // 6.6.3\n        // Exceptional cases of iso_map are inputs that cause the denominator of\n        // either rational function to evaluate to zero; such cases MUST return\n        // the identity point on E.\n        const [xd_inv, yd_inv] = (0,_modular_js__WEBPACK_IMPORTED_MODULE_1__.FpInvertBatch)(field, [xd, yd], true);\n        x = field.mul(xn, xd_inv); // xNum / xDen\n        y = field.mul(y, field.mul(yn, yd_inv)); // y * (yNum / yDev)\n        return { x, y };\n    };\n}\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. */\nfunction createHasher(Point, mapToCurve, defaults) {\n    if (typeof mapToCurve !== 'function')\n        throw new Error('mapToCurve() must be defined');\n    function map(num) {\n        return Point.fromAffine(mapToCurve(num));\n    }\n    function clear(initial) {\n        const P = initial.clearCofactor();\n        if (P.equals(Point.ZERO))\n            return Point.ZERO; // zero will throw in assert\n        P.assertValidity();\n        return P;\n    }\n    return {\n        defaults,\n        // Encodes byte string to elliptic curve.\n        // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        hashToCurve(msg, options) {\n            const u = hash_to_field(msg, 2, { ...defaults, DST: defaults.DST, ...options });\n            const u0 = map(u[0]);\n            const u1 = map(u[1]);\n            return clear(u0.add(u1));\n        },\n        // Encodes byte string to elliptic curve.\n        // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n        encodeToCurve(msg, options) {\n            const u = hash_to_field(msg, 1, { ...defaults, DST: defaults.encodeDST, ...options });\n            return clear(map(u[0]));\n        },\n        // Same as encodeToCurve, but without hash\n        mapToCurve(scalars) {\n            if (!Array.isArray(scalars))\n                throw new Error('expected array of bigints');\n            for (const i of scalars)\n                if (typeof i !== 'bigint')\n                    throw new Error('expected array of bigints');\n            return clear(map(scalars));\n        },\n    };\n}\n//# sourceMappingURL=hash-to-curve.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/hash-to-curve.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/modular.js":
/*!************************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/modular.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FpDiv: () => (/* binding */ FpDiv),\n/* harmony export */   FpInvertBatch: () => (/* binding */ FpInvertBatch),\n/* harmony export */   FpIsSquare: () => (/* binding */ FpIsSquare),\n/* harmony export */   FpLegendre: () => (/* binding */ FpLegendre),\n/* harmony export */   FpPow: () => (/* binding */ FpPow),\n/* harmony export */   FpSqrt: () => (/* binding */ FpSqrt),\n/* harmony export */   FpSqrtEven: () => (/* binding */ FpSqrtEven),\n/* harmony export */   FpSqrtOdd: () => (/* binding */ FpSqrtOdd),\n/* harmony export */   getFieldBytesLength: () => (/* binding */ getFieldBytesLength),\n/* harmony export */   getMinHashLength: () => (/* binding */ getMinHashLength),\n/* harmony export */   hashToPrivateScalar: () => (/* binding */ hashToPrivateScalar),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   isNegativeLE: () => (/* binding */ isNegativeLE),\n/* harmony export */   mapHashToField: () => (/* binding */ mapHashToField),\n/* harmony export */   mod: () => (/* binding */ mod),\n/* harmony export */   nLength: () => (/* binding */ nLength),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   pow2: () => (/* binding */ pow2),\n/* harmony export */   tonelliShanks: () => (/* binding */ tonelliShanks),\n/* harmony export */   validateField: () => (/* binding */ validateField)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// Calculates a modulo b\nfunction mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * TODO: remove.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nfunction pow(num, power, modulo) {\n    return FpPow(Field(modulo), num, power);\n}\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nfunction pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nfunction invert(number, modulo) {\n    if (number === _0n)\n        throw new Error('invert: expected non-zero number');\n    if (modulo <= _0n)\n        throw new Error('invert: expected positive modulus, got ' + modulo);\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n// Not all roots are possible! Example which will throw:\n// const NUM =\n// n = 72057594037927816n;\n// Fp = Field(BigInt('0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaab'));\nfunction sqrt3mod4(Fp, n) {\n    const p1div4 = (Fp.ORDER + _1n) / _4n;\n    const root = Fp.pow(n, p1div4);\n    // Throw if root^2 != n\n    if (!Fp.eql(Fp.sqr(root), n))\n        throw new Error('Cannot find square root');\n    return root;\n}\nfunction sqrt5mod8(Fp, n) {\n    const p5div8 = (Fp.ORDER - _5n) / _8n;\n    const n2 = Fp.mul(n, _2n);\n    const v = Fp.pow(n2, p5div8);\n    const nv = Fp.mul(n, v);\n    const i = Fp.mul(Fp.mul(nv, _2n), v);\n    const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n    if (!Fp.eql(Fp.sqr(root), n))\n        throw new Error('Cannot find square root');\n    return root;\n}\n// TODO: Commented-out for now. Provide test vectors.\n// Tonelli is too slow for extension fields Fp2.\n// That means we can't use sqrt (c1, c2...) even for initialization constants.\n// if (P % _16n === _9n) return sqrt9mod16;\n// // prettier-ignore\n// function sqrt9mod16<T>(Fp: IField<T>, n: T, p7div16?: bigint) {\n//   if (p7div16 === undefined) p7div16 = (Fp.ORDER + BigInt(7)) / _16n;\n//   const c1 = Fp.sqrt(Fp.neg(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n//   const c2 = Fp.sqrt(c1);             //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n//   const c3 = Fp.sqrt(Fp.neg(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n//   const c4 = p7div16;                 //  4. c4 = (q + 7) / 16        # Integer arithmetic\n//   let tv1 = Fp.pow(n, c4);            //  1. tv1 = x^c4\n//   let tv2 = Fp.mul(c1, tv1);          //  2. tv2 = c1 * tv1\n//   const tv3 = Fp.mul(c2, tv1);        //  3. tv3 = c2 * tv1\n//   let tv4 = Fp.mul(c3, tv1);          //  4. tv4 = c3 * tv1\n//   const e1 = Fp.eql(Fp.sqr(tv2), n);  //  5.  e1 = (tv2^2) == x\n//   const e2 = Fp.eql(Fp.sqr(tv3), n);  //  6.  e2 = (tv3^2) == x\n//   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n//   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n//   const e3 = Fp.eql(Fp.sqr(tv2), n);  //  9.  e3 = (tv2^2) == x\n//   return Fp.cmov(tv1, tv2, e3); // 10.  z = CMOV(tv1, tv2, e3) # Select the sqrt from tv1 and tv2\n// }\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nfunction tonelliShanks(P) {\n    // Initialization (precomputation).\n    if (P < BigInt(3))\n        throw new Error('sqrt is not defined for small field');\n    // Factor P - 1 = Q * 2^S, where Q is odd\n    let Q = P - _1n;\n    let S = 0;\n    while (Q % _2n === _0n) {\n        Q /= _2n;\n        S++;\n    }\n    // Find the first quadratic non-residue Z >= 2\n    let Z = _2n;\n    const _Fp = Field(P);\n    while (FpLegendre(_Fp, Z) === 1) {\n        // Basic primality test for P. After x iterations, chance of\n        // not finding quadratic non-residue is 2^x, so 2^1000.\n        if (Z++ > 1000)\n            throw new Error('Cannot find square root: probably non-prime P');\n    }\n    // Fast-path; usually done before Z, but we do \"primality test\".\n    if (S === 1)\n        return sqrt3mod4;\n    // Slow-path\n    // TODO: test on Fp2 and others\n    let cc = _Fp.pow(Z, Q); // c = z^Q\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        if (Fp.is0(n))\n            return n;\n        // Check if n is a quadratic residue using Legendre symbol\n        if (FpLegendre(Fp, n) !== 1)\n            throw new Error('Cannot find square root');\n        // Initialize variables for the main loop\n        let M = S;\n        let c = Fp.mul(Fp.ONE, cc); // c = z^Q, move cc from field _Fp into field Fp\n        let t = Fp.pow(n, Q); // t = n^Q, first guess at the fudge factor\n        let R = Fp.pow(n, Q1div2); // R = n^((Q+1)/2), first guess at the square root\n        // Main loop\n        // while t != 1\n        while (!Fp.eql(t, Fp.ONE)) {\n            if (Fp.is0(t))\n                return Fp.ZERO; // if t=0 return R=0\n            let i = 1;\n            // Find the smallest i >= 1 such that t^(2^i) ≡ 1 (mod P)\n            let t_tmp = Fp.sqr(t); // t^(2^1)\n            while (!Fp.eql(t_tmp, Fp.ONE)) {\n                i++;\n                t_tmp = Fp.sqr(t_tmp); // t^(2^2)...\n                if (i === M)\n                    throw new Error('Cannot find square root');\n            }\n            // Calculate the exponent for b: 2^(M - i - 1)\n            const exponent = _1n << BigInt(M - i - 1); // bigint is important\n            const b = Fp.pow(c, exponent); // b = 2^(M - i - 1)\n            // Update variables\n            M = i;\n            c = Fp.sqr(b); // c = b^2\n            t = Fp.mul(t, c); // t = (t * b^2)\n            R = Fp.mul(R, b); // R = R*b\n        }\n        return R;\n    };\n}\n/**\n * Square root for a finite field. Will try optimized versions first:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nfunction FpSqrt(P) {\n    // P ≡ 3 (mod 4) => √n = n^((P+1)/4)\n    if (P % _4n === _3n)\n        return sqrt3mod4;\n    // P ≡ 5 (mod 8) => Atkin algorithm, page 10 of https://eprint.iacr.org/2012/685.pdf\n    if (P % _8n === _5n)\n        return sqrt5mod8;\n    // P ≡ 9 (mod 16) not implemented, see above\n    // Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nconst isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nfunction validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nfunction FpPow(Fp, num, power) {\n    if (power < _0n)\n        throw new Error('invalid exponent, negatives unsupported');\n    if (power === _0n)\n        return Fp.ONE;\n    if (power === _1n)\n        return num;\n    let p = Fp.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = Fp.mul(p, d);\n        d = Fp.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * Exception-free. Will return `undefined` for 0 elements.\n * @param passZero map 0 to 0 (instead of undefined)\n */\nfunction FpInvertBatch(Fp, nums, passZero = false) {\n    const inverted = new Array(nums.length).fill(passZero ? Fp.ZERO : undefined);\n    // Walk from first to last, multiply them by each other MOD p\n    const multipliedAcc = nums.reduce((acc, num, i) => {\n        if (Fp.is0(num))\n            return acc;\n        inverted[i] = acc;\n        return Fp.mul(acc, num);\n    }, Fp.ONE);\n    // Invert last element\n    const invertedAcc = Fp.inv(multipliedAcc);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (Fp.is0(num))\n            return acc;\n        inverted[i] = Fp.mul(acc, inverted[i]);\n        return Fp.mul(acc, num);\n    }, invertedAcc);\n    return inverted;\n}\n// TODO: remove\nfunction FpDiv(Fp, lhs, rhs) {\n    return Fp.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, Fp.ORDER) : Fp.inv(rhs));\n}\n/**\n * Legendre symbol.\n * Legendre constant is used to calculate Legendre symbol (a | p)\n * which denotes the value of a^((p-1)/2) (mod p).\n *\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nfunction FpLegendre(Fp, n) {\n    // We can use 3rd argument as optional cache of this value\n    // but seems unneeded for now. The operation is very fast.\n    const p1mod2 = (Fp.ORDER - _1n) / _2n;\n    const powered = Fp.pow(n, p1mod2);\n    const yes = Fp.eql(powered, Fp.ONE);\n    const zero = Fp.eql(powered, Fp.ZERO);\n    const no = Fp.eql(powered, Fp.neg(Fp.ONE));\n    if (!yes && !zero && !no)\n        throw new Error('invalid Legendre symbol result');\n    return yes ? 1 : zero ? 0 : -1;\n}\n// This function returns True whenever the value x is a square in the field F.\nfunction FpIsSquare(Fp, n) {\n    const l = FpLegendre(Fp, n);\n    return l === 1;\n}\n// CURVE.n lengths\nfunction nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    if (nBitLength !== undefined)\n        (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.anumber)(nBitLength);\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nfunction Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n    let sqrtP; // cached sqrtP\n    const f = Object.freeze({\n        ORDER,\n        isLE,\n        BITS,\n        BYTES,\n        MASK: (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error('invalid field element: expected bigint, got ' + typeof num);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt ||\n            ((n) => {\n                if (!sqrtP)\n                    sqrtP = FpSqrt(ORDER);\n                return sqrtP(f, n);\n            }),\n        toBytes: (num) => (isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(num, BYTES) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n            return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(bytes) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes);\n        },\n        // TODO: we don't need it here, move out to separate fn\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // We can't move this out because Fp6, Fp12 implement it\n        // and it's unclear what to return in there.\n        cmov: (a, b, c) => (c ? b : a),\n    });\n    return Object.freeze(f);\n}\nfunction FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nfunction FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(\"Field doesn't have isOdd\");\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nfunction hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error('hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(hash) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nfunction getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nfunction getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nfunction mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n    const num = isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberLE)(key) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesLE)(reduced, fieldLen) : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/modular.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/weierstrass.js":
/*!****************************************************************!*\
  !*** ./node_modules/@noble/curves/esm/abstract/weierstrass.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DER: () => (/* binding */ DER),\n/* harmony export */   DERErr: () => (/* binding */ DERErr),\n/* harmony export */   SWUFpSqrtRatio: () => (/* binding */ SWUFpSqrtRatio),\n/* harmony export */   mapToCurveSimpleSWU: () => (/* binding */ mapToCurveSimpleSWU),\n/* harmony export */   weierstrass: () => (/* binding */ weierstrass),\n/* harmony export */   weierstrassPoints: () => (/* binding */ weierstrassPoints)\n/* harmony export */ });\n/* harmony import */ var _curve_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./curve.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/curve.js\");\n/* harmony import */ var _modular_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modular.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/**\n * Short Weierstrass curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Parameters\n *\n * To initialize a weierstrass curve, one needs to pass following params:\n *\n * * a: formula param\n * * b: formula param\n * * Fp: finite field of prime characteristic P; may be complex (Fp2). Arithmetics is done in field\n * * n: order of prime subgroup a.k.a total amount of valid curve points\n * * Gx: Base point (x, y) aka generator point. Gx = x coordinate\n * * Gy: ...y coordinate\n * * h: cofactor, usually 1. h*n = curve group order (n is only subgroup order)\n * * lowS: whether to enable (default) or disable \"low-s\" non-malleable signatures\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// prettier-ignore\n\n// prettier-ignore\n\n// prettier-ignore\n\nfunction validateSigVerOpts(opts) {\n    if (opts.lowS !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('lowS', opts.lowS);\n    if (opts.prehash !== undefined)\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('prehash', opts.prehash);\n}\nfunction validatePointOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowInfinityPoint: 'boolean',\n        allowedPrivateKeyLengths: 'array',\n        clearCofactor: 'function',\n        fromBytes: 'function',\n        isTorsionFree: 'function',\n        toBytes: 'function',\n        wrapPrivateKey: 'boolean',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('invalid endo: CURVE.a must be 0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('invalid endo: expected \"beta\": bigint and \"splitScalar\": function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\nclass DERErr extends Error {\n    constructor(m = '') {\n        super(m);\n    }\n}\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nconst DER = {\n    // asn.1 DER encoding utils\n    Err: DERErr,\n    // Basic building block is TLV (Tag-Length-Value)\n    _tlv: {\n        encode: (tag, data) => {\n            const { Err: E } = DER;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length & 1)\n                throw new E('tlv.encode: unpadded data');\n            const dataLen = data.length / 2;\n            const len = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)(dataLen);\n            if ((len.length / 2) & 128)\n                throw new E('tlv.encode: long form length too big');\n            // length of length with long form flag\n            const lenLen = dataLen > 127 ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)((len.length / 2) | 128) : '';\n            const t = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)(tag);\n            return t + lenLen + len + data;\n        },\n        // v - value, l - left bytes (unparsed)\n        decode(tag, data) {\n            const { Err: E } = DER;\n            let pos = 0;\n            if (tag < 0 || tag > 256)\n                throw new E('tlv.encode: wrong tag');\n            if (data.length < 2 || data[pos++] !== tag)\n                throw new E('tlv.decode: wrong tlv');\n            const first = data[pos++];\n            const isLong = !!(first & 128); // First bit of first length byte is flag for short/long form\n            let length = 0;\n            if (!isLong)\n                length = first;\n            else {\n                // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n                const lenLen = first & 127;\n                if (!lenLen)\n                    throw new E('tlv.decode(long): indefinite length not supported');\n                if (lenLen > 4)\n                    throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n                const lengthBytes = data.subarray(pos, pos + lenLen);\n                if (lengthBytes.length !== lenLen)\n                    throw new E('tlv.decode: length bytes not complete');\n                if (lengthBytes[0] === 0)\n                    throw new E('tlv.decode(long): zero leftmost byte');\n                for (const b of lengthBytes)\n                    length = (length << 8) | b;\n                pos += lenLen;\n                if (length < 128)\n                    throw new E('tlv.decode(long): not minimal encoding');\n            }\n            const v = data.subarray(pos, pos + length);\n            if (v.length !== length)\n                throw new E('tlv.decode: wrong value length');\n            return { v, l: data.subarray(pos + length) };\n        },\n    },\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    _int: {\n        encode(num) {\n            const { Err: E } = DER;\n            if (num < _0n)\n                throw new E('integer: negative integers are not allowed');\n            let hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToHexUnpadded)(num);\n            // Pad with zero byte if negative flag is present\n            if (Number.parseInt(hex[0], 16) & 0b1000)\n                hex = '00' + hex;\n            if (hex.length & 1)\n                throw new E('unexpected DER parsing assertion: unpadded hex');\n            return hex;\n        },\n        decode(data) {\n            const { Err: E } = DER;\n            if (data[0] & 128)\n                throw new E('invalid signature integer: negative');\n            if (data[0] === 0x00 && !(data[1] & 128))\n                throw new E('invalid signature integer: unnecessary leading zero');\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(data);\n        },\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E, _int: int, _tlv: tlv } = DER;\n        const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('signature', hex);\n        const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n        if (seqLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n        const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n        if (sLeftBytes.length)\n            throw new E('invalid signature: left bytes after parsing');\n        return { r: int.decode(rBytes), s: int.decode(sBytes) };\n    },\n    hexFromSig(sig) {\n        const { _tlv: tlv, _int: int } = DER;\n        const rs = tlv.encode(0x02, int.encode(sig.r));\n        const ss = tlv.encode(0x02, int.encode(sig.s));\n        const seq = rs + ss;\n        return tlv.encode(0x30, seq);\n    },\n};\nfunction numToSizedHex(num, size) {\n    return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, size));\n}\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nfunction weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const Fn = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.Field)(CURVE.n, CURVE.nBitLength);\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula. Takes x, returns y².\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x² * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x³ + a * x + b\n    }\n    function isValidXY(x, y) {\n        const left = Fp.sqr(y); // y²\n        const right = weierstrassEquation(x); // x³ + ax + b\n        return Fp.eql(left, right);\n    }\n    // Validate whether the passed curve params are valid.\n    // Test 1: equation y² = x³ + ax + b should work for generator point.\n    if (!isValidXY(CURVE.Gx, CURVE.Gy))\n        throw new Error('bad curve params: generator point');\n    // Test 2: discriminant Δ part should be non-zero: 4a³ + 27b² != 0.\n    // Guarantees curve is genus-1, smooth (non-singular).\n    const _4a3 = Fp.mul(Fp.pow(CURVE.a, _3n), _4n);\n    const _27b2 = Fp.mul(Fp.sqr(CURVE.b), BigInt(27));\n    if (Fp.is0(Fp.add(_4a3, _27b2)))\n        throw new Error('bad curve params: a or b');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange)(num, _1n, CURVE.n);\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n: N } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes)(key))\n                key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('invalid private key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error('invalid private key, expected hex or ' + nByteLength + ' bytes, got ' + typeof key);\n        }\n        if (wrapPrivateKey)\n            num = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(num, N); // disabled by default, enabled for BLS\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('private key', num, _1n, N); // num in range [1..N-1]\n        return num;\n    }\n    function aprjpoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    // Memoized toAffine / validity check. They are heavy. Points are immutable.\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (X, Y, Z) ∋ (x=X/Z, y=Y/Z)\n    const toAffineMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p, iz) => {\n        const { px: x, py: y, pz: z } = p;\n        // Fast-path for normalized points\n        if (Fp.eql(z, Fp.ONE))\n            return { x, y };\n        const is0 = p.is0();\n        // If invZ was 0, we return zero point. However we still want to execute\n        // all operations, so we replace invZ with a random number, 1.\n        if (iz == null)\n            iz = is0 ? Fp.ONE : Fp.inv(z);\n        const ax = Fp.mul(x, iz);\n        const ay = Fp.mul(y, iz);\n        const zz = Fp.mul(z, iz);\n        if (is0)\n            return { x: Fp.ZERO, y: Fp.ZERO };\n        if (!Fp.eql(zz, Fp.ONE))\n            throw new Error('invZ was invalid');\n        return { x: ax, y: ay };\n    });\n    // NOTE: on exception this will crash 'cached' and no value will be set.\n    // Otherwise true will be return\n    const assertValidMemo = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memoized)((p) => {\n        if (p.is0()) {\n            // (0, 1, 0) aka ZERO is invalid in most contexts.\n            // In BLS, ZERO can be serialized, so we allow it.\n            // (0, 0, 0) is invalid representation of ZERO.\n            if (CURVE.allowInfinityPoint && !Fp.is0(p.py))\n                return;\n            throw new Error('bad point: ZERO');\n        }\n        // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n        const { x, y } = p.toAffine();\n        // Check if x, y are valid field elements\n        if (!Fp.isValid(x) || !Fp.isValid(y))\n            throw new Error('bad point: x or y not FE');\n        if (!isValidXY(x, y))\n            throw new Error('bad point: equation left != right');\n        if (!p.isTorsionFree())\n            throw new Error('bad point: not in prime-order subgroup');\n        return true;\n    });\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (X, Y, Z) ∋ (x=X/Z, y=Y/Z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py) || Fp.is0(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            Object.freeze(this);\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.FpInvertBatch)(Fp, points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // Multiscalar Multiplication\n        static msm(points, scalars) {\n            return (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.pippenger)(Point, Fn, points, scalars);\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            wnaf.setWindowSize(this, windowSize);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            assertValidMemo(this);\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            aprjpoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            aprjpoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, n, Point.normalizeZ);\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(sc) {\n            const { endo, n: N } = CURVE;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('scalar', sc, _0n, N);\n            const I = Point.ZERO;\n            if (sc === _0n)\n                return I;\n            if (this.is0() || sc === _1n)\n                return this;\n            // Case a: no endomorphism. Case b: has precomputes.\n            if (!endo || wnaf.hasPrecomputes(this))\n                return wnaf.wNAFCachedUnsafe(this, sc, Point.normalizeZ);\n            // Case c: endomorphism\n            /** See docs for {@link EndomorphismOpts} */\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            const { endo, n: N } = CURVE;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('scalar', scalar, _1n, N);\n            let point, fake; // Fake point is used to const-time mult\n            /** See docs for {@link EndomorphismOpts} */\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(scalar);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            return toAffineMemo(this, iz);\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToHex)(this.toRawBytes(isCompressed));\n        }\n    }\n    // base / generator point\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    // zero / infinity / identity point\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO); // 0, 1, 0\n    const { endo, nBitLength } = CURVE;\n    const wnaf = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.wNAF)(Point, endo ? Math.ceil(nBitLength / 2) : nBitLength);\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = (0,_curve_js__WEBPACK_IMPORTED_MODULE_1__.validateBasic)(curve);\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.validateObject)(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\n/**\n * Creates short weierstrass curve and ECDSA signature methods for it.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, b, p, n, Gx, Gy\n * const curve = weierstrass({ a, b, Fp: Field(p), n, Gx, Gy, h: 1n })\n */\nfunction weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER, nByteLength, nBitLength } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function modN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mod)(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.invert)(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = _utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes;\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abool)('isCompressed', isCompressed);\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(tail);\n                if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.inRange)(x, _1n, Fp.ORDER))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y;\n                try {\n                    y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                }\n                catch (sqrtError) {\n                    const suffix = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n                    throw new Error('Point is not on curve' + suffix);\n                }\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                const cl = compressedLen;\n                const ul = uncompressedLen;\n                throw new Error('invalid Point, expected length of ' + cl + ', or uncompressed ' + ul + ', got ' + len);\n            }\n        },\n    });\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('r', r, _1n, CURVE_ORDER); // r in [1..N]\n            (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('s', s, _1n, CURVE_ORDER); // s in [1..N]\n            this.r = r;\n            this.s = s;\n            if (recovery != null)\n                this.recovery = recovery;\n            Object.freeze(this);\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = nByteLength;\n            hex = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('DER', hex));\n            return new Signature(r, s);\n        }\n        /**\n         * @todo remove\n         * @deprecated\n         */\n        assertValidity() { }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToSizedHex(radj, Fp.BYTES));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes)(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig(this);\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.hexToBytes)(this.toCompactHex());\n        }\n        toCompactHex() {\n            const l = nByteLength;\n            return numToSizedHex(this.r, l) + numToSizedHex(this.s, l);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.getMinHashLength)(CURVE.n);\n            return (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.mapHashToField)(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        if (typeof item === 'bigint')\n            return false;\n        if (item instanceof Point)\n            return true;\n        const arr = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('key', item);\n        const len = arr.length;\n        const fpl = Fp.BYTES;\n        const compLen = fpl + 1; // e.g. 33 for 32\n        const uncompLen = 2 * fpl + 1; // e.g. 65 for 32\n        if (CURVE.allowedPrivateKeyLengths || nByteLength === compLen) {\n            return undefined;\n        }\n        else {\n            return len === compLen || len === uncompLen;\n        }\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA) === true)\n            throw new Error('first arg must be private key');\n        if (isProbPub(publicB) === false)\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // Our custom check \"just in case\", for protection against DoS\n            if (bytes.length > 8192)\n                throw new Error('input is too large');\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bytesToNumberBE)(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.bitMask)(nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aInRange)('num < 2^' + nBitLength, num, _0n, ORDER_MASK);\n        // works with order, can have different size than numToField!\n        return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.numberToBytesBE)(num, nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n    // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        validateSigVerOpts(opts);\n        if (prehash)\n            msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null && ent !== false) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('extraEntropy', e)); // check for being bytes\n        }\n        const seed = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.concatBytes)(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.createHmacDrbg)(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('msgHash', msgHash);\n        publicKey = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureBytes)('publicKey', publicKey);\n        const { lowS, prehash, format } = opts;\n        // Verify opts, deduce signature format\n        validateSigVerOpts(opts);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        if (format !== undefined && format !== 'compact' && format !== 'der')\n            throw new Error('format must be compact or der');\n        const isHex = typeof sg === 'string' || (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isBytes)(sg);\n        const isObj = !isHex &&\n            !format &&\n            typeof sg === 'object' &&\n            sg !== null &&\n            typeof sg.r === 'bigint' &&\n            typeof sg.s === 'bigint';\n        if (!isHex && !isObj)\n            throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n        let _sig = undefined;\n        let P;\n        try {\n            if (isObj)\n                _sig = new Signature(sg.r, sg.s);\n            if (isHex) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    if (format !== 'compact')\n                        _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                }\n                if (!_sig && format !== 'der')\n                    _sig = Signature.fromCompact(sg);\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            return false;\n        }\n        if (!_sig)\n            return false;\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nfunction SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nfunction mapToCurveSimpleSWU(Fp, opts) {\n    (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.validateField)(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        const tv4_inv = (0,_modular_js__WEBPACK_IMPORTED_MODULE_2__.FpInvertBatch)(Fp, [tv4], true)[0];\n        x = Fp.mul(x, tv4_inv); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/weierstrass.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@noble/curves/esm/secp256k1.js":
/*!*****************************************************!*\
  !*** ./node_modules/@noble/curves/esm/secp256k1.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeToCurve: () => (/* binding */ encodeToCurve),\n/* harmony export */   hashToCurve: () => (/* binding */ hashToCurve),\n/* harmony export */   schnorr: () => (/* binding */ schnorr),\n/* harmony export */   secp256k1: () => (/* binding */ secp256k1),\n/* harmony export */   secp256k1_hasher: () => (/* binding */ secp256k1_hasher)\n/* harmony export */ });\n/* harmony import */ var _noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @noble/hashes/sha2 */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/sha2.js\");\n/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/hashes/utils */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/utils.js\");\n/* harmony import */ var _shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./_shortw_utils.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/_shortw_utils.js\");\n/* harmony import */ var _abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./abstract/hash-to-curve.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/hash-to-curve.js\");\n/* harmony import */ var _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abstract/modular.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/modular.js\");\n/* harmony import */ var _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./abstract/utils.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./abstract/weierstrass.js */ \"(app-pages-browser)/./node_modules/@noble/curves/esm/abstract/weierstrass.js\");\n/**\n * NIST secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Seems to be rigid (not backdoored)\n * [as per discussion](https://bitcointalk.org/index.php?topic=289795.msg3183975#msg3183975).\n *\n * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n * [See explanation](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n\n\n\n\n\n\n\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b3, _3n, P) * b3) % P;\n    const b9 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b6, _3n, P) * b3) % P;\n    const b11 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b9, _2n, P) * b2) % P;\n    const b22 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b11, _11n, P) * b11) % P;\n    const b44 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b22, _22n, P) * b22) % P;\n    const b88 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b44, _44n, P) * b44) % P;\n    const b176 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b88, _88n, P) * b88) % P;\n    const b220 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b176, _44n, P) * b44) % P;\n    const b223 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b220, _3n, P) * b3) % P;\n    const t1 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(b223, _23n, P) * b22) % P;\n    const t2 = ((0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t1, _6n, P) * b2) % P;\n    const root = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.pow2)(t2, _2n, P);\n    if (!Fpk1.eql(Fpk1.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fpk1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.Field)(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\n/**\n * secp256k1 curve, ECDSA and ECDH methods.\n *\n * Field: `2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n`\n *\n * @example\n * ```js\n * import { secp256k1 } from '@noble/curves/secp256k1';\n * const priv = secp256k1.utils.randomPrivateKey();\n * const pub = secp256k1.getPublicKey(priv);\n * const msg = new Uint8Array(32).fill(1); // message hash (not message) in ecdsa\n * const sig = secp256k1.sign(msg, priv); // `{prehash: true}` option is available\n * const isValid = secp256k1.verify(sig, msg, pub) === true;\n * ```\n */\nconst secp256k1 = (0,_shortw_utils_js__WEBPACK_IMPORTED_MODULE_1__.createCurve)({\n    a: _0n,\n    b: BigInt(7),\n    Fp: Fpk1,\n    n: secp256k1N,\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1),\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    endo: {\n        // Endomorphism, see above\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(k - c1 * a1 - c2 * a2, n);\n            let k2 = (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, _noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = (0,_noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256)(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return (0,_noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256)((0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.concatBytes)(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE)(n, 32);\nconst modP = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1P);\nconst modN = (x) => (0,_abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod)(x, secp256k1N);\nconst Point = /* @__PURE__ */ (() => secp256k1.ProjectivePoint)();\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.aInRange)('x', x, _1n, secp256k1P); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\nconst num = _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN(num(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_4__.randomBytes)(32)) {\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN(num(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('signature', signature, 64);\n    const m = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('message', message);\n    const pub = (0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.ensureBytes)('publicKey', publicKey, 32);\n    try {\n        const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(r, _1n, secp256k1P))\n            return false;\n        const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!(0,_abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.inRange)(s, _1n, secp256k1N))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * ```js\n * import { schnorr } from '@noble/curves/secp256k1';\n * const priv = schnorr.utils.randomPrivateKey();\n * const pub = schnorr.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, priv);\n * const isValid = schnorr.verify(sig, msg, pub);\n * ```\n */\nconst schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.numberToBytesBE,\n        bytesToNumberBE: _abstract_utils_js__WEBPACK_IMPORTED_MODULE_3__.bytesToNumberBE,\n        taggedHash,\n        mod: _abstract_modular_js__WEBPACK_IMPORTED_MODULE_0__.mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.isogenyMap)(Fpk1, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => (0,_abstract_weierstrass_js__WEBPACK_IMPORTED_MODULE_6__.mapToCurveSimpleSWU)(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n}))();\n/** Hashing / encoding to secp256k1 points / field. RFC 9380 methods. */\nconst secp256k1_hasher = /* @__PURE__ */ (() => (0,_abstract_hash_to_curve_js__WEBPACK_IMPORTED_MODULE_5__.createHasher)(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fpk1.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: _noble_hashes_sha2__WEBPACK_IMPORTED_MODULE_2__.sha256,\n}))();\nconst hashToCurve = /* @__PURE__ */ (() => secp256k1_hasher.hashToCurve)();\nconst encodeToCurve = /* @__PURE__ */ (() => secp256k1_hasher.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@noble/curves/esm/secp256k1.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@noble/hashes/esm/hmac.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/hmac.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HMAC: () => (/* binding */ HMAC),\n/* harmony export */   hmac: () => (/* binding */ hmac)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/@noble/hashes/esm/utils.js\");\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\n\nclass HMAC extends _utils_js__WEBPACK_IMPORTED_MODULE_0__.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ahash)(hash);\n        const key = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.clean)(pad);\n    }\n    update(buf) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.aexists)(this);\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Abm9ibGUvaGFzaGVzL2VzbS9obWFjLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQzBFO0FBQ25FLG1CQUFtQiwyQ0FBSTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsZ0RBQUs7QUFDYixvQkFBb0Isa0RBQU87QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdCQUFnQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdCQUFnQjtBQUN4QztBQUNBO0FBQ0EsUUFBUSxnREFBSztBQUNiO0FBQ0E7QUFDQSxRQUFRLGtEQUFPO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGtEQUFPO0FBQ2YsUUFBUSxpREFBTTtBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFO0FBQ2pFLGdCQUFnQix5REFBeUQ7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQjtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9Abm9ibGUvaGFzaGVzL2VzbS9obWFjLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogSE1BQzogUkZDMjEwNCBtZXNzYWdlIGF1dGhlbnRpY2F0aW9uIGNvZGUuXG4gKiBAbW9kdWxlXG4gKi9cbmltcG9ydCB7IGFieXRlcywgYWV4aXN0cywgYWhhc2gsIGNsZWFuLCBIYXNoLCB0b0J5dGVzIH0gZnJvbSBcIi4vdXRpbHMuanNcIjtcbmV4cG9ydCBjbGFzcyBITUFDIGV4dGVuZHMgSGFzaCB7XG4gICAgY29uc3RydWN0b3IoaGFzaCwgX2tleSkge1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICB0aGlzLmZpbmlzaGVkID0gZmFsc2U7XG4gICAgICAgIHRoaXMuZGVzdHJveWVkID0gZmFsc2U7XG4gICAgICAgIGFoYXNoKGhhc2gpO1xuICAgICAgICBjb25zdCBrZXkgPSB0b0J5dGVzKF9rZXkpO1xuICAgICAgICB0aGlzLmlIYXNoID0gaGFzaC5jcmVhdGUoKTtcbiAgICAgICAgaWYgKHR5cGVvZiB0aGlzLmlIYXNoLnVwZGF0ZSAhPT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRXhwZWN0ZWQgaW5zdGFuY2Ugb2YgY2xhc3Mgd2hpY2ggZXh0ZW5kcyB1dGlscy5IYXNoJyk7XG4gICAgICAgIHRoaXMuYmxvY2tMZW4gPSB0aGlzLmlIYXNoLmJsb2NrTGVuO1xuICAgICAgICB0aGlzLm91dHB1dExlbiA9IHRoaXMuaUhhc2gub3V0cHV0TGVuO1xuICAgICAgICBjb25zdCBibG9ja0xlbiA9IHRoaXMuYmxvY2tMZW47XG4gICAgICAgIGNvbnN0IHBhZCA9IG5ldyBVaW50OEFycmF5KGJsb2NrTGVuKTtcbiAgICAgICAgLy8gYmxvY2tMZW4gY2FuIGJlIGJpZ2dlciB0aGFuIG91dHB1dExlblxuICAgICAgICBwYWQuc2V0KGtleS5sZW5ndGggPiBibG9ja0xlbiA/IGhhc2guY3JlYXRlKCkudXBkYXRlKGtleSkuZGlnZXN0KCkgOiBrZXkpO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHBhZC5sZW5ndGg7IGkrKylcbiAgICAgICAgICAgIHBhZFtpXSBePSAweDM2O1xuICAgICAgICB0aGlzLmlIYXNoLnVwZGF0ZShwYWQpO1xuICAgICAgICAvLyBCeSBkb2luZyB1cGRhdGUgKHByb2Nlc3Npbmcgb2YgZmlyc3QgYmxvY2spIG9mIG91dGVyIGhhc2ggaGVyZSB3ZSBjYW4gcmUtdXNlIGl0IGJldHdlZW4gbXVsdGlwbGUgY2FsbHMgdmlhIGNsb25lXG4gICAgICAgIHRoaXMub0hhc2ggPSBoYXNoLmNyZWF0ZSgpO1xuICAgICAgICAvLyBVbmRvIGludGVybmFsIFhPUiAmJiBhcHBseSBvdXRlciBYT1JcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBwYWQubGVuZ3RoOyBpKyspXG4gICAgICAgICAgICBwYWRbaV0gXj0gMHgzNiBeIDB4NWM7XG4gICAgICAgIHRoaXMub0hhc2gudXBkYXRlKHBhZCk7XG4gICAgICAgIGNsZWFuKHBhZCk7XG4gICAgfVxuICAgIHVwZGF0ZShidWYpIHtcbiAgICAgICAgYWV4aXN0cyh0aGlzKTtcbiAgICAgICAgdGhpcy5pSGFzaC51cGRhdGUoYnVmKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIGRpZ2VzdEludG8ob3V0KSB7XG4gICAgICAgIGFleGlzdHModGhpcyk7XG4gICAgICAgIGFieXRlcyhvdXQsIHRoaXMub3V0cHV0TGVuKTtcbiAgICAgICAgdGhpcy5maW5pc2hlZCA9IHRydWU7XG4gICAgICAgIHRoaXMuaUhhc2guZGlnZXN0SW50byhvdXQpO1xuICAgICAgICB0aGlzLm9IYXNoLnVwZGF0ZShvdXQpO1xuICAgICAgICB0aGlzLm9IYXNoLmRpZ2VzdEludG8ob3V0KTtcbiAgICAgICAgdGhpcy5kZXN0cm95KCk7XG4gICAgfVxuICAgIGRpZ2VzdCgpIHtcbiAgICAgICAgY29uc3Qgb3V0ID0gbmV3IFVpbnQ4QXJyYXkodGhpcy5vSGFzaC5vdXRwdXRMZW4pO1xuICAgICAgICB0aGlzLmRpZ2VzdEludG8ob3V0KTtcbiAgICAgICAgcmV0dXJuIG91dDtcbiAgICB9XG4gICAgX2Nsb25lSW50byh0bykge1xuICAgICAgICAvLyBDcmVhdGUgbmV3IGluc3RhbmNlIHdpdGhvdXQgY2FsbGluZyBjb25zdHJ1Y3RvciBzaW5jZSBrZXkgYWxyZWFkeSBpbiBzdGF0ZSBhbmQgd2UgZG9uJ3Qga25vdyBpdC5cbiAgICAgICAgdG8gfHwgKHRvID0gT2JqZWN0LmNyZWF0ZShPYmplY3QuZ2V0UHJvdG90eXBlT2YodGhpcyksIHt9KSk7XG4gICAgICAgIGNvbnN0IHsgb0hhc2gsIGlIYXNoLCBmaW5pc2hlZCwgZGVzdHJveWVkLCBibG9ja0xlbiwgb3V0cHV0TGVuIH0gPSB0aGlzO1xuICAgICAgICB0byA9IHRvO1xuICAgICAgICB0by5maW5pc2hlZCA9IGZpbmlzaGVkO1xuICAgICAgICB0by5kZXN0cm95ZWQgPSBkZXN0cm95ZWQ7XG4gICAgICAgIHRvLmJsb2NrTGVuID0gYmxvY2tMZW47XG4gICAgICAgIHRvLm91dHB1dExlbiA9IG91dHB1dExlbjtcbiAgICAgICAgdG8ub0hhc2ggPSBvSGFzaC5fY2xvbmVJbnRvKHRvLm9IYXNoKTtcbiAgICAgICAgdG8uaUhhc2ggPSBpSGFzaC5fY2xvbmVJbnRvKHRvLmlIYXNoKTtcbiAgICAgICAgcmV0dXJuIHRvO1xuICAgIH1cbiAgICBjbG9uZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2Nsb25lSW50bygpO1xuICAgIH1cbiAgICBkZXN0cm95KCkge1xuICAgICAgICB0aGlzLmRlc3Ryb3llZCA9IHRydWU7XG4gICAgICAgIHRoaXMub0hhc2guZGVzdHJveSgpO1xuICAgICAgICB0aGlzLmlIYXNoLmRlc3Ryb3koKTtcbiAgICB9XG59XG4vKipcbiAqIEhNQUM6IFJGQzIxMDQgbWVzc2FnZSBhdXRoZW50aWNhdGlvbiBjb2RlLlxuICogQHBhcmFtIGhhc2ggLSBmdW5jdGlvbiB0aGF0IHdvdWxkIGJlIHVzZWQgZS5nLiBzaGEyNTZcbiAqIEBwYXJhbSBrZXkgLSBtZXNzYWdlIGtleVxuICogQHBhcmFtIG1lc3NhZ2UgLSBtZXNzYWdlIGRhdGFcbiAqIEBleGFtcGxlXG4gKiBpbXBvcnQgeyBobWFjIH0gZnJvbSAnQG5vYmxlL2hhc2hlcy9obWFjJztcbiAqIGltcG9ydCB7IHNoYTI1NiB9IGZyb20gJ0Bub2JsZS9oYXNoZXMvc2hhMic7XG4gKiBjb25zdCBtYWMxID0gaG1hYyhzaGEyNTYsICdrZXknLCAnbWVzc2FnZScpO1xuICovXG5leHBvcnQgY29uc3QgaG1hYyA9IChoYXNoLCBrZXksIG1lc3NhZ2UpID0+IG5ldyBITUFDKGhhc2gsIGtleSkudXBkYXRlKG1lc3NhZ2UpLmRpZ2VzdCgpO1xuaG1hYy5jcmVhdGUgPSAoaGFzaCwga2V5KSA9PiBuZXcgSE1BQyhoYXNoLCBrZXkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aG1hYy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@noble/hashes/esm/hmac.js\n"));

/***/ })

}]);