"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/abitype";
exports.ids = ["vendor-chunks/abitype"];
exports.modules = {

/***/ "(ssr)/./node_modules/abitype/dist/esm/errors.js":
/*!*************************************************!*\
  !*** ./node_modules/abitype/dist/esm/errors.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/abitype/dist/esm/version.js\");\n\nclass BaseError extends Error {\n    constructor(shortMessage, args = {}) {\n        const details = args.cause instanceof BaseError\n            ? args.cause.details\n            : args.cause?.message\n                ? args.cause.message\n                : args.details;\n        const docsPath = args.cause instanceof BaseError\n            ? args.cause.docsPath || args.docsPath\n            : args.docsPath;\n        const message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n            ...(docsPath ? [`Docs: https://abitype.dev${docsPath}`] : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: abitype@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`,\n        ].join('\\n');\n        super(message);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'AbiTypeError'\n        });\n        if (args.cause)\n            this.cause = args.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = args.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js":
/*!************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidAbiItemError: () => (/* binding */ InvalidAbiItemError),\n/* harmony export */   UnknownSolidityTypeError: () => (/* binding */ UnknownSolidityTypeError),\n/* harmony export */   UnknownTypeError: () => (/* binding */ UnknownTypeError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidAbiItemError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature }) {\n        super('Failed to parse ABI item.', {\n            details: `parseAbiItem(${JSON.stringify(signature, null, 2)})`,\n            docsPath: '/api/human#parseabiitem-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiItemError'\n        });\n    }\n}\nclass UnknownTypeError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [\n                `Type \"${type}\" is not a valid ABI type. Perhaps you forgot to include a struct signature?`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownTypeError'\n        });\n    }\n}\nclass UnknownSolidityTypeError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ type }) {\n        super('Unknown type.', {\n            metaMessages: [`Type \"${type}\" is not a valid ABI type.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSolidityTypeError'\n        });\n    }\n}\n//# sourceMappingURL=abiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidAbiParameterError: () => (/* binding */ InvalidAbiParameterError),\n/* harmony export */   InvalidAbiParametersError: () => (/* binding */ InvalidAbiParametersError),\n/* harmony export */   InvalidAbiTypeParameterError: () => (/* binding */ InvalidAbiTypeParameterError),\n/* harmony export */   InvalidFunctionModifierError: () => (/* binding */ InvalidFunctionModifierError),\n/* harmony export */   InvalidModifierError: () => (/* binding */ InvalidModifierError),\n/* harmony export */   InvalidParameterError: () => (/* binding */ InvalidParameterError),\n/* harmony export */   SolidityProtectedKeywordError: () => (/* binding */ SolidityProtectedKeywordError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidAbiParameterError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param }) {\n        super('Failed to parse ABI parameter.', {\n            details: `parseAbiParameter(${JSON.stringify(param, null, 2)})`,\n            docsPath: '/api/human#parseabiparameter-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParameterError'\n        });\n    }\n}\nclass InvalidAbiParametersError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ params }) {\n        super('Failed to parse ABI parameters.', {\n            details: `parseAbiParameters(${JSON.stringify(params, null, 2)})`,\n            docsPath: '/api/human#parseabiparameters-1',\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiParametersError'\n        });\n    }\n}\nclass InvalidParameterError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParameterError'\n        });\n    }\n}\nclass SolidityProtectedKeywordError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param, name }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `\"${name}\" is a protected Solidity keyword. More info: https://docs.soliditylang.org/en/latest/cheatsheet.html`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SolidityProtectedKeywordError'\n        });\n    }\n}\nclass InvalidModifierError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidModifierError'\n        });\n    }\n}\nclass InvalidFunctionModifierError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ param, type, modifier, }) {\n        super('Invalid ABI parameter.', {\n            details: param,\n            metaMessages: [\n                `Modifier \"${modifier}\" not allowed${type ? ` in \"${type}\" type` : ''}.`,\n                `Data location can only be specified for array, struct, or mapping types, but \"${modifier}\" was given.`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidFunctionModifierError'\n        });\n    }\n}\nclass InvalidAbiTypeParameterError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ abiParameter, }) {\n        super('Invalid ABI parameter.', {\n            details: JSON.stringify(abiParameter, null, 2),\n            metaMessages: ['ABI parameter type is invalid.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidAbiTypeParameterError'\n        });\n    }\n}\n//# sourceMappingURL=abiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/signature.js":
/*!**************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/signature.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidSignatureError: () => (/* binding */ InvalidSignatureError),\n/* harmony export */   InvalidStructSignatureError: () => (/* binding */ InvalidStructSignatureError),\n/* harmony export */   UnknownSignatureError: () => (/* binding */ UnknownSignatureError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidSignatureError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature, type, }) {\n        super(`Invalid ${type} signature.`, {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidSignatureError'\n        });\n    }\n}\nclass UnknownSignatureError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature }) {\n        super('Unknown signature.', {\n            details: signature,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'UnknownSignatureError'\n        });\n    }\n}\nclass InvalidStructSignatureError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ signature }) {\n        super('Invalid struct signature.', {\n            details: signature,\n            metaMessages: ['No properties exist.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidStructSignatureError'\n        });\n    }\n}\n//# sourceMappingURL=signature.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/signature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js":
/*!********************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidParenthesisError: () => (/* binding */ InvalidParenthesisError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass InvalidParenthesisError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ current, depth }) {\n        super('Unbalanced parentheses.', {\n            metaMessages: [\n                `\"${current.trim()}\" has too many ${depth > 0 ? 'opening' : 'closing'} parentheses.`,\n            ],\n            details: `Depth \"${depth}\"`,\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'InvalidParenthesisError'\n        });\n    }\n}\n//# sourceMappingURL=splitParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3BsaXRQYXJhbWV0ZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBQ3JDLHNDQUFzQyxpREFBUztBQUN0RCxrQkFBa0IsZ0JBQWdCO0FBQ2xDO0FBQ0E7QUFDQSxvQkFBb0IsZUFBZSxpQkFBaUIsbUNBQW1DO0FBQ3ZGO0FBQ0EsK0JBQStCLE1BQU07QUFDckMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvZXNtL2h1bWFuLXJlYWRhYmxlL2Vycm9ycy9zcGxpdFBhcmFtZXRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi4vLi4vZXJyb3JzLmpzJztcbmV4cG9ydCBjbGFzcyBJbnZhbGlkUGFyZW50aGVzaXNFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyBjdXJyZW50LCBkZXB0aCB9KSB7XG4gICAgICAgIHN1cGVyKCdVbmJhbGFuY2VkIHBhcmVudGhlc2VzLicsIHtcbiAgICAgICAgICAgIG1ldGFNZXNzYWdlczogW1xuICAgICAgICAgICAgICAgIGBcIiR7Y3VycmVudC50cmltKCl9XCIgaGFzIHRvbyBtYW55ICR7ZGVwdGggPiAwID8gJ29wZW5pbmcnIDogJ2Nsb3NpbmcnfSBwYXJlbnRoZXNlcy5gLFxuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIGRldGFpbHM6IGBEZXB0aCBcIiR7ZGVwdGh9XCJgLFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0ludmFsaWRQYXJlbnRoZXNpc0Vycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zcGxpdFBhcmFtZXRlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/struct.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/errors/struct.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CircularReferenceError: () => (/* binding */ CircularReferenceError)\n/* harmony export */ });\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../errors.js */ \"(ssr)/./node_modules/abitype/dist/esm/errors.js\");\n\nclass CircularReferenceError extends _errors_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ type }) {\n        super('Circular reference detected.', {\n            metaMessages: [`Struct \"${type}\" is a circular reference.`],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'CircularReferenceError'\n        });\n    }\n}\n//# sourceMappingURL=struct.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3RydWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBQ3JDLHFDQUFxQyxpREFBUztBQUNyRCxrQkFBa0IsTUFBTTtBQUN4QjtBQUNBLHNDQUFzQyxLQUFLO0FBQzNDLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9lcnJvcnMvc3RydWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJhc2VFcnJvciB9IGZyb20gJy4uLy4uL2Vycm9ycy5qcyc7XG5leHBvcnQgY2xhc3MgQ2lyY3VsYXJSZWZlcmVuY2VFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyB0eXBlIH0pIHtcbiAgICAgICAgc3VwZXIoJ0NpcmN1bGFyIHJlZmVyZW5jZSBkZXRlY3RlZC4nLCB7XG4gICAgICAgICAgICBtZXRhTWVzc2FnZXM6IFtgU3RydWN0IFwiJHt0eXBlfVwiIGlzIGEgY2lyY3VsYXIgcmVmZXJlbmNlLmBdLFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ0NpcmN1bGFyUmVmZXJlbmNlRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN0cnVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/struct.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiItem.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/formatAbiItem.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAbiItem: () => (/* binding */ formatAbiItem)\n/* harmony export */ });\n/* harmony import */ var _formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatAbiParameters.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js\");\n\n/**\n * Formats ABI item (e.g. error, event, function) into human-readable ABI item\n *\n * @param abiItem - ABI item\n * @returns Human-readable ABI item\n */\nfunction formatAbiItem(abiItem) {\n    if (abiItem.type === 'function')\n        return `function ${abiItem.name}(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability && abiItem.stateMutability !== 'nonpayable'\n            ? ` ${abiItem.stateMutability}`\n            : ''}${abiItem.outputs?.length\n            ? ` returns (${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.outputs)})`\n            : ''}`;\n    if (abiItem.type === 'event')\n        return `event ${abiItem.name}(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'error')\n        return `error ${abiItem.name}(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})`;\n    if (abiItem.type === 'constructor')\n        return `constructor(${(0,_formatAbiParameters_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameters)(abiItem.inputs)})${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    if (abiItem.type === 'fallback')\n        return `fallback() external${abiItem.stateMutability === 'payable' ? ' payable' : ''}`;\n    return 'receive() external payable';\n}\n//# sourceMappingURL=formatAbiItem.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js":
/*!****************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAbiParameter: () => (/* binding */ formatAbiParameter)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../regex.js */ \"(ssr)/./node_modules/abitype/dist/esm/regex.js\");\n\n// https://regexr.com/7f7rv\nconst tupleRegex = /^tuple(?<array>(\\[(\\d*)\\])*)$/;\n/**\n * Formats {@link AbiParameter} to human-readable ABI parameter.\n *\n * @param abiParameter - ABI parameter\n * @returns Human-readable ABI parameter\n *\n * @example\n * const result = formatAbiParameter({ type: 'address', name: 'from' })\n * //    ^? const result: 'address from'\n */\nfunction formatAbiParameter(abiParameter) {\n    let type = abiParameter.type;\n    if (tupleRegex.test(abiParameter.type) && 'components' in abiParameter) {\n        type = '(';\n        const length = abiParameter.components.length;\n        for (let i = 0; i < length; i++) {\n            const component = abiParameter.components[i];\n            type += formatAbiParameter(component);\n            if (i < length - 1)\n                type += ', ';\n        }\n        const result = (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(tupleRegex, abiParameter.type);\n        type += `)${result?.array ?? ''}`;\n        return formatAbiParameter({\n            ...abiParameter,\n            type,\n        });\n    }\n    // Add `indexed` to type if in `abiParameter`\n    if ('indexed' in abiParameter && abiParameter.indexed)\n        type = `${type} indexed`;\n    // Return human-readable ABI parameter\n    if (abiParameter.name)\n        return `${type} ${abiParameter.name}`;\n    return type;\n}\n//# sourceMappingURL=formatAbiParameter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatAbiParameters: () => (/* binding */ formatAbiParameters)\n/* harmony export */ });\n/* harmony import */ var _formatAbiParameter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatAbiParameter.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameter.js\");\n\n/**\n * Formats {@link AbiParameter}s to human-readable ABI parameters.\n *\n * @param abiParameters - ABI parameters\n * @returns Human-readable ABI parameters\n *\n * @example\n * const result = formatAbiParameters([\n *   //  ^? const result: 'address from, uint256 tokenId'\n *   { type: 'address', name: 'from' },\n *   { type: 'uint256', name: 'tokenId' },\n * ])\n */\nfunction formatAbiParameters(abiParameters) {\n    let params = '';\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        params += (0,_formatAbiParameter_js__WEBPACK_IMPORTED_MODULE_0__.formatAbiParameter)(abiParameter);\n        if (i !== length - 1)\n            params += ', ';\n    }\n    return params;\n}\n//# sourceMappingURL=formatAbiParameters.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9mb3JtYXRBYmlQYXJhbWV0ZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThEO0FBQzlEO0FBQ0EsWUFBWSxtQkFBbUI7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPLCtCQUErQjtBQUN0QyxPQUFPLGtDQUFrQztBQUN6QztBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0Esb0JBQW9CLFlBQVk7QUFDaEM7QUFDQSxrQkFBa0IsMEVBQWtCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9odW1hbi1yZWFkYWJsZS9mb3JtYXRBYmlQYXJhbWV0ZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZvcm1hdEFiaVBhcmFtZXRlciwgfSBmcm9tICcuL2Zvcm1hdEFiaVBhcmFtZXRlci5qcyc7XG4vKipcbiAqIEZvcm1hdHMge0BsaW5rIEFiaVBhcmFtZXRlcn1zIHRvIGh1bWFuLXJlYWRhYmxlIEFCSSBwYXJhbWV0ZXJzLlxuICpcbiAqIEBwYXJhbSBhYmlQYXJhbWV0ZXJzIC0gQUJJIHBhcmFtZXRlcnNcbiAqIEByZXR1cm5zIEh1bWFuLXJlYWRhYmxlIEFCSSBwYXJhbWV0ZXJzXG4gKlxuICogQGV4YW1wbGVcbiAqIGNvbnN0IHJlc3VsdCA9IGZvcm1hdEFiaVBhcmFtZXRlcnMoW1xuICogICAvLyAgXj8gY29uc3QgcmVzdWx0OiAnYWRkcmVzcyBmcm9tLCB1aW50MjU2IHRva2VuSWQnXG4gKiAgIHsgdHlwZTogJ2FkZHJlc3MnLCBuYW1lOiAnZnJvbScgfSxcbiAqICAgeyB0eXBlOiAndWludDI1NicsIG5hbWU6ICd0b2tlbklkJyB9LFxuICogXSlcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEFiaVBhcmFtZXRlcnMoYWJpUGFyYW1ldGVycykge1xuICAgIGxldCBwYXJhbXMgPSAnJztcbiAgICBjb25zdCBsZW5ndGggPSBhYmlQYXJhbWV0ZXJzLmxlbmd0aDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IGFiaVBhcmFtZXRlciA9IGFiaVBhcmFtZXRlcnNbaV07XG4gICAgICAgIHBhcmFtcyArPSBmb3JtYXRBYmlQYXJhbWV0ZXIoYWJpUGFyYW1ldGVyKTtcbiAgICAgICAgaWYgKGkgIT09IGxlbmd0aCAtIDEpXG4gICAgICAgICAgICBwYXJhbXMgKz0gJywgJztcbiAgICB9XG4gICAgcmV0dXJuIHBhcmFtcztcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWZvcm1hdEFiaVBhcmFtZXRlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/formatAbiParameters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/parseAbi.js":
/*!******************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/parseAbi.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseAbi: () => (/* binding */ parseAbi)\n/* harmony export */ });\n/* harmony import */ var _runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./runtime/signatures.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _runtime_structs_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./runtime/structs.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/structs.js\");\n/* harmony import */ var _runtime_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./runtime/utils.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n/**\n * Parses human-readable ABI into JSON {@link Abi}\n *\n * @param signatures - Human-Readable ABI\n * @returns Parsed {@link Abi}\n *\n * @example\n * const abi = parseAbi([\n *   //  ^? const abi: readonly [{ name: \"balanceOf\"; type: \"function\"; stateMutability:...\n *   'function balanceOf(address owner) view returns (uint256)',\n *   'event Transfer(address indexed from, address indexed to, uint256 amount)',\n * ])\n */\nfunction parseAbi(signatures) {\n    const structs = (0,_runtime_structs_js__WEBPACK_IMPORTED_MODULE_0__.parseStructs)(signatures);\n    const abi = [];\n    const length = signatures.length;\n    for (let i = 0; i < length; i++) {\n        const signature = signatures[i];\n        if ((0,_runtime_signatures_js__WEBPACK_IMPORTED_MODULE_1__.isStructSignature)(signature))\n            continue;\n        abi.push((0,_runtime_utils_js__WEBPACK_IMPORTED_MODULE_2__.parseSignature)(signature, structs));\n    }\n    return abi;\n}\n//# sourceMappingURL=parseAbi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/parseAbi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/cache.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/runtime/cache.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getParameterCacheKey: () => (/* binding */ getParameterCacheKey),\n/* harmony export */   parameterCache: () => (/* binding */ parameterCache)\n/* harmony export */ });\n/**\n * Gets {@link parameterCache} cache key namespaced by {@link type}. This prevents parameters from being accessible to types that don't allow them (e.g. `string indexed foo` not allowed outside of `type: 'event'`).\n * @param param ABI parameter string\n * @param type ABI parameter type\n * @returns Cache key for {@link parameterCache}\n */\nfunction getParameterCacheKey(param, type, structs) {\n    let structKey = '';\n    if (structs)\n        for (const struct of Object.entries(structs)) {\n            if (!struct)\n                continue;\n            let propertyKey = '';\n            for (const property of struct[1]) {\n                propertyKey += `[${property.type}${property.name ? `:${property.name}` : ''}]`;\n            }\n            structKey += `(${struct[0]}{${propertyKey}})`;\n        }\n    if (type)\n        return `${type}:${param}${structKey}`;\n    return param;\n}\n/**\n * Basic cache seeded with common ABI parameter strings.\n *\n * **Note: When seeding more parameters, make sure you benchmark performance. The current number is the ideal balance between performance and having an already existing cache.**\n */\nconst parameterCache = new Map([\n    // Unnamed\n    ['address', { type: 'address' }],\n    ['bool', { type: 'bool' }],\n    ['bytes', { type: 'bytes' }],\n    ['bytes32', { type: 'bytes32' }],\n    ['int', { type: 'int256' }],\n    ['int256', { type: 'int256' }],\n    ['string', { type: 'string' }],\n    ['uint', { type: 'uint256' }],\n    ['uint8', { type: 'uint8' }],\n    ['uint16', { type: 'uint16' }],\n    ['uint24', { type: 'uint24' }],\n    ['uint32', { type: 'uint32' }],\n    ['uint64', { type: 'uint64' }],\n    ['uint96', { type: 'uint96' }],\n    ['uint112', { type: 'uint112' }],\n    ['uint160', { type: 'uint160' }],\n    ['uint192', { type: 'uint192' }],\n    ['uint256', { type: 'uint256' }],\n    // Named\n    ['address owner', { type: 'address', name: 'owner' }],\n    ['address to', { type: 'address', name: 'to' }],\n    ['bool approved', { type: 'bool', name: 'approved' }],\n    ['bytes _data', { type: 'bytes', name: '_data' }],\n    ['bytes data', { type: 'bytes', name: 'data' }],\n    ['bytes signature', { type: 'bytes', name: 'signature' }],\n    ['bytes32 hash', { type: 'bytes32', name: 'hash' }],\n    ['bytes32 r', { type: 'bytes32', name: 'r' }],\n    ['bytes32 root', { type: 'bytes32', name: 'root' }],\n    ['bytes32 s', { type: 'bytes32', name: 's' }],\n    ['string name', { type: 'string', name: 'name' }],\n    ['string symbol', { type: 'string', name: 'symbol' }],\n    ['string tokenURI', { type: 'string', name: 'tokenURI' }],\n    ['uint tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint8 v', { type: 'uint8', name: 'v' }],\n    ['uint256 balance', { type: 'uint256', name: 'balance' }],\n    ['uint256 tokenId', { type: 'uint256', name: 'tokenId' }],\n    ['uint256 value', { type: 'uint256', name: 'value' }],\n    // Indexed\n    [\n        'event:address indexed from',\n        { type: 'address', name: 'from', indexed: true },\n    ],\n    ['event:address indexed to', { type: 'address', name: 'to', indexed: true }],\n    [\n        'event:uint indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n    [\n        'event:uint256 indexed tokenId',\n        { type: 'uint256', name: 'tokenId', indexed: true },\n    ],\n]);\n//# sourceMappingURL=cache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/cache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js":
/*!****************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventModifiers: () => (/* binding */ eventModifiers),\n/* harmony export */   execConstructorSignature: () => (/* binding */ execConstructorSignature),\n/* harmony export */   execErrorSignature: () => (/* binding */ execErrorSignature),\n/* harmony export */   execEventSignature: () => (/* binding */ execEventSignature),\n/* harmony export */   execFallbackSignature: () => (/* binding */ execFallbackSignature),\n/* harmony export */   execFunctionSignature: () => (/* binding */ execFunctionSignature),\n/* harmony export */   execStructSignature: () => (/* binding */ execStructSignature),\n/* harmony export */   functionModifiers: () => (/* binding */ functionModifiers),\n/* harmony export */   isConstructorSignature: () => (/* binding */ isConstructorSignature),\n/* harmony export */   isErrorSignature: () => (/* binding */ isErrorSignature),\n/* harmony export */   isEventSignature: () => (/* binding */ isEventSignature),\n/* harmony export */   isFallbackSignature: () => (/* binding */ isFallbackSignature),\n/* harmony export */   isFunctionSignature: () => (/* binding */ isFunctionSignature),\n/* harmony export */   isReceiveSignature: () => (/* binding */ isReceiveSignature),\n/* harmony export */   isStructSignature: () => (/* binding */ isStructSignature),\n/* harmony export */   modifiers: () => (/* binding */ modifiers)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../regex.js */ \"(ssr)/./node_modules/abitype/dist/esm/regex.js\");\n\n// https://regexr.com/7gmok\nconst errorSignatureRegex = /^error (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isErrorSignature(signature) {\n    return errorSignatureRegex.test(signature);\n}\nfunction execErrorSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(errorSignatureRegex, signature);\n}\n// https://regexr.com/7gmoq\nconst eventSignatureRegex = /^event (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/;\nfunction isEventSignature(signature) {\n    return eventSignatureRegex.test(signature);\n}\nfunction execEventSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(eventSignatureRegex, signature);\n}\n// https://regexr.com/7gmot\nconst functionSignatureRegex = /^function (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)(?: (?<scope>external|public{1}))?(?: (?<stateMutability>pure|view|nonpayable|payable{1}))?(?: returns\\s?\\((?<returns>.*?)\\))?$/;\nfunction isFunctionSignature(signature) {\n    return functionSignatureRegex.test(signature);\n}\nfunction execFunctionSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(functionSignatureRegex, signature);\n}\n// https://regexr.com/7gmp3\nconst structSignatureRegex = /^struct (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*) \\{(?<properties>.*?)\\}$/;\nfunction isStructSignature(signature) {\n    return structSignatureRegex.test(signature);\n}\nfunction execStructSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(structSignatureRegex, signature);\n}\n// https://regexr.com/78u01\nconst constructorSignatureRegex = /^constructor\\((?<parameters>.*?)\\)(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isConstructorSignature(signature) {\n    return constructorSignatureRegex.test(signature);\n}\nfunction execConstructorSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(constructorSignatureRegex, signature);\n}\n// https://regexr.com/7srtn\nconst fallbackSignatureRegex = /^fallback\\(\\) external(?:\\s(?<stateMutability>payable{1}))?$/;\nfunction isFallbackSignature(signature) {\n    return fallbackSignatureRegex.test(signature);\n}\nfunction execFallbackSignature(signature) {\n    return (0,_regex_js__WEBPACK_IMPORTED_MODULE_0__.execTyped)(fallbackSignatureRegex, signature);\n}\n// https://regexr.com/78u1k\nconst receiveSignatureRegex = /^receive\\(\\) external payable$/;\nfunction isReceiveSignature(signature) {\n    return receiveSignatureRegex.test(signature);\n}\nconst modifiers = new Set([\n    'memory',\n    'indexed',\n    'storage',\n    'calldata',\n]);\nconst eventModifiers = new Set(['indexed']);\nconst functionModifiers = new Set([\n    'calldata',\n    'memory',\n    'storage',\n]);\n//# sourceMappingURL=signatures.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/structs.js":
/*!*************************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/runtime/structs.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseStructs: () => (/* binding */ parseStructs)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../regex.js */ \"(ssr)/./node_modules/abitype/dist/esm/regex.js\");\n/* harmony import */ var _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\");\n/* harmony import */ var _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\");\n/* harmony import */ var _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/signature.js\");\n/* harmony import */ var _errors_struct_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../errors/struct.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/struct.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js\");\n\n\n\n\n\n\n\nfunction parseStructs(signatures) {\n    // Create \"shallow\" version of each struct (and filter out non-structs or invalid structs)\n    const shallowStructs = {};\n    const signaturesLength = signatures.length;\n    for (let i = 0; i < signaturesLength; i++) {\n        const signature = signatures[i];\n        if (!(0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isStructSignature)(signature))\n            continue;\n        const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execStructSignature)(signature);\n        if (!match)\n            throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'struct' });\n        const properties = match.properties.split(';');\n        const components = [];\n        const propertiesLength = properties.length;\n        for (let k = 0; k < propertiesLength; k++) {\n            const property = properties[k];\n            const trimmed = property.trim();\n            if (!trimmed)\n                continue;\n            const abiParameter = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.parseAbiParameter)(trimmed, {\n                type: 'struct',\n            });\n            components.push(abiParameter);\n        }\n        if (!components.length)\n            throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidStructSignatureError({ signature });\n        shallowStructs[match.name] = components;\n    }\n    // Resolve nested structs inside each parameter\n    const resolvedStructs = {};\n    const entries = Object.entries(shallowStructs);\n    const entriesLength = entries.length;\n    for (let i = 0; i < entriesLength; i++) {\n        const [name, parameters] = entries[i];\n        resolvedStructs[name] = resolveStructs(parameters, shallowStructs);\n    }\n    return resolvedStructs;\n}\nconst typeWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?$/;\nfunction resolveStructs(abiParameters, structs, ancestors = new Set()) {\n    const components = [];\n    const length = abiParameters.length;\n    for (let i = 0; i < length; i++) {\n        const abiParameter = abiParameters[i];\n        const isTuple = _regex_js__WEBPACK_IMPORTED_MODULE_3__.isTupleRegex.test(abiParameter.type);\n        if (isTuple)\n            components.push(abiParameter);\n        else {\n            const match = (0,_regex_js__WEBPACK_IMPORTED_MODULE_3__.execTyped)(typeWithoutTupleRegex, abiParameter.type);\n            if (!match?.type)\n                throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidAbiTypeParameterError({ abiParameter });\n            const { array, type } = match;\n            if (type in structs) {\n                if (ancestors.has(type))\n                    throw new _errors_struct_js__WEBPACK_IMPORTED_MODULE_5__.CircularReferenceError({ type });\n                components.push({\n                    ...abiParameter,\n                    type: `tuple${array ?? ''}`,\n                    components: resolveStructs(structs[type] ?? [], structs, new Set([...ancestors, type])),\n                });\n            }\n            else {\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isSolidityType)(type))\n                    components.push(abiParameter);\n                else\n                    throw new _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_6__.UnknownTypeError({ type });\n            }\n        }\n    }\n    return components;\n}\n//# sourceMappingURL=structs.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/structs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js":
/*!***********************************************************************!*\
  !*** ./node_modules/abitype/dist/esm/human-readable/runtime/utils.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSolidityKeyword: () => (/* binding */ isSolidityKeyword),\n/* harmony export */   isSolidityType: () => (/* binding */ isSolidityType),\n/* harmony export */   isValidDataLocation: () => (/* binding */ isValidDataLocation),\n/* harmony export */   parseAbiParameter: () => (/* binding */ parseAbiParameter),\n/* harmony export */   parseConstructorSignature: () => (/* binding */ parseConstructorSignature),\n/* harmony export */   parseErrorSignature: () => (/* binding */ parseErrorSignature),\n/* harmony export */   parseEventSignature: () => (/* binding */ parseEventSignature),\n/* harmony export */   parseFallbackSignature: () => (/* binding */ parseFallbackSignature),\n/* harmony export */   parseFunctionSignature: () => (/* binding */ parseFunctionSignature),\n/* harmony export */   parseSignature: () => (/* binding */ parseSignature),\n/* harmony export */   splitParameters: () => (/* binding */ splitParameters)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../regex.js */ \"(ssr)/./node_modules/abitype/dist/esm/regex.js\");\n/* harmony import */ var _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../errors/abiItem.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiItem.js\");\n/* harmony import */ var _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/abiParameter.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/abiParameter.js\");\n/* harmony import */ var _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/signature.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/signature.js\");\n/* harmony import */ var _errors_splitParameters_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/splitParameters.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/errors/splitParameters.js\");\n/* harmony import */ var _cache_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cache.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/cache.js\");\n/* harmony import */ var _signatures_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signatures.js */ \"(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/signatures.js\");\n\n\n\n\n\n\n\nfunction parseSignature(signature, structs = {}) {\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isFunctionSignature)(signature))\n        return parseFunctionSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isEventSignature)(signature))\n        return parseEventSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isErrorSignature)(signature))\n        return parseErrorSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isConstructorSignature)(signature))\n        return parseConstructorSignature(signature, structs);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isFallbackSignature)(signature))\n        return parseFallbackSignature(signature);\n    if ((0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.isReceiveSignature)(signature))\n        return {\n            type: 'receive',\n            stateMutability: 'payable',\n        };\n    throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.UnknownSignatureError({ signature });\n}\nfunction parseFunctionSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execFunctionSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'function' });\n    const inputParams = splitParameters(match.parameters);\n    const inputs = [];\n    const inputLength = inputParams.length;\n    for (let i = 0; i < inputLength; i++) {\n        inputs.push(parseAbiParameter(inputParams[i], {\n            modifiers: _signatures_js__WEBPACK_IMPORTED_MODULE_0__.functionModifiers,\n            structs,\n            type: 'function',\n        }));\n    }\n    const outputs = [];\n    if (match.returns) {\n        const outputParams = splitParameters(match.returns);\n        const outputLength = outputParams.length;\n        for (let i = 0; i < outputLength; i++) {\n            outputs.push(parseAbiParameter(outputParams[i], {\n                modifiers: _signatures_js__WEBPACK_IMPORTED_MODULE_0__.functionModifiers,\n                structs,\n                type: 'function',\n            }));\n        }\n    }\n    return {\n        name: match.name,\n        type: 'function',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs,\n        outputs,\n    };\n}\nfunction parseEventSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execEventSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'event' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], {\n            modifiers: _signatures_js__WEBPACK_IMPORTED_MODULE_0__.eventModifiers,\n            structs,\n            type: 'event',\n        }));\n    return { name: match.name, type: 'event', inputs: abiParameters };\n}\nfunction parseErrorSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execErrorSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'error' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'error' }));\n    return { name: match.name, type: 'error', inputs: abiParameters };\n}\nfunction parseConstructorSignature(signature, structs = {}) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execConstructorSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'constructor' });\n    const params = splitParameters(match.parameters);\n    const abiParameters = [];\n    const length = params.length;\n    for (let i = 0; i < length; i++)\n        abiParameters.push(parseAbiParameter(params[i], { structs, type: 'constructor' }));\n    return {\n        type: 'constructor',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n        inputs: abiParameters,\n    };\n}\nfunction parseFallbackSignature(signature) {\n    const match = (0,_signatures_js__WEBPACK_IMPORTED_MODULE_0__.execFallbackSignature)(signature);\n    if (!match)\n        throw new _errors_signature_js__WEBPACK_IMPORTED_MODULE_1__.InvalidSignatureError({ signature, type: 'fallback' });\n    return {\n        type: 'fallback',\n        stateMutability: match.stateMutability ?? 'nonpayable',\n    };\n}\nconst abiParameterWithoutTupleRegex = /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst abiParameterWithTupleRegex = /^\\((?<type>.+?)\\)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/;\nconst dynamicIntegerRegex = /^u?int$/;\nfunction parseAbiParameter(param, options) {\n    // optional namespace cache by `type`\n    const parameterCacheKey = (0,_cache_js__WEBPACK_IMPORTED_MODULE_2__.getParameterCacheKey)(param, options?.type, options?.structs);\n    if (_cache_js__WEBPACK_IMPORTED_MODULE_2__.parameterCache.has(parameterCacheKey))\n        return _cache_js__WEBPACK_IMPORTED_MODULE_2__.parameterCache.get(parameterCacheKey);\n    const isTuple = _regex_js__WEBPACK_IMPORTED_MODULE_3__.isTupleRegex.test(param);\n    const match = (0,_regex_js__WEBPACK_IMPORTED_MODULE_3__.execTyped)(isTuple ? abiParameterWithTupleRegex : abiParameterWithoutTupleRegex, param);\n    if (!match)\n        throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidParameterError({ param });\n    if (match.name && isSolidityKeyword(match.name))\n        throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.SolidityProtectedKeywordError({ param, name: match.name });\n    const name = match.name ? { name: match.name } : {};\n    const indexed = match.modifier === 'indexed' ? { indexed: true } : {};\n    const structs = options?.structs ?? {};\n    let type;\n    let components = {};\n    if (isTuple) {\n        type = 'tuple';\n        const params = splitParameters(match.type);\n        const components_ = [];\n        const length = params.length;\n        for (let i = 0; i < length; i++) {\n            // remove `modifiers` from `options` to prevent from being added to tuple components\n            components_.push(parseAbiParameter(params[i], { structs }));\n        }\n        components = { components: components_ };\n    }\n    else if (match.type in structs) {\n        type = 'tuple';\n        components = { components: structs[match.type] };\n    }\n    else if (dynamicIntegerRegex.test(match.type)) {\n        type = `${match.type}256`;\n    }\n    else {\n        type = match.type;\n        if (!(options?.type === 'struct') && !isSolidityType(type))\n            throw new _errors_abiItem_js__WEBPACK_IMPORTED_MODULE_5__.UnknownSolidityTypeError({ type });\n    }\n    if (match.modifier) {\n        // Check if modifier exists, but is not allowed (e.g. `indexed` in `functionModifiers`)\n        if (!options?.modifiers?.has?.(match.modifier))\n            throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n        // Check if resolved `type` is valid if there is a function modifier\n        if (_signatures_js__WEBPACK_IMPORTED_MODULE_0__.functionModifiers.has(match.modifier) &&\n            !isValidDataLocation(type, !!match.array))\n            throw new _errors_abiParameter_js__WEBPACK_IMPORTED_MODULE_4__.InvalidFunctionModifierError({\n                param,\n                type: options?.type,\n                modifier: match.modifier,\n            });\n    }\n    const abiParameter = {\n        type: `${type}${match.array ?? ''}`,\n        ...name,\n        ...indexed,\n        ...components,\n    };\n    _cache_js__WEBPACK_IMPORTED_MODULE_2__.parameterCache.set(parameterCacheKey, abiParameter);\n    return abiParameter;\n}\n// s/o latika for this\nfunction splitParameters(params, result = [], current = '', depth = 0) {\n    const length = params.trim().length;\n    // biome-ignore lint/correctness/noUnreachable: recursive\n    for (let i = 0; i < length; i++) {\n        const char = params[i];\n        const tail = params.slice(i + 1);\n        switch (char) {\n            case ',':\n                return depth === 0\n                    ? splitParameters(tail, [...result, current.trim()])\n                    : splitParameters(tail, result, `${current}${char}`, depth);\n            case '(':\n                return splitParameters(tail, result, `${current}${char}`, depth + 1);\n            case ')':\n                return splitParameters(tail, result, `${current}${char}`, depth - 1);\n            default:\n                return splitParameters(tail, result, `${current}${char}`, depth);\n        }\n    }\n    if (current === '')\n        return result;\n    if (depth !== 0)\n        throw new _errors_splitParameters_js__WEBPACK_IMPORTED_MODULE_6__.InvalidParenthesisError({ current, depth });\n    result.push(current.trim());\n    return result;\n}\nfunction isSolidityType(type) {\n    return (type === 'address' ||\n        type === 'bool' ||\n        type === 'function' ||\n        type === 'string' ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.bytesRegex.test(type) ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.integerRegex.test(type));\n}\nconst protectedKeywordsRegex = /^(?:after|alias|anonymous|apply|auto|byte|calldata|case|catch|constant|copyof|default|defined|error|event|external|false|final|function|immutable|implements|in|indexed|inline|internal|let|mapping|match|memory|mutable|null|of|override|partial|private|promise|public|pure|reference|relocatable|return|returns|sizeof|static|storage|struct|super|supports|switch|this|true|try|typedef|typeof|var|view|virtual)$/;\n/** @internal */\nfunction isSolidityKeyword(name) {\n    return (name === 'address' ||\n        name === 'bool' ||\n        name === 'function' ||\n        name === 'string' ||\n        name === 'tuple' ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.bytesRegex.test(name) ||\n        _regex_js__WEBPACK_IMPORTED_MODULE_3__.integerRegex.test(name) ||\n        protectedKeywordsRegex.test(name));\n}\n/** @internal */\nfunction isValidDataLocation(type, isArray) {\n    return isArray || type === 'bytes' || type === 'string' || type === 'tuple';\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/human-readable/runtime/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/regex.js":
/*!************************************************!*\
  !*** ./node_modules/abitype/dist/esm/regex.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bytesRegex: () => (/* binding */ bytesRegex),\n/* harmony export */   execTyped: () => (/* binding */ execTyped),\n/* harmony export */   integerRegex: () => (/* binding */ integerRegex),\n/* harmony export */   isTupleRegex: () => (/* binding */ isTupleRegex)\n/* harmony export */ });\n// TODO: This looks cool. Need to check the performance of `new RegExp` versus defined inline though.\n// https://twitter.com/GabrielVergnaud/status/1622906834343366657\nfunction execTyped(regex, string) {\n    const match = regex.exec(string);\n    return match?.groups;\n}\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nconst bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/;\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nconst integerRegex = /^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;\nconst isTupleRegex = /^\\(.+?\\).*?$/;\n//# sourceMappingURL=regex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9yZWdleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ0E7QUFDUCIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS9yZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUT0RPOiBUaGlzIGxvb2tzIGNvb2wuIE5lZWQgdG8gY2hlY2sgdGhlIHBlcmZvcm1hbmNlIG9mIGBuZXcgUmVnRXhwYCB2ZXJzdXMgZGVmaW5lZCBpbmxpbmUgdGhvdWdoLlxuLy8gaHR0cHM6Ly90d2l0dGVyLmNvbS9HYWJyaWVsVmVyZ25hdWQvc3RhdHVzLzE2MjI5MDY4MzQzNDMzNjY2NTdcbmV4cG9ydCBmdW5jdGlvbiBleGVjVHlwZWQocmVnZXgsIHN0cmluZykge1xuICAgIGNvbnN0IG1hdGNoID0gcmVnZXguZXhlYyhzdHJpbmcpO1xuICAgIHJldHVybiBtYXRjaD8uZ3JvdXBzO1xufVxuLy8gYGJ5dGVzPE0+YDogYmluYXJ5IHR5cGUgb2YgYE1gIGJ5dGVzLCBgMCA8IE0gPD0gMzJgXG4vLyBodHRwczovL3JlZ2V4ci5jb20vNnZhNTVcbmV4cG9ydCBjb25zdCBieXRlc1JlZ2V4ID0gL15ieXRlcyhbMS05XXwxWzAtOV18MlswLTldfDNbMC0yXSk/JC87XG4vLyBgKHUpaW50PE0+YDogKHVuKXNpZ25lZCBpbnRlZ2VyIHR5cGUgb2YgYE1gIGJpdHMsIGAwIDwgTSA8PSAyNTZgLCBgTSAlIDggPT0gMGBcbi8vIGh0dHBzOi8vcmVnZXhyLmNvbS82djhocFxuZXhwb3J0IGNvbnN0IGludGVnZXJSZWdleCA9IC9edT9pbnQoOHwxNnwyNHwzMnw0MHw0OHw1Nnw2NHw3Mnw4MHw4OHw5NnwxMDR8MTEyfDEyMHwxMjh8MTM2fDE0NHwxNTJ8MTYwfDE2OHwxNzZ8MTg0fDE5MnwyMDB8MjA4fDIxNnwyMjR8MjMyfDI0MHwyNDh8MjU2KT8kLztcbmV4cG9ydCBjb25zdCBpc1R1cGxlUmVnZXggPSAvXlxcKC4rP1xcKS4qPyQvO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVnZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/regex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/abitype/dist/esm/version.js":
/*!**************************************************!*\
  !*** ./node_modules/abitype/dist/esm/version.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '1.0.8';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYWJpdHlwZS9kaXN0L2VzbS92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9hYml0eXBlL2Rpc3QvZXNtL3ZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMS4wLjgnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/abitype/dist/esm/version.js\n");

/***/ })

};
;