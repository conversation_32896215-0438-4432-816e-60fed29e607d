"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/json-stringify-pretty-compact";
exports.ids = ["vendor-chunks/json-stringify-pretty-compact"];
exports.modules = {

/***/ "(ssr)/./node_modules/json-stringify-pretty-compact/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/json-stringify-pretty-compact/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ stringify)\n/* harmony export */ });\n// Note: This regex matches even invalid JSON strings, but since we’re\n// working on the output of `JSON.stringify` we know that only valid strings\n// are present (unless the user supplied a weird `options.indent` but in\n// that case we don’t care since the output would be invalid anyway).\nconst stringOrChar = /(\"(?:[^\\\\\"]|\\\\.)*\")|[:,]/g;\n\nfunction stringify(passedObj, options = {}) {\n  const indent = JSON.stringify(\n    [1],\n    undefined,\n    options.indent === undefined ? 2 : options.indent\n  ).slice(2, -3);\n\n  const maxLength =\n    indent === \"\"\n      ? Infinity\n      : options.maxLength === undefined\n      ? 80\n      : options.maxLength;\n\n  let { replacer } = options;\n\n  return (function _stringify(obj, currentIndent, reserved) {\n    if (obj && typeof obj.toJSON === \"function\") {\n      obj = obj.toJSON();\n    }\n\n    const string = JSON.stringify(obj, replacer);\n\n    if (string === undefined) {\n      return string;\n    }\n\n    const length = maxLength - currentIndent.length - reserved;\n\n    if (string.length <= length) {\n      const prettified = string.replace(\n        stringOrChar,\n        (match, stringLiteral) => {\n          return stringLiteral || `${match} `;\n        }\n      );\n      if (prettified.length <= length) {\n        return prettified;\n      }\n    }\n\n    if (replacer != null) {\n      obj = JSON.parse(string);\n      replacer = undefined;\n    }\n\n    if (typeof obj === \"object\" && obj !== null) {\n      const nextIndent = currentIndent + indent;\n      const items = [];\n      let index = 0;\n      let start;\n      let end;\n\n      if (Array.isArray(obj)) {\n        start = \"[\";\n        end = \"]\";\n        const { length } = obj;\n        for (; index < length; index++) {\n          items.push(\n            _stringify(obj[index], nextIndent, index === length - 1 ? 0 : 1) ||\n              \"null\"\n          );\n        }\n      } else {\n        start = \"{\";\n        end = \"}\";\n        const keys = Object.keys(obj);\n        const { length } = keys;\n        for (; index < length; index++) {\n          const key = keys[index];\n          const keyPart = `${JSON.stringify(key)}: `;\n          const value = _stringify(\n            obj[key],\n            nextIndent,\n            keyPart.length + (index === length - 1 ? 0 : 1)\n          );\n          if (value !== undefined) {\n            items.push(keyPart + value);\n          }\n        }\n      }\n\n      if (items.length > 0) {\n        return [start, indent + items.join(`,\\n${nextIndent}`), end].join(\n          `\\n${currentIndent}`\n        );\n      }\n    }\n\n    return string;\n  })(passedObj, \"\", 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/json-stringify-pretty-compact/index.js\n");

/***/ })

};
;