"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile";
exports.ids = ["vendor-chunks/vfile"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/vfile/lib/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VFile: () => (/* binding */ VFile)\n/* harmony export */ });\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! vfile-message */ \"(ssr)/./node_modules/vfile-message/lib/index.js\");\n/* harmony import */ var _minpath__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! #minpath */ \"node:path\");\n/* harmony import */ var _minproc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! #minproc */ \"node:process\");\n/* harmony import */ var _minurl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! #minurl */ \"(ssr)/./node_modules/vfile/lib/minurl.shared.js\");\n/* harmony import */ var _minurl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! #minurl */ \"node:url\");\n/**\n * @import {Node, Point, Position} from 'unist'\n * @import {Options as MessageOptions} from 'vfile-message'\n * @import {Compatible, Data, Map, Options, Value} from 'vfile'\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\n\n\n\n\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n */\nconst order = /** @type {const} */ ([\n  'history',\n  'path',\n  'basename',\n  'stem',\n  'extname',\n  'dirname'\n])\n\nclass VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Uint8Array` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if ((0,_minurl__WEBPACK_IMPORTED_MODULE_0__.isUrl)(value)) {\n      options = {path: value}\n    } else if (typeof value === 'string' || isUint8Array(value)) {\n      options = {value}\n    } else {\n      options = value\n    }\n\n    /* eslint-disable no-unused-expressions */\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    // Prevent calling `cwd` (which could be expensive) if it’s not needed;\n    // the empty string will be overridden in the next block.\n    this.cwd = 'cwd' in options ? '' : _minproc__WEBPACK_IMPORTED_MODULE_1__.cwd()\n\n    /**\n     * Place to store custom info (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of file paths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const field = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        field in options &&\n        options[field] !== undefined &&\n        options[field] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[field] = field === 'history' ? [...options[field]] : options[field]\n      }\n    }\n\n    /** @type {string} */\n    let field\n\n    // Set non-path related properties.\n    for (field in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(field)) {\n        // @ts-expect-error: fine to set other things.\n        this[field] = options[field]\n      }\n    }\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   *\n   * @returns {string | undefined}\n   *   Basename.\n   */\n  get basename() {\n    return typeof this.path === 'string'\n      ? _minpath__WEBPACK_IMPORTED_MODULE_2__.basename(this.path)\n      : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} basename\n   *   Basename.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   *\n   * @returns {string | undefined}\n   *   Dirname.\n   */\n  get dirname() {\n    return typeof this.path === 'string'\n      ? _minpath__WEBPACK_IMPORTED_MODULE_2__.dirname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} dirname\n   *   Dirname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   *\n   * @returns {string | undefined}\n   *   Extname.\n   */\n  get extname() {\n    return typeof this.path === 'string'\n      ? _minpath__WEBPACK_IMPORTED_MODULE_2__.extname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} extname\n   *   Extname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.codePointAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   *   Path.\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {URL | string} path\n   *   Path.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set path(path) {\n    if ((0,_minurl__WEBPACK_IMPORTED_MODULE_0__.isUrl)(path)) {\n      path = (0,_minurl__WEBPACK_IMPORTED_MODULE_3__.fileURLToPath)(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * @returns {string | undefined}\n   *   Stem.\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? _minpath__WEBPACK_IMPORTED_MODULE_2__.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} stem\n   *   Stem.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  // Normal prototypal methods.\n  /**\n   * Create a fatal message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `true` (error; file not usable)\n   * and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Never.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n\n  /**\n   * Create an info message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `undefined` (info; change\n   * likely not needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = undefined\n\n    return message\n  }\n\n  /**\n   * Create a message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `false` (warning; change may be\n   * needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(causeOrReason, optionsOrParentOrPlace, origin) {\n    const message = new vfile_message__WEBPACK_IMPORTED_MODULE_4__.VFileMessage(\n      // @ts-expect-error: the overloads are fine.\n      causeOrReason,\n      optionsOrParentOrPlace,\n      origin\n    )\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * > **Note**: which encodings are supported depends on the engine.\n   * > For info on Node.js, see:\n   * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n   *\n   * @param {string | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Uint8Array`\n   *   (default: `'utf-8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    if (this.value === undefined) {\n      return ''\n    }\n\n    if (typeof this.value === 'string') {\n      return this.value\n    }\n\n    const decoder = new TextDecoder(encoding || undefined)\n    return decoder.decode(this.value)\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {undefined}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(_minpath__WEBPACK_IMPORTED_MODULE_2__.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + _minpath__WEBPACK_IMPORTED_MODULE_2__.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vfile/lib/minurl.shared.js":
/*!*************************************************!*\
  !*** ./node_modules/vfile/lib/minurl.shared.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isUrl: () => (/* binding */ isUrl)\n/* harmony export */ });\n/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * We check for auth attribute to distinguish legacy url instance with\n * WHATWG URL instance.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js#L720>\nfunction isUrl(fileUrlOrPath) {\n  return Boolean(\n    fileUrlOrPath !== null &&\n      typeof fileUrlOrPath === 'object' &&\n      'href' in fileUrlOrPath &&\n      fileUrlOrPath.href &&\n      'protocol' in fileUrlOrPath &&\n      fileUrlOrPath.protocol &&\n      // @ts-expect-error: indexing is fine.\n      fileUrlOrPath.auth === undefined\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile/lib/minurl.shared.js\n");

/***/ })

};
;