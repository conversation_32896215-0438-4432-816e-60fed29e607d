"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wagmi";
exports.ids = ["vendor-chunks/@wagmi"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getAccount.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccount: () => (/* binding */ getAccount)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/getAccount */\nfunction getAccount(config) {\n    const uid = config.state.current;\n    const connection = config.state.connections.get(uid);\n    const addresses = connection?.accounts;\n    const address = addresses?.[0];\n    const chain = config.chains.find((chain) => chain.id === connection?.chainId);\n    const status = config.state.status;\n    switch (status) {\n        case 'connected':\n            return {\n                address: address,\n                addresses: addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: true,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'reconnecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: !!address,\n                isConnecting: false,\n                isDisconnected: false,\n                isReconnecting: true,\n                status,\n            };\n        case 'connecting':\n            return {\n                address,\n                addresses,\n                chain,\n                chainId: connection?.chainId,\n                connector: connection?.connector,\n                isConnected: false,\n                isConnecting: true,\n                isDisconnected: false,\n                isReconnecting: false,\n                status,\n            };\n        case 'disconnected':\n            return {\n                address: undefined,\n                addresses: undefined,\n                chain: undefined,\n                chainId: undefined,\n                connector: undefined,\n                isConnected: false,\n                isConnecting: false,\n                isDisconnected: true,\n                isReconnecting: false,\n                status,\n            };\n    }\n}\n//# sourceMappingURL=getAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getBalance.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getBalance.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBalance: () => (/* binding */ getBalance)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/data/trim.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/unit/formatUnits.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/getBalance.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/getUnit.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getUnit.js\");\n/* harmony import */ var _readContracts_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./readContracts.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContracts.js\");\n\n\n\n\n\n/** https://wagmi.sh/core/api/actions/getBalance */\nasync function getBalance(config, parameters) {\n    const { address, blockNumber, blockTag, chainId, token: tokenAddress, unit = 'ether', } = parameters;\n    if (tokenAddress) {\n        try {\n            return await getTokenBalance(config, {\n                balanceAddress: address,\n                chainId,\n                symbolType: 'string',\n                tokenAddress,\n            });\n        }\n        catch (error) {\n            // In the chance that there is an error upon decoding the contract result,\n            // it could be likely that the contract data is represented as bytes32 instead\n            // of a string.\n            if (error.name ===\n                'ContractFunctionExecutionError') {\n                const balance = await getTokenBalance(config, {\n                    balanceAddress: address,\n                    chainId,\n                    symbolType: 'bytes32',\n                    tokenAddress,\n                });\n                const symbol = (0,viem__WEBPACK_IMPORTED_MODULE_0__.hexToString)((0,viem__WEBPACK_IMPORTED_MODULE_1__.trim)(balance.symbol, { dir: 'right' }));\n                return { ...balance, symbol };\n            }\n            throw error;\n        }\n    }\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_2__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_3__.getBalance, 'getBalance');\n    const value = await action(blockNumber ? { address, blockNumber } : { address, blockTag });\n    const chain = config.chains.find((x) => x.id === chainId) ?? client.chain;\n    return {\n        decimals: chain.nativeCurrency.decimals,\n        formatted: (0,viem__WEBPACK_IMPORTED_MODULE_4__.formatUnits)(value, (0,_utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__.getUnit)(unit)),\n        symbol: chain.nativeCurrency.symbol,\n        value,\n    };\n}\nasync function getTokenBalance(config, parameters) {\n    const { balanceAddress, chainId, symbolType, tokenAddress, unit } = parameters;\n    const contract = {\n        abi: [\n            {\n                type: 'function',\n                name: 'balanceOf',\n                stateMutability: 'view',\n                inputs: [{ type: 'address' }],\n                outputs: [{ type: 'uint256' }],\n            },\n            {\n                type: 'function',\n                name: 'decimals',\n                stateMutability: 'view',\n                inputs: [],\n                outputs: [{ type: 'uint8' }],\n            },\n            {\n                type: 'function',\n                name: 'symbol',\n                stateMutability: 'view',\n                inputs: [],\n                outputs: [{ type: symbolType }],\n            },\n        ],\n        address: tokenAddress,\n    };\n    const [value, decimals, symbol] = await (0,_readContracts_js__WEBPACK_IMPORTED_MODULE_6__.readContracts)(config, {\n        allowFailure: false,\n        contracts: [\n            {\n                ...contract,\n                functionName: 'balanceOf',\n                args: [balanceAddress],\n                chainId,\n            },\n            { ...contract, functionName: 'decimals', chainId },\n            { ...contract, functionName: 'symbol', chainId },\n        ],\n    });\n    const formatted = (0,viem__WEBPACK_IMPORTED_MODULE_4__.formatUnits)(value ?? '0', (0,_utils_getUnit_js__WEBPACK_IMPORTED_MODULE_5__.getUnit)(unit ?? decimals));\n    return { decimals, formatted, symbol, value };\n}\n//# sourceMappingURL=getBalance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getBalance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getChainId.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getChainId.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChainId: () => (/* binding */ getChainId)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/getChainId */\nfunction getChainId(config) {\n    return config.state.chainId;\n}\n//# sourceMappingURL=getChainId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9nZXRDaGFpbklkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL2dldENoYWluSWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy9nZXRDaGFpbklkICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q2hhaW5JZChjb25maWcpIHtcbiAgICByZXR1cm4gY29uZmlnLnN0YXRlLmNoYWluSWQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRDaGFpbklkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getChainId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getConnectorClient: () => (/* binding */ getConnectorClient)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/transports/custom.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/accounts/utils/parseAccount.js\");\n/* harmony import */ var viem_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/utils */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/getConnectorClient */\nasync function getConnectorClient(config, parameters = {}) {\n    // Get connection\n    let connection;\n    if (parameters.connector) {\n        const { connector } = parameters;\n        if (config.state.status === 'reconnecting' &&\n            !connector.getAccounts &&\n            !connector.getChainId)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorUnavailableReconnectingError({ connector });\n        const [accounts, chainId] = await Promise.all([\n            connector.getAccounts().catch((e) => {\n                if (parameters.account === null)\n                    return [];\n                throw e;\n            }),\n            connector.getChainId(),\n        ]);\n        connection = {\n            accounts: accounts,\n            chainId,\n            connector,\n        };\n    }\n    else\n        connection = config.state.connections.get(config.state.current);\n    if (!connection)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorNotConnectedError();\n    const chainId = parameters.chainId ?? connection.chainId;\n    // Check connector using same chainId as connection\n    const connectorChainId = await connection.connector.getChainId();\n    if (connectorChainId !== connection.chainId)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorChainMismatchError({\n            connectionChainId: connection.chainId,\n            connectorChainId,\n        });\n    const connector = connection.connector;\n    if (connector.getClient)\n        return connector.getClient({ chainId });\n    // Default using `custom` transport\n    const account = (0,viem_utils__WEBPACK_IMPORTED_MODULE_1__.parseAccount)(parameters.account ?? connection.accounts[0]);\n    if (account)\n        account.address = (0,viem_utils__WEBPACK_IMPORTED_MODULE_2__.getAddress)(account.address); // TODO: Checksum address as part of `parseAccount`?\n    // If account was provided, check that it exists on the connector\n    if (parameters.account &&\n        !connection.accounts.some((x) => x.toLowerCase() === account.address.toLowerCase()))\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_0__.ConnectorAccountNotFoundError({\n            address: account.address,\n            connector,\n        });\n    const chain = config.chains.find((chain) => chain.id === chainId);\n    const provider = (await connection.connector.getProvider({ chainId }));\n    return (0,viem__WEBPACK_IMPORTED_MODULE_3__.createClient)({\n        account,\n        chain,\n        name: 'Connector Client',\n        transport: (opts) => (0,viem__WEBPACK_IMPORTED_MODULE_4__.custom)(provider)({ ...opts, retryCount: 0 }),\n    });\n}\n//# sourceMappingURL=getConnectorClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/multicall.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/multicall.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   multicall: () => (/* binding */ multicall)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/multicall.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\nasync function multicall(config, parameters) {\n    const { allowFailure = true, chainId, contracts, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.multicall, 'multicall');\n    return action({\n        allowFailure,\n        contracts,\n        ...rest,\n    });\n}\n//# sourceMappingURL=multicall.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9tdWx0aWNhbGwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJEO0FBQ1Q7QUFDM0M7QUFDUCxZQUFZLG1EQUFtRDtBQUMvRCxzQ0FBc0MsU0FBUztBQUMvQyxtQkFBbUIsOERBQVMsU0FBUyxtREFBYztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL211bHRpY2FsbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtdWx0aWNhbGwgYXMgdmllbV9tdWx0aWNhbGwgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBtdWx0aWNhbGwoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBhbGxvd0ZhaWx1cmUgPSB0cnVlLCBjaGFpbklkLCBjb250cmFjdHMsIC4uLnJlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgY29uc3QgY2xpZW50ID0gY29uZmlnLmdldENsaWVudCh7IGNoYWluSWQgfSk7XG4gICAgY29uc3QgYWN0aW9uID0gZ2V0QWN0aW9uKGNsaWVudCwgdmllbV9tdWx0aWNhbGwsICdtdWx0aWNhbGwnKTtcbiAgICByZXR1cm4gYWN0aW9uKHtcbiAgICAgICAgYWxsb3dGYWlsdXJlLFxuICAgICAgICBjb250cmFjdHMsXG4gICAgICAgIC4uLnJlc3QsXG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tdWx0aWNhbGwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/multicall.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/readContract.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContract: () => (/* binding */ readContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/readContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n/** https://wagmi.sh/core/api/actions/readContract */\nfunction readContract(config, parameters) {\n    const { chainId, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.readContract, 'readContract');\n    return action(rest);\n}\n//# sourceMappingURL=readContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9yZWFkQ29udHJhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtFO0FBQ2hCO0FBQ2xEO0FBQ087QUFDUCxZQUFZLG1CQUFtQjtBQUMvQixzQ0FBc0MsU0FBUztBQUMvQyxtQkFBbUIsOERBQVMsU0FBUyxzREFBaUI7QUFDdEQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL3JlYWRDb250cmFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZWFkQ29udHJhY3QgYXMgdmllbV9yZWFkQ29udHJhY3QsIH0gZnJvbSAndmllbS9hY3Rpb25zJztcbmltcG9ydCB7IGdldEFjdGlvbiB9IGZyb20gJy4uL3V0aWxzL2dldEFjdGlvbi5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3JlYWRDb250cmFjdCAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJlYWRDb250cmFjdChjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IGNoYWluSWQsIC4uLnJlc3QgfSA9IHBhcmFtZXRlcnM7XG4gICAgY29uc3QgY2xpZW50ID0gY29uZmlnLmdldENsaWVudCh7IGNoYWluSWQgfSk7XG4gICAgY29uc3QgYWN0aW9uID0gZ2V0QWN0aW9uKGNsaWVudCwgdmllbV9yZWFkQ29udHJhY3QsICdyZWFkQ29udHJhY3QnKTtcbiAgICByZXR1cm4gYWN0aW9uKHJlc3QpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVhZENvbnRyYWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContracts.js":
/*!********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/readContracts.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readContracts: () => (/* binding */ readContracts)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/contract.js\");\n/* harmony import */ var _multicall_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./multicall.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/multicall.js\");\n/* harmony import */ var _readContract_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./readContract.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContract.js\");\n\n\n\nasync function readContracts(config, parameters) {\n    const { allowFailure = true, blockNumber, blockTag, ...rest } = parameters;\n    const contracts = parameters.contracts;\n    try {\n        const contractsByChainId = {};\n        for (const [index, contract] of contracts.entries()) {\n            const chainId = contract.chainId ?? config.state.chainId;\n            if (!contractsByChainId[chainId])\n                contractsByChainId[chainId] = [];\n            contractsByChainId[chainId]?.push({ contract, index });\n        }\n        const promises = () => Object.entries(contractsByChainId).map(([chainId, contracts]) => (0,_multicall_js__WEBPACK_IMPORTED_MODULE_0__.multicall)(config, {\n            ...rest,\n            allowFailure,\n            blockNumber,\n            blockTag,\n            chainId: Number.parseInt(chainId),\n            contracts: contracts.map(({ contract }) => contract),\n        }));\n        const multicallResults = (await Promise.all(promises())).flat();\n        // Reorder the contract results back to the order they were\n        // provided in.\n        const resultIndexes = Object.values(contractsByChainId).flatMap((contracts) => contracts.map(({ index }) => index));\n        return multicallResults.reduce((results, result, index) => {\n            if (results)\n                results[resultIndexes[index]] = result;\n            return results;\n        }, []);\n    }\n    catch (error) {\n        if (error instanceof viem__WEBPACK_IMPORTED_MODULE_1__.ContractFunctionExecutionError)\n            throw error;\n        const promises = () => contracts.map((contract) => (0,_readContract_js__WEBPACK_IMPORTED_MODULE_2__.readContract)(config, { ...contract, blockNumber, blockTag }));\n        if (allowFailure)\n            return (await Promise.allSettled(promises())).map((result) => {\n                if (result.status === 'fulfilled')\n                    return { result: result.value, status: 'success' };\n                return { error: result.reason, result: undefined, status: 'failure' };\n            });\n        return (await Promise.all(promises()));\n    }\n}\n//# sourceMappingURL=readContracts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/readContracts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/reconnect.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reconnect: () => (/* binding */ reconnect)\n/* harmony export */ });\nlet isReconnecting = false;\n/** https://wagmi.sh/core/api/actions/reconnect */\nasync function reconnect(config, parameters = {}) {\n    // If already reconnecting, do nothing\n    if (isReconnecting)\n        return [];\n    isReconnecting = true;\n    config.setState((x) => ({\n        ...x,\n        status: x.current ? 'reconnecting' : 'connecting',\n    }));\n    const connectors = [];\n    if (parameters.connectors?.length) {\n        for (const connector_ of parameters.connectors) {\n            let connector;\n            // \"Register\" connector if not already created\n            if (typeof connector_ === 'function')\n                connector = config._internal.connectors.setup(connector_);\n            else\n                connector = connector_;\n            connectors.push(connector);\n        }\n    }\n    else\n        connectors.push(...config.connectors);\n    // Try recently-used connectors first\n    let recentConnectorId;\n    try {\n        recentConnectorId = await config.storage?.getItem('recentConnectorId');\n    }\n    catch { }\n    const scores = {};\n    for (const [, connection] of config.state.connections) {\n        scores[connection.connector.id] = 1;\n    }\n    if (recentConnectorId)\n        scores[recentConnectorId] = 0;\n    const sorted = Object.keys(scores).length > 0\n        ? // .toSorted()\n            [...connectors].sort((a, b) => (scores[a.id] ?? 10) - (scores[b.id] ?? 10))\n        : connectors;\n    // Iterate through each connector and try to connect\n    let connected = false;\n    const connections = [];\n    const providers = [];\n    for (const connector of sorted) {\n        const provider = await connector.getProvider().catch(() => undefined);\n        if (!provider)\n            continue;\n        // If we already have an instance of this connector's provider,\n        // then we have already checked it (ie. injected connectors can\n        // share the same `window.ethereum` instance, so we don't want to\n        // connect to it again).\n        if (providers.some((x) => x === provider))\n            continue;\n        const isAuthorized = await connector.isAuthorized();\n        if (!isAuthorized)\n            continue;\n        const data = await connector\n            .connect({ isReconnecting: true })\n            .catch(() => null);\n        if (!data)\n            continue;\n        connector.emitter.off('connect', config._internal.events.connect);\n        connector.emitter.on('change', config._internal.events.change);\n        connector.emitter.on('disconnect', config._internal.events.disconnect);\n        config.setState((x) => {\n            const connections = new Map(connected ? x.connections : new Map()).set(connector.uid, { accounts: data.accounts, chainId: data.chainId, connector });\n            return {\n                ...x,\n                current: connected ? x.current : connector.uid,\n                connections,\n            };\n        });\n        connections.push({\n            accounts: data.accounts,\n            chainId: data.chainId,\n            connector,\n        });\n        providers.push(provider);\n        connected = true;\n    }\n    // Prevent overwriting connected status from race condition\n    if (config.state.status === 'reconnecting' ||\n        config.state.status === 'connecting') {\n        // If connecting didn't succeed, set to disconnected\n        if (!connected)\n            config.setState((x) => ({\n                ...x,\n                connections: new Map(),\n                current: null,\n                status: 'disconnected',\n            }));\n        else\n            config.setState((x) => ({ ...x, status: 'connected' }));\n    }\n    isReconnecting = false;\n    return connections;\n}\n//# sourceMappingURL=reconnect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/switchChain.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/switchChain.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   switchChain: () => (/* binding */ switchChain)\n/* harmony export */ });\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n\n\n/** https://wagmi.sh/core/api/actions/switchChain */\nasync function switchChain(config, parameters) {\n    const { addEthereumChainParameter, chainId } = parameters;\n    const connection = config.state.connections.get(parameters.connector?.uid ?? config.state.current);\n    if (connection) {\n        const connector = connection.connector;\n        if (!connector.switchChain)\n            throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_0__.SwitchChainNotSupportedError({ connector });\n        const chain = await connector.switchChain({\n            addEthereumChainParameter,\n            chainId,\n        });\n        return chain;\n    }\n    const chain = config.chains.find((x) => x.id === chainId);\n    if (!chain)\n        throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_1__.ChainNotConfiguredError();\n    config.setState((x) => ({ ...x, chainId }));\n    return chain;\n}\n//# sourceMappingURL=switchChain.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9zd2l0Y2hDaGFpbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0Q7QUFDUTtBQUN2RTtBQUNPO0FBQ1AsWUFBWSxxQ0FBcUM7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsOEVBQTRCLEdBQUcsV0FBVztBQUNoRTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0Isc0VBQXVCO0FBQ3pDLDhCQUE4QixlQUFlO0FBQzdDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy9zd2l0Y2hDaGFpbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDaGFpbk5vdENvbmZpZ3VyZWRFcnJvciwgfSBmcm9tICcuLi9lcnJvcnMvY29uZmlnLmpzJztcbmltcG9ydCB7IFN3aXRjaENoYWluTm90U3VwcG9ydGVkRXJyb3IsIH0gZnJvbSAnLi4vZXJyb3JzL2Nvbm5lY3Rvci5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3N3aXRjaENoYWluICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc3dpdGNoQ2hhaW4oY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyLCBjaGFpbklkIH0gPSBwYXJhbWV0ZXJzO1xuICAgIGNvbnN0IGNvbm5lY3Rpb24gPSBjb25maWcuc3RhdGUuY29ubmVjdGlvbnMuZ2V0KHBhcmFtZXRlcnMuY29ubmVjdG9yPy51aWQgPz8gY29uZmlnLnN0YXRlLmN1cnJlbnQpO1xuICAgIGlmIChjb25uZWN0aW9uKSB7XG4gICAgICAgIGNvbnN0IGNvbm5lY3RvciA9IGNvbm5lY3Rpb24uY29ubmVjdG9yO1xuICAgICAgICBpZiAoIWNvbm5lY3Rvci5zd2l0Y2hDaGFpbilcbiAgICAgICAgICAgIHRocm93IG5ldyBTd2l0Y2hDaGFpbk5vdFN1cHBvcnRlZEVycm9yKHsgY29ubmVjdG9yIH0pO1xuICAgICAgICBjb25zdCBjaGFpbiA9IGF3YWl0IGNvbm5lY3Rvci5zd2l0Y2hDaGFpbih7XG4gICAgICAgICAgICBhZGRFdGhlcmV1bUNoYWluUGFyYW1ldGVyLFxuICAgICAgICAgICAgY2hhaW5JZCxcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBjaGFpbjtcbiAgICB9XG4gICAgY29uc3QgY2hhaW4gPSBjb25maWcuY2hhaW5zLmZpbmQoKHgpID0+IHguaWQgPT09IGNoYWluSWQpO1xuICAgIGlmICghY2hhaW4pXG4gICAgICAgIHRocm93IG5ldyBDaGFpbk5vdENvbmZpZ3VyZWRFcnJvcigpO1xuICAgIGNvbmZpZy5zZXRTdGF0ZSgoeCkgPT4gKHsgLi4ueCwgY2hhaW5JZCB9KSk7XG4gICAgcmV0dXJuIGNoYWluO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3dpdGNoQ2hhaW4uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/switchChain.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   waitForTransactionReceipt: () => (/* binding */ waitForTransactionReceipt)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/fromHex.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/waitForTransactionReceipt.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/getTransaction.js\");\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/public/call.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n\n\n\nasync function waitForTransactionReceipt(config, parameters) {\n    const { chainId, timeout = 0, ...rest } = parameters;\n    const client = config.getClient({ chainId });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_1__.waitForTransactionReceipt, 'waitForTransactionReceipt');\n    const receipt = await action({ ...rest, timeout });\n    if (receipt.status === 'reverted') {\n        const action_getTransaction = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.getTransaction, 'getTransaction');\n        const txn = await action_getTransaction({ hash: receipt.transactionHash });\n        const action_call = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_0__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_3__.call, 'call');\n        const code = await action_call({\n            ...txn,\n            data: txn.input,\n            gasPrice: txn.type !== 'eip1559' ? txn.gasPrice : undefined,\n            maxFeePerGas: txn.type === 'eip1559' ? txn.maxFeePerGas : undefined,\n            maxPriorityFeePerGas: txn.type === 'eip1559' ? txn.maxPriorityFeePerGas : undefined,\n        });\n        const reason = code?.data\n            ? (0,viem__WEBPACK_IMPORTED_MODULE_4__.hexToString)(`0x${code.data.substring(138)}`)\n            : 'unknown reason';\n        throw new Error(reason);\n    }\n    return {\n        ...receipt,\n        chainId: client.chain.id,\n    };\n}\n//# sourceMappingURL=waitForTransactionReceipt.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/waitForTransactionReceipt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchAccount: () => (/* binding */ watchAccount)\n/* harmony export */ });\n/* harmony import */ var _utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/deepEqual.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\");\n/* harmony import */ var _getAccount_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getAccount.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getAccount.js\");\n\n\n/** https://wagmi.sh/core/api/actions/watchAccount */\nfunction watchAccount(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe(() => (0,_getAccount_js__WEBPACK_IMPORTED_MODULE_0__.getAccount)(config), onChange, {\n        equalityFn(a, b) {\n            const { connector: aConnector, ...aRest } = a;\n            const { connector: bConnector, ...bRest } = b;\n            return ((0,_utils_deepEqual_js__WEBPACK_IMPORTED_MODULE_1__.deepEqual)(aRest, bRest) &&\n                // check connector separately\n                aConnector?.id === bConnector?.id &&\n                aConnector?.uid === bConnector?.uid);\n        },\n    });\n}\n//# sourceMappingURL=watchAccount.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaEFjY291bnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtEO0FBQ0w7QUFDN0M7QUFDTztBQUNQLFlBQVksV0FBVztBQUN2QixrQ0FBa0MsMERBQVU7QUFDNUM7QUFDQSxvQkFBb0Isa0NBQWtDO0FBQ3RELG9CQUFvQixrQ0FBa0M7QUFDdEQsb0JBQW9CLDhEQUFTO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaEFjY291bnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVlcEVxdWFsIH0gZnJvbSAnLi4vdXRpbHMvZGVlcEVxdWFsLmpzJztcbmltcG9ydCB7IGdldEFjY291bnQgfSBmcm9tICcuL2dldEFjY291bnQuanMnO1xuLyoqIGh0dHBzOi8vd2FnbWkuc2gvY29yZS9hcGkvYWN0aW9ucy93YXRjaEFjY291bnQgKi9cbmV4cG9ydCBmdW5jdGlvbiB3YXRjaEFjY291bnQoY29uZmlnLCBwYXJhbWV0ZXJzKSB7XG4gICAgY29uc3QgeyBvbkNoYW5nZSB9ID0gcGFyYW1ldGVycztcbiAgICByZXR1cm4gY29uZmlnLnN1YnNjcmliZSgoKSA9PiBnZXRBY2NvdW50KGNvbmZpZyksIG9uQ2hhbmdlLCB7XG4gICAgICAgIGVxdWFsaXR5Rm4oYSwgYikge1xuICAgICAgICAgICAgY29uc3QgeyBjb25uZWN0b3I6IGFDb25uZWN0b3IsIC4uLmFSZXN0IH0gPSBhO1xuICAgICAgICAgICAgY29uc3QgeyBjb25uZWN0b3I6IGJDb25uZWN0b3IsIC4uLmJSZXN0IH0gPSBiO1xuICAgICAgICAgICAgcmV0dXJuIChkZWVwRXF1YWwoYVJlc3QsIGJSZXN0KSAmJlxuICAgICAgICAgICAgICAgIC8vIGNoZWNrIGNvbm5lY3RvciBzZXBhcmF0ZWx5XG4gICAgICAgICAgICAgICAgYUNvbm5lY3Rvcj8uaWQgPT09IGJDb25uZWN0b3I/LmlkICYmXG4gICAgICAgICAgICAgICAgYUNvbm5lY3Rvcj8udWlkID09PSBiQ29ubmVjdG9yPy51aWQpO1xuICAgICAgICB9LFxuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2F0Y2hBY2NvdW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchAccount.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchChainId.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/watchChainId.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   watchChainId: () => (/* binding */ watchChainId)\n/* harmony export */ });\n/** https://wagmi.sh/core/api/actions/watchChainId */\nfunction watchChainId(config, parameters) {\n    const { onChange } = parameters;\n    return config.subscribe((state) => state.chainId, onChange);\n}\n//# sourceMappingURL=watchChainId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93YXRjaENoYWluSWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCxZQUFZLFdBQVc7QUFDdkI7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9hY3Rpb25zL3dhdGNoQ2hhaW5JZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3dhdGNoQ2hhaW5JZCAqL1xuZXhwb3J0IGZ1bmN0aW9uIHdhdGNoQ2hhaW5JZChjb25maWcsIHBhcmFtZXRlcnMpIHtcbiAgICBjb25zdCB7IG9uQ2hhbmdlIH0gPSBwYXJhbWV0ZXJzO1xuICAgIHJldHVybiBjb25maWcuc3Vic2NyaWJlKChzdGF0ZSkgPT4gc3RhdGUuY2hhaW5JZCwgb25DaGFuZ2UpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2F0Y2hDaGFpbklkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/watchChainId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/actions/writeContract.js":
/*!********************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/actions/writeContract.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   writeContract: () => (/* binding */ writeContract)\n/* harmony export */ });\n/* harmony import */ var viem_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem/actions */ \"(ssr)/./node_modules/viem/_esm/actions/wallet/writeContract.js\");\n/* harmony import */ var _utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getAction.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\");\n/* harmony import */ var _getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getConnectorClient.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getConnectorClient.js\");\n\n\n\n/** https://wagmi.sh/core/api/actions/writeContract */\nasync function writeContract(config, parameters) {\n    const { account, chainId, connector, ...request } = parameters;\n    let client;\n    if (typeof account === 'object' && account?.type === 'local')\n        client = config.getClient({ chainId });\n    else\n        client = await (0,_getConnectorClient_js__WEBPACK_IMPORTED_MODULE_0__.getConnectorClient)(config, {\n            account: account ?? undefined,\n            chainId,\n            connector,\n        });\n    const action = (0,_utils_getAction_js__WEBPACK_IMPORTED_MODULE_1__.getAction)(client, viem_actions__WEBPACK_IMPORTED_MODULE_2__.writeContract, 'writeContract');\n    const hash = await action({\n        ...request,\n        ...(account ? { account } : {}),\n        chain: chainId ? { id: chainId } : null,\n    });\n    return hash;\n}\n//# sourceMappingURL=writeContract.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vYWN0aW9ucy93cml0ZUNvbnRyYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0U7QUFDbEI7QUFDWTtBQUM5RDtBQUNPO0FBQ1AsWUFBWSwwQ0FBMEM7QUFDdEQ7QUFDQTtBQUNBLG9DQUFvQyxTQUFTO0FBQzdDO0FBQ0EsdUJBQXVCLDBFQUFrQjtBQUN6QztBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsbUJBQW1CLDhEQUFTLFNBQVMsdURBQWtCO0FBQ3ZEO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVSxJQUFJO0FBQ3RDLDJCQUEyQixjQUFjO0FBQ3pDLEtBQUs7QUFDTDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2FjdGlvbnMvd3JpdGVDb250cmFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB3cml0ZUNvbnRyYWN0IGFzIHZpZW1fd3JpdGVDb250cmFjdCwgfSBmcm9tICd2aWVtL2FjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0QWN0aW9uIH0gZnJvbSAnLi4vdXRpbHMvZ2V0QWN0aW9uLmpzJztcbmltcG9ydCB7IGdldENvbm5lY3RvckNsaWVudCwgfSBmcm9tICcuL2dldENvbm5lY3RvckNsaWVudC5qcyc7XG4vKiogaHR0cHM6Ly93YWdtaS5zaC9jb3JlL2FwaS9hY3Rpb25zL3dyaXRlQ29udHJhY3QgKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB3cml0ZUNvbnRyYWN0KGNvbmZpZywgcGFyYW1ldGVycykge1xuICAgIGNvbnN0IHsgYWNjb3VudCwgY2hhaW5JZCwgY29ubmVjdG9yLCAuLi5yZXF1ZXN0IH0gPSBwYXJhbWV0ZXJzO1xuICAgIGxldCBjbGllbnQ7XG4gICAgaWYgKHR5cGVvZiBhY2NvdW50ID09PSAnb2JqZWN0JyAmJiBhY2NvdW50Py50eXBlID09PSAnbG9jYWwnKVxuICAgICAgICBjbGllbnQgPSBjb25maWcuZ2V0Q2xpZW50KHsgY2hhaW5JZCB9KTtcbiAgICBlbHNlXG4gICAgICAgIGNsaWVudCA9IGF3YWl0IGdldENvbm5lY3RvckNsaWVudChjb25maWcsIHtcbiAgICAgICAgICAgIGFjY291bnQ6IGFjY291bnQgPz8gdW5kZWZpbmVkLFxuICAgICAgICAgICAgY2hhaW5JZCxcbiAgICAgICAgICAgIGNvbm5lY3RvcixcbiAgICAgICAgfSk7XG4gICAgY29uc3QgYWN0aW9uID0gZ2V0QWN0aW9uKGNsaWVudCwgdmllbV93cml0ZUNvbnRyYWN0LCAnd3JpdGVDb250cmFjdCcpO1xuICAgIGNvbnN0IGhhc2ggPSBhd2FpdCBhY3Rpb24oe1xuICAgICAgICAuLi5yZXF1ZXN0LFxuICAgICAgICAuLi4oYWNjb3VudCA/IHsgYWNjb3VudCB9IDoge30pLFxuICAgICAgICBjaGFpbjogY2hhaW5JZCA/IHsgaWQ6IGNoYWluSWQgfSA6IG51bGwsXG4gICAgfSk7XG4gICAgcmV0dXJuIGhhc2g7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD13cml0ZUNvbnRyYWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/actions/writeContract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConnector: () => (/* binding */ createConnector)\n/* harmony export */ });\nfunction createConnector(createConnectorFn) {\n    return createConnectorFn;\n}\n//# sourceMappingURL=createConnector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vY29ubmVjdG9ycy9jcmVhdGVDb25uZWN0b3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9jb25uZWN0b3JzL2NyZWF0ZUNvbm5lY3Rvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gY3JlYXRlQ29ubmVjdG9yKGNyZWF0ZUNvbm5lY3RvckZuKSB7XG4gICAgcmV0dXJuIGNyZWF0ZUNvbm5lY3RvckZuO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3JlYXRlQ29ubmVjdG9yLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js":
/*!******************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/connectors/injected.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   injected: () => (/* binding */ injected)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/address/getAddress.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/errors/rpc.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withTimeout.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/promise/withRetry.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/utils/encoding/toHex.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/connector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\");\n/* harmony import */ var _createConnector_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createConnector.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/createConnector.js\");\n\n\n\n\ninjected.type = 'injected';\nfunction injected(parameters = {}) {\n    const { shimDisconnect = true, unstable_shimAsyncInject } = parameters;\n    function getTarget() {\n        const target = parameters.target;\n        if (typeof target === 'function') {\n            const result = target();\n            if (result)\n                return result;\n        }\n        if (typeof target === 'object')\n            return target;\n        if (typeof target === 'string')\n            return {\n                ...(targetMap[target] ?? {\n                    id: target,\n                    name: `${target[0].toUpperCase()}${target.slice(1)}`,\n                    provider: `is${target[0].toUpperCase()}${target.slice(1)}`,\n                }),\n            };\n        return {\n            id: 'injected',\n            name: 'Injected',\n            provider(window) {\n                return window?.ethereum;\n            },\n        };\n    }\n    let accountsChanged;\n    let chainChanged;\n    let connect;\n    let disconnect;\n    return (0,_createConnector_js__WEBPACK_IMPORTED_MODULE_0__.createConnector)((config) => ({\n        get icon() {\n            return getTarget().icon;\n        },\n        get id() {\n            return getTarget().id;\n        },\n        get name() {\n            return getTarget().name;\n        },\n        /** @deprecated */\n        get supportsSimulation() {\n            return true;\n        },\n        type: injected.type,\n        async setup() {\n            const provider = await this.getProvider();\n            // Only start listening for events if `target` is set, otherwise `injected()` will also receive events\n            if (provider?.on && parameters.target) {\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n                // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n                // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n            }\n        },\n        async connect({ chainId, isReconnecting } = {}) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            let accounts = [];\n            if (isReconnecting)\n                accounts = await this.getAccounts().catch(() => []);\n            else if (shimDisconnect) {\n                // Attempt to show another prompt for selecting account if `shimDisconnect` flag is enabled\n                try {\n                    const permissions = await provider.request({\n                        method: 'wallet_requestPermissions',\n                        params: [{ eth_accounts: {} }],\n                    });\n                    accounts = permissions[0]?.caveats?.[0]?.value?.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                    // `'wallet_requestPermissions'` can return a different order of accounts than `'eth_accounts'`\n                    // switch to `'eth_accounts'` ordering if more than one account is connected\n                    // https://github.com/wevm/wagmi/issues/4140\n                    if (accounts.length > 0) {\n                        const sortedAccounts = await this.getAccounts();\n                        accounts = sortedAccounts;\n                    }\n                }\n                catch (err) {\n                    const error = err;\n                    // Not all injected providers support `wallet_requestPermissions` (e.g. MetaMask iOS).\n                    // Only bubble up error if user rejects request\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    // Or prompt is already open\n                    if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                        throw error;\n                }\n            }\n            try {\n                if (!accounts?.length && !isReconnecting) {\n                    const requestedAccounts = await provider.request({\n                        method: 'eth_requestAccounts',\n                    });\n                    accounts = requestedAccounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n                }\n                // Manage EIP-1193 event listeners\n                // https://eips.ethereum.org/EIPS/eip-1193#events\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n                // Switch to chain if provided\n                let currentChainId = await this.getChainId();\n                if (chainId && currentChainId !== chainId) {\n                    const chain = await this.switchChain({ chainId }).catch((error) => {\n                        if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                            throw error;\n                        return { id: currentChainId };\n                    });\n                    currentChainId = chain?.id ?? currentChainId;\n                }\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n                // Add connected shim if no target exists\n                if (!parameters.target)\n                    await config.storage?.setItem('injected.connected', true);\n                return { accounts, chainId: currentChainId };\n            }\n            catch (err) {\n                const error = err;\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.ResourceUnavailableRpcError(error);\n                throw error;\n            }\n        },\n        async disconnect() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            // Manage EIP-1193 event listeners\n            if (chainChanged) {\n                provider.removeListener('chainChanged', chainChanged);\n                chainChanged = undefined;\n            }\n            if (disconnect) {\n                provider.removeListener('disconnect', disconnect);\n                disconnect = undefined;\n            }\n            if (!connect) {\n                connect = this.onConnect.bind(this);\n                provider.on('connect', connect);\n            }\n            // Experimental support for MetaMask disconnect\n            // https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-2.md\n            try {\n                // Adding timeout as not all wallets support this method and can hang\n                // https://github.com/wevm/wagmi/issues/4064\n                await (0,viem__WEBPACK_IMPORTED_MODULE_4__.withTimeout)(() => \n                // TODO: Remove explicit type for viem@3\n                provider.request({\n                    // `'wallet_revokePermissions'` added in `viem@2.10.3`\n                    method: 'wallet_revokePermissions',\n                    params: [{ eth_accounts: {} }],\n                }), { timeout: 100 });\n            }\n            catch { }\n            // Add shim signalling connector is disconnected\n            if (shimDisconnect) {\n                await config.storage?.setItem(`${this.id}.disconnected`, true);\n            }\n            if (!parameters.target)\n                await config.storage?.removeItem('injected.connected');\n        },\n        async getAccounts() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const accounts = await provider.request({ method: 'eth_accounts' });\n            return accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x));\n        },\n        async getChainId() {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const hexChainId = await provider.request({ method: 'eth_chainId' });\n            return Number(hexChainId);\n        },\n        async getProvider() {\n            if (typeof window === 'undefined')\n                return undefined;\n            let provider;\n            const target = getTarget();\n            if (typeof target.provider === 'function')\n                provider = target.provider(window);\n            else if (typeof target.provider === 'string')\n                provider = findProvider(window, target.provider);\n            else\n                provider = target.provider;\n            // Some wallets do not conform to EIP-1193 (e.g. Trust Wallet)\n            // https://github.com/wevm/wagmi/issues/3526#issuecomment-**********\n            if (provider && !provider.removeListener) {\n                // Try using `off` handler if it exists, otherwise noop\n                if ('off' in provider && typeof provider.off === 'function')\n                    provider.removeListener =\n                        provider.off;\n                else\n                    provider.removeListener = () => { };\n            }\n            return provider;\n        },\n        async isAuthorized() {\n            try {\n                const isDisconnected = shimDisconnect &&\n                    // If shim exists in storage, connector is disconnected\n                    (await config.storage?.getItem(`${this.id}.disconnected`));\n                if (isDisconnected)\n                    return false;\n                // Don't allow injected connector to connect if no target is set and it hasn't already connected\n                // (e.g. flag in storage is not set). This prevents a targetless injected connector from connecting\n                // automatically whenever there is a targeted connector configured.\n                if (!parameters.target) {\n                    const connected = await config.storage?.getItem('injected.connected');\n                    if (!connected)\n                        return false;\n                }\n                const provider = await this.getProvider();\n                if (!provider) {\n                    if (unstable_shimAsyncInject !== undefined &&\n                        unstable_shimAsyncInject !== false) {\n                        // If no provider is found, check for async injection\n                        // https://github.com/wevm/references/issues/167\n                        // https://github.com/MetaMask/detect-provider\n                        const handleEthereum = async () => {\n                            if (typeof window !== 'undefined')\n                                window.removeEventListener('ethereum#initialized', handleEthereum);\n                            const provider = await this.getProvider();\n                            return !!provider;\n                        };\n                        const timeout = typeof unstable_shimAsyncInject === 'number'\n                            ? unstable_shimAsyncInject\n                            : 1_000;\n                        const res = await Promise.race([\n                            ...(typeof window !== 'undefined'\n                                ? [\n                                    new Promise((resolve) => window.addEventListener('ethereum#initialized', () => resolve(handleEthereum()), { once: true })),\n                                ]\n                                : []),\n                            new Promise((resolve) => setTimeout(() => resolve(handleEthereum()), timeout)),\n                        ]);\n                        if (res)\n                            return true;\n                    }\n                    throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n                }\n                // Use retry strategy as some injected wallets (e.g. MetaMask) fail to\n                // immediately resolve JSON-RPC requests on page load.\n                const accounts = await (0,viem__WEBPACK_IMPORTED_MODULE_5__.withRetry)(() => this.getAccounts());\n                return !!accounts.length;\n            }\n            catch {\n                return false;\n            }\n        },\n        async switchChain({ addEthereumChainParameter, chainId }) {\n            const provider = await this.getProvider();\n            if (!provider)\n                throw new _errors_connector_js__WEBPACK_IMPORTED_MODULE_1__.ProviderNotFoundError();\n            const chain = config.chains.find((x) => x.id === chainId);\n            if (!chain)\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError());\n            const promise = new Promise((resolve) => {\n                const listener = ((data) => {\n                    if ('chainId' in data && data.chainId === chainId) {\n                        config.emitter.off('change', listener);\n                        resolve();\n                    }\n                });\n                config.emitter.on('change', listener);\n            });\n            try {\n                await Promise.all([\n                    provider\n                        .request({\n                        method: 'wallet_switchEthereumChain',\n                        params: [{ chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId) }],\n                    })\n                        // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n                        // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n                        // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n                        // this callback or an externally emitted `'chainChanged'` event.\n                        // https://github.com/MetaMask/metamask-extension/issues/24247\n                        .then(async () => {\n                        const currentChainId = await this.getChainId();\n                        if (currentChainId === chainId)\n                            config.emitter.emit('change', { chainId });\n                    }),\n                    promise,\n                ]);\n                return chain;\n            }\n            catch (err) {\n                const error = err;\n                // Indicates chain is not added to provider\n                if (error.code === 4902 ||\n                    // Unwrapping for MetaMask Mobile\n                    // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n                    error\n                        ?.data?.originalError?.code === 4902) {\n                    try {\n                        const { default: blockExplorer, ...blockExplorers } = chain.blockExplorers ?? {};\n                        let blockExplorerUrls;\n                        if (addEthereumChainParameter?.blockExplorerUrls)\n                            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls;\n                        else if (blockExplorer)\n                            blockExplorerUrls = [\n                                blockExplorer.url,\n                                ...Object.values(blockExplorers).map((x) => x.url),\n                            ];\n                        let rpcUrls;\n                        if (addEthereumChainParameter?.rpcUrls?.length)\n                            rpcUrls = addEthereumChainParameter.rpcUrls;\n                        else\n                            rpcUrls = [chain.rpcUrls.default?.http[0] ?? ''];\n                        const addEthereumChain = {\n                            blockExplorerUrls,\n                            chainId: (0,viem__WEBPACK_IMPORTED_MODULE_7__.numberToHex)(chainId),\n                            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                            iconUrls: addEthereumChainParameter?.iconUrls,\n                            nativeCurrency: addEthereumChainParameter?.nativeCurrency ??\n                                chain.nativeCurrency,\n                            rpcUrls,\n                        };\n                        await Promise.all([\n                            provider\n                                .request({\n                                method: 'wallet_addEthereumChain',\n                                params: [addEthereumChain],\n                            })\n                                .then(async () => {\n                                const currentChainId = await this.getChainId();\n                                if (currentChainId === chainId)\n                                    config.emitter.emit('change', { chainId });\n                                else\n                                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(new Error('User rejected switch after adding network.'));\n                            }),\n                            promise,\n                        ]);\n                        return chain;\n                    }\n                    catch (error) {\n                        throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                    }\n                }\n                if (error.code === viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError.code)\n                    throw new viem__WEBPACK_IMPORTED_MODULE_3__.UserRejectedRequestError(error);\n                throw new viem__WEBPACK_IMPORTED_MODULE_3__.SwitchChainError(error);\n            }\n        },\n        async onAccountsChanged(accounts) {\n            // Disconnect if there are no accounts\n            if (accounts.length === 0)\n                this.onDisconnect();\n            // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n            else if (config.emitter.listenerCount('connect')) {\n                const chainId = (await this.getChainId()).toString();\n                this.onConnect({ chainId });\n                // Remove disconnected shim if it exists\n                if (shimDisconnect)\n                    await config.storage?.removeItem(`${this.id}.disconnected`);\n            }\n            // Regular change event\n            else\n                config.emitter.emit('change', {\n                    accounts: accounts.map((x) => (0,viem__WEBPACK_IMPORTED_MODULE_2__.getAddress)(x)),\n                });\n        },\n        onChainChanged(chain) {\n            const chainId = Number(chain);\n            config.emitter.emit('change', { chainId });\n        },\n        async onConnect(connectInfo) {\n            const accounts = await this.getAccounts();\n            if (accounts.length === 0)\n                return;\n            const chainId = Number(connectInfo.chainId);\n            config.emitter.emit('connect', { accounts, chainId });\n            // Manage EIP-1193 event listeners\n            const provider = await this.getProvider();\n            if (provider) {\n                if (connect) {\n                    provider.removeListener('connect', connect);\n                    connect = undefined;\n                }\n                if (!accountsChanged) {\n                    accountsChanged = this.onAccountsChanged.bind(this);\n                    provider.on('accountsChanged', accountsChanged);\n                }\n                if (!chainChanged) {\n                    chainChanged = this.onChainChanged.bind(this);\n                    provider.on('chainChanged', chainChanged);\n                }\n                if (!disconnect) {\n                    disconnect = this.onDisconnect.bind(this);\n                    provider.on('disconnect', disconnect);\n                }\n            }\n        },\n        async onDisconnect(error) {\n            const provider = await this.getProvider();\n            // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n            // https://github.com/MetaMask/providers/pull/120\n            if (error && error.code === 1013) {\n                if (provider && !!(await this.getAccounts()).length)\n                    return;\n            }\n            // No need to remove `${this.id}.disconnected` from storage because `onDisconnect` is typically\n            // only called when the wallet is disconnected through the wallet's interface, meaning the wallet\n            // actually disconnected and we don't need to simulate it.\n            config.emitter.emit('disconnect');\n            // Manage EIP-1193 event listeners\n            if (provider) {\n                if (chainChanged) {\n                    provider.removeListener('chainChanged', chainChanged);\n                    chainChanged = undefined;\n                }\n                if (disconnect) {\n                    provider.removeListener('disconnect', disconnect);\n                    disconnect = undefined;\n                }\n                if (!connect) {\n                    connect = this.onConnect.bind(this);\n                    provider.on('connect', connect);\n                }\n            }\n        },\n    }));\n}\nconst targetMap = {\n    coinbaseWallet: {\n        id: 'coinbaseWallet',\n        name: 'Coinbase Wallet',\n        provider(window) {\n            if (window?.coinbaseWalletExtension)\n                return window.coinbaseWalletExtension;\n            return findProvider(window, 'isCoinbaseWallet');\n        },\n    },\n    metaMask: {\n        id: 'metaMask',\n        name: 'MetaMask',\n        provider(window) {\n            return findProvider(window, (provider) => {\n                if (!provider.isMetaMask)\n                    return false;\n                // Brave tries to make itself look like MetaMask\n                // Could also try RPC `web3_clientVersion` if following is unreliable\n                if (provider.isBraveWallet && !provider._events && !provider._state)\n                    return false;\n                // Other wallets that try to look like MetaMask\n                const flags = [\n                    'isApexWallet',\n                    'isAvalanche',\n                    'isBitKeep',\n                    'isBlockWallet',\n                    'isKuCoinWallet',\n                    'isMathWallet',\n                    'isOkxWallet',\n                    'isOKExWallet',\n                    'isOneInchIOSWallet',\n                    'isOneInchAndroidWallet',\n                    'isOpera',\n                    'isPhantom',\n                    'isPortal',\n                    'isRabby',\n                    'isTokenPocket',\n                    'isTokenary',\n                    'isUniswapWallet',\n                    'isZerion',\n                ];\n                for (const flag of flags)\n                    if (provider[flag])\n                        return false;\n                return true;\n            });\n        },\n    },\n    phantom: {\n        id: 'phantom',\n        name: 'Phantom',\n        provider(window) {\n            if (window?.phantom?.ethereum)\n                return window.phantom?.ethereum;\n            return findProvider(window, 'isPhantom');\n        },\n    },\n};\nfunction findProvider(window, select) {\n    function isProvider(provider) {\n        if (typeof select === 'function')\n            return select(provider);\n        if (typeof select === 'string')\n            return provider[select];\n        return true;\n    }\n    const ethereum = window.ethereum;\n    if (ethereum?.providers)\n        return ethereum.providers.find((provider) => isProvider(provider));\n    if (ethereum && isProvider(ethereum))\n        return ethereum;\n    return undefined;\n}\n//# sourceMappingURL=injected.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js":
/*!***********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createConfig.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfig: () => (/* binding */ createConfig)\n/* harmony export */ });\n/* harmony import */ var mipd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mipd */ \"(ssr)/./node_modules/mipd/dist/esm/store.js\");\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/clients/createClient.js\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var _connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./connectors/injected.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/connectors/injected.js\");\n/* harmony import */ var _createEmitter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./createEmitter.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\");\n/* harmony import */ var _createStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createStorage.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\");\n/* harmony import */ var _errors_config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors/config.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\");\n/* harmony import */ var _utils_uid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/uid.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\n\n\n\n\n\n\n\n\n\nfunction createConfig(parameters) {\n    const { multiInjectedProviderDiscovery = true, storage = (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.createStorage)({\n        storage: (0,_createStorage_js__WEBPACK_IMPORTED_MODULE_0__.getDefaultStorage)(),\n    }), syncConnectedChain = true, ssr = false, ...rest } = parameters;\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Set up connectors, clients, etc.\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    const mipd = typeof window !== 'undefined' && multiInjectedProviderDiscovery\n        ? (0,mipd__WEBPACK_IMPORTED_MODULE_1__.createStore)()\n        : undefined;\n    const chains = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => rest.chains);\n    const connectors = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)(() => {\n        const collection = [];\n        const rdnsSet = new Set();\n        for (const connectorFns of rest.connectors ?? []) {\n            const connector = setup(connectorFns);\n            collection.push(connector);\n            if (!ssr && connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    rdnsSet.add(rdns);\n                }\n            }\n        }\n        if (!ssr && mipd) {\n            const providers = mipd.getProviders();\n            for (const provider of providers) {\n                if (rdnsSet.has(provider.info.rdns))\n                    continue;\n                collection.push(setup(providerDetailToConnector(provider)));\n            }\n        }\n        return collection;\n    });\n    function setup(connectorFn) {\n        // Set up emitter with uid and add to connector so they are \"linked\" together.\n        const emitter = (0,_createEmitter_js__WEBPACK_IMPORTED_MODULE_3__.createEmitter)((0,_utils_uid_js__WEBPACK_IMPORTED_MODULE_4__.uid)());\n        const connector = {\n            ...connectorFn({\n                emitter,\n                chains: chains.getState(),\n                storage,\n                transports: rest.transports,\n            }),\n            emitter,\n            uid: emitter.uid,\n        };\n        // Start listening for `connect` events on connector setup\n        // This allows connectors to \"connect\" themselves without user interaction (e.g. MetaMask's \"Manually connect to current site\")\n        emitter.on('connect', connect);\n        connector.setup?.();\n        return connector;\n    }\n    function providerDetailToConnector(providerDetail) {\n        const { info } = providerDetail;\n        const provider = providerDetail.provider;\n        return (0,_connectors_injected_js__WEBPACK_IMPORTED_MODULE_5__.injected)({ target: { ...info, id: info.rdns, provider } });\n    }\n    const clients = new Map();\n    function getClient(config = {}) {\n        const chainId = config.chainId ?? store.getState().chainId;\n        const chain = chains.getState().find((x) => x.id === chainId);\n        // chainId specified and not configured\n        if (config.chainId && !chain)\n            throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        {\n            const client = clients.get(store.getState().chainId);\n            if (client && !chain)\n                return client;\n            if (!chain)\n                throw new _errors_config_js__WEBPACK_IMPORTED_MODULE_6__.ChainNotConfiguredError();\n        }\n        // If a memoized client exists for a chain id, use that.\n        {\n            const client = clients.get(chainId);\n            if (client)\n                return client;\n        }\n        let client;\n        if (rest.client)\n            client = rest.client({ chain });\n        else {\n            const chainId = chain.id;\n            const chainIds = chains.getState().map((x) => x.id);\n            // Grab all properties off `rest` and resolve for use in `createClient`\n            const properties = {};\n            const entries = Object.entries(rest);\n            for (const [key, value] of entries) {\n                if (key === 'chains' ||\n                    key === 'client' ||\n                    key === 'connectors' ||\n                    key === 'transports')\n                    continue;\n                if (typeof value === 'object') {\n                    // check if value is chainId-specific since some values can be objects\n                    // e.g. { batch: { multicall: { batchSize: 1024 } } }\n                    if (chainId in value)\n                        properties[key] = value[chainId];\n                    else {\n                        // check if value is chainId-specific, but does not have value for current chainId\n                        const hasChainSpecificValue = chainIds.some((x) => x in value);\n                        if (hasChainSpecificValue)\n                            continue;\n                        properties[key] = value;\n                    }\n                }\n                else\n                    properties[key] = value;\n            }\n            client = (0,viem__WEBPACK_IMPORTED_MODULE_7__.createClient)({\n                ...properties,\n                chain,\n                batch: properties.batch ?? { multicall: true },\n                transport: (parameters) => rest.transports[chainId]({ ...parameters, connectors }),\n            });\n        }\n        clients.set(chainId, client);\n        return client;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Create store\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function getInitialState() {\n        return {\n            chainId: chains.getState()[0].id,\n            connections: new Map(),\n            current: null,\n            status: 'disconnected',\n        };\n    }\n    let currentVersion;\n    const prefix = '0.0.0-canary-';\n    if (_version_js__WEBPACK_IMPORTED_MODULE_8__.version.startsWith(prefix))\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.replace(prefix, ''));\n    // use package major version to version store\n    else\n        currentVersion = Number.parseInt(_version_js__WEBPACK_IMPORTED_MODULE_8__.version.split('.')[0] ?? '0');\n    const store = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_2__.createStore)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.subscribeWithSelector)(\n    // only use persist middleware if storage exists\n    storage\n        ? (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_9__.persist)(getInitialState, {\n            migrate(persistedState, version) {\n                if (version === currentVersion)\n                    return persistedState;\n                const initialState = getInitialState();\n                const chainId = validatePersistedChainId(persistedState, initialState.chainId);\n                return { ...initialState, chainId };\n            },\n            name: 'store',\n            partialize(state) {\n                // Only persist \"critical\" store properties to preserve storage size.\n                return {\n                    connections: {\n                        __type: 'Map',\n                        value: Array.from(state.connections.entries()).map(([key, connection]) => {\n                            const { id, name, type, uid } = connection.connector;\n                            const connector = { id, name, type, uid };\n                            return [key, { ...connection, connector }];\n                        }),\n                    },\n                    chainId: state.chainId,\n                    current: state.current,\n                };\n            },\n            merge(persistedState, currentState) {\n                // `status` should not be persisted as it messes with reconnection\n                if (typeof persistedState === 'object' &&\n                    persistedState &&\n                    'status' in persistedState)\n                    delete persistedState.status;\n                // Make sure persisted `chainId` is valid\n                const chainId = validatePersistedChainId(persistedState, currentState.chainId);\n                return {\n                    ...currentState,\n                    ...persistedState,\n                    chainId,\n                };\n            },\n            skipHydration: ssr,\n            storage: storage,\n            version: currentVersion,\n        })\n        : getInitialState));\n    store.setState(getInitialState());\n    function validatePersistedChainId(persistedState, defaultChainId) {\n        return persistedState &&\n            typeof persistedState === 'object' &&\n            'chainId' in persistedState &&\n            typeof persistedState.chainId === 'number' &&\n            chains.getState().some((x) => x.id === persistedState.chainId)\n            ? persistedState.chainId\n            : defaultChainId;\n    }\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Subscribe to changes\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Update default chain when connector chain changes\n    if (syncConnectedChain)\n        store.subscribe(({ connections, current }) => current ? connections.get(current)?.chainId : undefined, (chainId) => {\n            // If chain is not configured, then don't switch over to it.\n            const isChainConfigured = chains\n                .getState()\n                .some((x) => x.id === chainId);\n            if (!isChainConfigured)\n                return;\n            return store.setState((x) => ({\n                ...x,\n                chainId: chainId ?? x.chainId,\n            }));\n        });\n    // EIP-6963 subscribe for new wallet providers\n    mipd?.subscribe((providerDetails) => {\n        const connectorIdSet = new Set();\n        const connectorRdnsSet = new Set();\n        for (const connector of connectors.getState()) {\n            connectorIdSet.add(connector.id);\n            if (connector.rdns) {\n                const rdnsValues = typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns;\n                for (const rdns of rdnsValues) {\n                    connectorRdnsSet.add(rdns);\n                }\n            }\n        }\n        const newConnectors = [];\n        for (const providerDetail of providerDetails) {\n            if (connectorRdnsSet.has(providerDetail.info.rdns))\n                continue;\n            const connector = setup(providerDetailToConnector(providerDetail));\n            if (connectorIdSet.has(connector.id))\n                continue;\n            newConnectors.push(connector);\n        }\n        if (storage && !store.persist.hasHydrated())\n            return;\n        connectors.setState((x) => [...x, ...newConnectors], true);\n    });\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    // Emitter listeners\n    /////////////////////////////////////////////////////////////////////////////////////////////////\n    function change(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (!connection)\n                return x;\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts ??\n                        connection.accounts,\n                    chainId: data.chainId ?? connection.chainId,\n                    connector: connection.connector,\n                }),\n            };\n        });\n    }\n    function connect(data) {\n        // Disable handling if reconnecting/connecting\n        if (store.getState().status === 'connecting' ||\n            store.getState().status === 'reconnecting')\n            return;\n        store.setState((x) => {\n            const connector = connectors.getState().find((x) => x.uid === data.uid);\n            if (!connector)\n                return x;\n            if (connector.emitter.listenerCount('connect'))\n                connector.emitter.off('connect', change);\n            if (!connector.emitter.listenerCount('change'))\n                connector.emitter.on('change', change);\n            if (!connector.emitter.listenerCount('disconnect'))\n                connector.emitter.on('disconnect', disconnect);\n            return {\n                ...x,\n                connections: new Map(x.connections).set(data.uid, {\n                    accounts: data.accounts,\n                    chainId: data.chainId,\n                    connector: connector,\n                }),\n                current: data.uid,\n                status: 'connected',\n            };\n        });\n    }\n    function disconnect(data) {\n        store.setState((x) => {\n            const connection = x.connections.get(data.uid);\n            if (connection) {\n                const connector = connection.connector;\n                if (connector.emitter.listenerCount('change'))\n                    connection.connector.emitter.off('change', change);\n                if (connector.emitter.listenerCount('disconnect'))\n                    connection.connector.emitter.off('disconnect', disconnect);\n                if (!connector.emitter.listenerCount('connect'))\n                    connection.connector.emitter.on('connect', connect);\n            }\n            x.connections.delete(data.uid);\n            if (x.connections.size === 0)\n                return {\n                    ...x,\n                    connections: new Map(),\n                    current: null,\n                    status: 'disconnected',\n                };\n            const nextConnection = x.connections.values().next().value;\n            return {\n                ...x,\n                connections: new Map(x.connections),\n                current: nextConnection.connector.uid,\n            };\n        });\n    }\n    return {\n        get chains() {\n            return chains.getState();\n        },\n        get connectors() {\n            return connectors.getState();\n        },\n        storage,\n        getClient,\n        get state() {\n            return store.getState();\n        },\n        setState(value) {\n            let newState;\n            if (typeof value === 'function')\n                newState = value(store.getState());\n            else\n                newState = value;\n            // Reset state if it got set to something not matching the base state\n            const initialState = getInitialState();\n            if (typeof newState !== 'object')\n                newState = initialState;\n            const isCorrupt = Object.keys(initialState).some((x) => !(x in newState));\n            if (isCorrupt)\n                newState = initialState;\n            store.setState(newState, true);\n        },\n        subscribe(selector, listener, options) {\n            return store.subscribe(selector, listener, options\n                ? {\n                    ...options,\n                    fireImmediately: options.emitImmediately,\n                    // Workaround cast since Zustand does not support `'exactOptionalPropertyTypes'`\n                }\n                : undefined);\n        },\n        _internal: {\n            mipd,\n            store,\n            ssr: Boolean(ssr),\n            syncConnectedChain,\n            transports: rest.transports,\n            chains: {\n                setState(value) {\n                    const nextChains = (typeof value === 'function' ? value(chains.getState()) : value);\n                    if (nextChains.length === 0)\n                        return;\n                    return chains.setState(nextChains, true);\n                },\n                subscribe(listener) {\n                    return chains.subscribe(listener);\n                },\n            },\n            connectors: {\n                providerDetailToConnector,\n                setup: setup,\n                setState(value) {\n                    return connectors.setState(typeof value === 'function' ? value(connectors.getState()) : value, true);\n                },\n                subscribe(listener) {\n                    return connectors.subscribe(listener);\n                },\n            },\n            events: { change, connect, disconnect },\n        },\n    };\n}\n//# sourceMappingURL=createConfig.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createConfig.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createEmitter.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitter: () => (/* binding */ Emitter),\n/* harmony export */   createEmitter: () => (/* binding */ createEmitter)\n/* harmony export */ });\n/* harmony import */ var eventemitter3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! eventemitter3 */ \"(ssr)/./node_modules/@wagmi/core/node_modules/eventemitter3/index.mjs\");\n\nclass Emitter {\n    constructor(uid) {\n        Object.defineProperty(this, \"uid\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: uid\n        });\n        Object.defineProperty(this, \"_emitter\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new eventemitter3__WEBPACK_IMPORTED_MODULE_0__.EventEmitter()\n        });\n    }\n    on(eventName, fn) {\n        this._emitter.on(eventName, fn);\n    }\n    once(eventName, fn) {\n        this._emitter.once(eventName, fn);\n    }\n    off(eventName, fn) {\n        this._emitter.off(eventName, fn);\n    }\n    emit(eventName, ...params) {\n        const data = params[0];\n        this._emitter.emit(eventName, { uid: this.uid, ...data });\n    }\n    listenerCount(eventName) {\n        return this._emitter.listenerCount(eventName);\n    }\n}\nfunction createEmitter(uid) {\n    return new Emitter(uid);\n}\n//# sourceMappingURL=createEmitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createEmitter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/createStorage.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStorage: () => (/* binding */ createStorage),\n/* harmony export */   getDefaultStorage: () => (/* binding */ getDefaultStorage),\n/* harmony export */   noopStorage: () => (/* binding */ noopStorage)\n/* harmony export */ });\n/* harmony import */ var _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/deserialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\");\n/* harmony import */ var _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/serialize.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\");\n\n\nfunction createStorage(parameters) {\n    const { deserialize = _utils_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize, key: prefix = 'wagmi', serialize = _utils_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize, storage = noopStorage, } = parameters;\n    function unwrap(value) {\n        if (value instanceof Promise)\n            return value.then((x) => x).catch(() => null);\n        return value;\n    }\n    return {\n        ...storage,\n        key: prefix,\n        async getItem(key, defaultValue) {\n            const value = storage.getItem(`${prefix}.${key}`);\n            const unwrapped = await unwrap(value);\n            if (unwrapped)\n                return deserialize(unwrapped) ?? null;\n            return (defaultValue ?? null);\n        },\n        async setItem(key, value) {\n            const storageKey = `${prefix}.${key}`;\n            if (value === null)\n                await unwrap(storage.removeItem(storageKey));\n            else\n                await unwrap(storage.setItem(storageKey, serialize(value)));\n        },\n        async removeItem(key) {\n            await unwrap(storage.removeItem(`${prefix}.${key}`));\n        },\n    };\n}\nconst noopStorage = {\n    getItem: () => null,\n    setItem: () => { },\n    removeItem: () => { },\n};\nfunction getDefaultStorage() {\n    const storage = (() => {\n        if (typeof window !== 'undefined' && window.localStorage)\n            return window.localStorage;\n        return noopStorage;\n    })();\n    return {\n        getItem(key) {\n            return storage.getItem(key);\n        },\n        removeItem(key) {\n            storage.removeItem(key);\n        },\n        setItem(key, value) {\n            try {\n                storage.setItem(key, value);\n                // silence errors by default (QuotaExceededError, SecurityError, etc.)\n            }\n            catch { }\n        },\n    };\n}\n//# sourceMappingURL=createStorage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/createStorage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js":
/*!**********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/base.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getVersion.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\");\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _BaseError_instances, _BaseError_walk;\n\nclass BaseError extends Error {\n    get docsBaseUrl() {\n        return 'https://wagmi.sh/core';\n    }\n    get version() {\n        return (0,_utils_getVersion_js__WEBPACK_IMPORTED_MODULE_0__.getVersion)();\n    }\n    constructor(shortMessage, options = {}) {\n        super();\n        _BaseError_instances.add(this);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"metaMessages\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'WagmiCoreError'\n        });\n        const details = options.cause instanceof BaseError\n            ? options.cause.details\n            : options.cause?.message\n                ? options.cause.message\n                : options.details;\n        const docsPath = options.cause instanceof BaseError\n            ? options.cause.docsPath || options.docsPath\n            : options.docsPath;\n        this.message = [\n            shortMessage || 'An error occurred.',\n            '',\n            ...(options.metaMessages ? [...options.metaMessages, ''] : []),\n            ...(docsPath\n                ? [\n                    `Docs: ${this.docsBaseUrl}${docsPath}.html${options.docsSlug ? `#${options.docsSlug}` : ''}`,\n                ]\n                : []),\n            ...(details ? [`Details: ${details}`] : []),\n            `Version: ${this.version}`,\n        ].join('\\n');\n        if (options.cause)\n            this.cause = options.cause;\n        this.details = details;\n        this.docsPath = docsPath;\n        this.metaMessages = options.metaMessages;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, this, fn);\n    }\n}\n_BaseError_instances = new WeakSet(), _BaseError_walk = function _BaseError_walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err.cause)\n        return __classPrivateFieldGet(this, _BaseError_instances, \"m\", _BaseError_walk).call(this, err.cause, fn);\n    return err;\n};\n//# sourceMappingURL=base.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/config.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChainNotConfiguredError: () => (/* binding */ ChainNotConfiguredError),\n/* harmony export */   ConnectorAccountNotFoundError: () => (/* binding */ ConnectorAccountNotFoundError),\n/* harmony export */   ConnectorAlreadyConnectedError: () => (/* binding */ ConnectorAlreadyConnectedError),\n/* harmony export */   ConnectorChainMismatchError: () => (/* binding */ ConnectorChainMismatchError),\n/* harmony export */   ConnectorNotConnectedError: () => (/* binding */ ConnectorNotConnectedError),\n/* harmony export */   ConnectorNotFoundError: () => (/* binding */ ConnectorNotFoundError),\n/* harmony export */   ConnectorUnavailableReconnectingError: () => (/* binding */ ConnectorUnavailableReconnectingError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ChainNotConfiguredError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Chain not configured.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ChainNotConfiguredError'\n        });\n    }\n}\nclass ConnectorAlreadyConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector already connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAlreadyConnectedError'\n        });\n    }\n}\nclass ConnectorNotConnectedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not connected.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotConnectedError'\n        });\n    }\n}\nclass ConnectorNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Connector not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorNotFoundError'\n        });\n    }\n}\nclass ConnectorAccountNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ address, connector, }) {\n        super(`Account \"${address}\" not found for connector \"${connector.name}\".`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorAccountNotFoundError'\n        });\n    }\n}\nclass ConnectorChainMismatchError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connectionChainId, connectorChainId, }) {\n        super(`The current chain of the connector (id: ${connectorChainId}) does not match the connection's chain (id: ${connectionChainId}).`, {\n            metaMessages: [\n                `Current Chain ID:  ${connectorChainId}`,\n                `Expected Chain ID: ${connectionChainId}`,\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorChainMismatchError'\n        });\n    }\n}\nclass ConnectorUnavailableReconnectingError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`Connector \"${connector.name}\" unavailable while reconnecting.`, {\n            details: [\n                'During the reconnection step, the only connector methods guaranteed to be available are: `id`, `name`, `type`, `uid`.',\n                'All other methods are not guaranteed to be available until reconnection completes and connectors are fully restored.',\n                'This error commonly occurs for connectors that asynchronously inject after reconnection has already started.',\n            ].join(' '),\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ConnectorUnavailableReconnectingError'\n        });\n    }\n}\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/errors/connector.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProviderNotFoundError: () => (/* binding */ ProviderNotFoundError),\n/* harmony export */   SwitchChainNotSupportedError: () => (/* binding */ SwitchChainNotSupportedError)\n/* harmony export */ });\n/* harmony import */ var _base_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/errors/base.js\");\n\nclass ProviderNotFoundError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor() {\n        super('Provider not found.');\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'ProviderNotFoundError'\n        });\n    }\n}\nclass SwitchChainNotSupportedError extends _base_js__WEBPACK_IMPORTED_MODULE_0__.BaseError {\n    constructor({ connector }) {\n        super(`\"${connector.name}\" does not support programmatic chain switching.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'SwitchChainNotSupportedError'\n        });\n    }\n}\n//# sourceMappingURL=connector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vZXJyb3JzL2Nvbm5lY3Rvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0M7QUFDL0Isb0NBQW9DLCtDQUFTO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDTywyQ0FBMkMsK0NBQVM7QUFDM0Qsa0JBQWtCLFdBQVc7QUFDN0Isa0JBQWtCLGVBQWU7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL2Vycm9ycy9jb25uZWN0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZUVycm9yIH0gZnJvbSAnLi9iYXNlLmpzJztcbmV4cG9ydCBjbGFzcyBQcm92aWRlck5vdEZvdW5kRXJyb3IgZXh0ZW5kcyBCYXNlRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlcignUHJvdmlkZXIgbm90IGZvdW5kLicpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJuYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiAnUHJvdmlkZXJOb3RGb3VuZEVycm9yJ1xuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgU3dpdGNoQ2hhaW5Ob3RTdXBwb3J0ZWRFcnJvciBleHRlbmRzIEJhc2VFcnJvciB7XG4gICAgY29uc3RydWN0b3IoeyBjb25uZWN0b3IgfSkge1xuICAgICAgICBzdXBlcihgXCIke2Nvbm5lY3Rvci5uYW1lfVwiIGRvZXMgbm90IHN1cHBvcnQgcHJvZ3JhbW1hdGljIGNoYWluIHN3aXRjaGluZy5gKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogJ1N3aXRjaENoYWluTm90U3VwcG9ydGVkRXJyb3InXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbm5lY3Rvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/errors/connector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/hydrate.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hydrate: () => (/* binding */ hydrate)\n/* harmony export */ });\n/* harmony import */ var _actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions/reconnect.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/reconnect.js\");\n\nfunction hydrate(config, parameters) {\n    const { initialState, reconnectOnMount } = parameters;\n    if (initialState && !config._internal.store.persist.hasHydrated())\n        config.setState({\n            ...initialState,\n            chainId: config.chains.some((x) => x.id === initialState.chainId)\n                ? initialState.chainId\n                : config.chains[0].id,\n            connections: reconnectOnMount ? initialState.connections : new Map(),\n            status: reconnectOnMount ? 'reconnecting' : 'disconnected',\n        });\n    return {\n        async onMount() {\n            if (config._internal.ssr) {\n                await config._internal.store.persist.rehydrate();\n                if (config._internal.mipd) {\n                    config._internal.connectors.setState((connectors) => {\n                        const rdnsSet = new Set();\n                        for (const connector of connectors ?? []) {\n                            if (connector.rdns) {\n                                const rdnsValues = Array.isArray(connector.rdns)\n                                    ? connector.rdns\n                                    : [connector.rdns];\n                                for (const rdns of rdnsValues) {\n                                    rdnsSet.add(rdns);\n                                }\n                            }\n                        }\n                        const mipdConnectors = [];\n                        const providers = config._internal.mipd?.getProviders() ?? [];\n                        for (const provider of providers) {\n                            if (rdnsSet.has(provider.info.rdns))\n                                continue;\n                            const connectorFn = config._internal.connectors.providerDetailToConnector(provider);\n                            const connector = config._internal.connectors.setup(connectorFn);\n                            mipdConnectors.push(connector);\n                        }\n                        return [...connectors, ...mipdConnectors];\n                    });\n                }\n            }\n            if (reconnectOnMount)\n                (0,_actions_reconnect_js__WEBPACK_IMPORTED_MODULE_0__.reconnect)(config);\n            else if (config.storage)\n                // Reset connections that may have been hydrated from storage.\n                config.setState((x) => ({\n                    ...x,\n                    connections: new Map(),\n                }));\n        },\n    };\n}\n//# sourceMappingURL=hydrate.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/hydrate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/getBalance.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/getBalance.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBalanceQueryKey: () => (/* binding */ getBalanceQueryKey),\n/* harmony export */   getBalanceQueryOptions: () => (/* binding */ getBalanceQueryOptions)\n/* harmony export */ });\n/* harmony import */ var _actions_getBalance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actions/getBalance.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/actions/getBalance.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/query/utils.js\");\n\n\nfunction getBalanceQueryOptions(config, options = {}) {\n    return {\n        async queryFn({ queryKey }) {\n            const { address, scopeKey: _, ...parameters } = queryKey[1];\n            if (!address)\n                throw new Error('address is required');\n            const balance = await (0,_actions_getBalance_js__WEBPACK_IMPORTED_MODULE_0__.getBalance)(config, {\n                ...parameters,\n                address,\n            });\n            return balance ?? null;\n        },\n        queryKey: getBalanceQueryKey(options),\n    };\n}\nfunction getBalanceQueryKey(options = {}) {\n    return ['balance', (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.filterQueryOptions)(options)];\n}\n//# sourceMappingURL=getBalance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvZ2V0QmFsYW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVEO0FBQ1A7QUFDekMsb0RBQW9EO0FBQzNEO0FBQ0Esd0JBQXdCLFVBQVU7QUFDbEMsb0JBQW9CLHNDQUFzQztBQUMxRDtBQUNBO0FBQ0Esa0NBQWtDLGtFQUFVO0FBQzVDO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ08sd0NBQXdDO0FBQy9DLHVCQUF1Qiw2REFBa0I7QUFDekM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvZ2V0QmFsYW5jZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRCYWxhbmNlLCB9IGZyb20gJy4uL2FjdGlvbnMvZ2V0QmFsYW5jZS5qcyc7XG5pbXBvcnQgeyBmaWx0ZXJRdWVyeU9wdGlvbnMgfSBmcm9tICcuL3V0aWxzLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBnZXRCYWxhbmNlUXVlcnlPcHRpb25zKGNvbmZpZywgb3B0aW9ucyA9IHt9KSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgYXN5bmMgcXVlcnlGbih7IHF1ZXJ5S2V5IH0pIHtcbiAgICAgICAgICAgIGNvbnN0IHsgYWRkcmVzcywgc2NvcGVLZXk6IF8sIC4uLnBhcmFtZXRlcnMgfSA9IHF1ZXJ5S2V5WzFdO1xuICAgICAgICAgICAgaWYgKCFhZGRyZXNzKVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignYWRkcmVzcyBpcyByZXF1aXJlZCcpO1xuICAgICAgICAgICAgY29uc3QgYmFsYW5jZSA9IGF3YWl0IGdldEJhbGFuY2UoY29uZmlnLCB7XG4gICAgICAgICAgICAgICAgLi4ucGFyYW1ldGVycyxcbiAgICAgICAgICAgICAgICBhZGRyZXNzLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gYmFsYW5jZSA/PyBudWxsO1xuICAgICAgICB9LFxuICAgICAgICBxdWVyeUtleTogZ2V0QmFsYW5jZVF1ZXJ5S2V5KG9wdGlvbnMpLFxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gZ2V0QmFsYW5jZVF1ZXJ5S2V5KG9wdGlvbnMgPSB7fSkge1xuICAgIHJldHVybiBbJ2JhbGFuY2UnLCBmaWx0ZXJRdWVyeU9wdGlvbnMob3B0aW9ucyldO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0QmFsYW5jZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/getBalance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/query/utils.js":
/*!**********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/query/utils.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filterQueryOptions: () => (/* binding */ filterQueryOptions),\n/* harmony export */   hashFn: () => (/* binding */ hashFn),\n/* harmony export */   structuralSharing: () => (/* binding */ structuralSharing)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n\nfunction structuralSharing(oldData, newData) {\n    return (0,_tanstack_query_core__WEBPACK_IMPORTED_MODULE_0__.replaceEqualDeep)(oldData, newData);\n}\nfunction hashFn(queryKey) {\n    return JSON.stringify(queryKey, (_, value) => {\n        if (isPlainObject(value))\n            return Object.keys(value)\n                .sort()\n                .reduce((result, key) => {\n                result[key] = value[key];\n                return result;\n            }, {});\n        if (typeof value === 'bigint')\n            return value.toString();\n        return value;\n    });\n}\n// biome-ignore lint/complexity/noBannedTypes:\nfunction isPlainObject(value) {\n    if (!hasObjectPrototype(value)) {\n        return false;\n    }\n    // If has modified constructor\n    const ctor = value.constructor;\n    if (typeof ctor === 'undefined')\n        return true;\n    // If has modified prototype\n    const prot = ctor.prototype;\n    if (!hasObjectPrototype(prot))\n        return false;\n    // If constructor does not have an Object-specific method\n    // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>\n    if (!prot.hasOwnProperty('isPrototypeOf'))\n        return false;\n    // Most likely a plain Object\n    return true;\n}\nfunction hasObjectPrototype(o) {\n    return Object.prototype.toString.call(o) === '[object Object]';\n}\nfunction filterQueryOptions(options) {\n    // destructuring is super fast\n    // biome-ignore format: no formatting\n    const { \n    // import('@tanstack/query-core').QueryOptions\n    _defaulted, behavior, gcTime, initialData, initialDataUpdatedAt, maxPages, meta, networkMode, queryFn, queryHash, queryKey, queryKeyHashFn, retry, retryDelay, structuralSharing, \n    // import('@tanstack/query-core').InfiniteQueryObserverOptions\n    getPreviousPageParam, getNextPageParam, initialPageParam, \n    // import('@tanstack/react-query').UseQueryOptions\n    _optimisticResults, enabled, notifyOnChangeProps, placeholderData, refetchInterval, refetchIntervalInBackground, refetchOnMount, refetchOnReconnect, refetchOnWindowFocus, retryOnMount, select, staleTime, suspense, throwOnError, \n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    // wagmi\n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    config, connector, query, ...rest } = options;\n    return rest;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vcXVlcnkvdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF3RDtBQUNqRDtBQUNQLFdBQVcsc0VBQWdCO0FBQzNCO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsSUFBSTtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9Ad2FnbWkvY29yZS9kaXN0L2VzbS9xdWVyeS91dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZXBsYWNlRXF1YWxEZWVwIH0gZnJvbSAnQHRhbnN0YWNrL3F1ZXJ5LWNvcmUnO1xuZXhwb3J0IGZ1bmN0aW9uIHN0cnVjdHVyYWxTaGFyaW5nKG9sZERhdGEsIG5ld0RhdGEpIHtcbiAgICByZXR1cm4gcmVwbGFjZUVxdWFsRGVlcChvbGREYXRhLCBuZXdEYXRhKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBoYXNoRm4ocXVlcnlLZXkpIHtcbiAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkocXVlcnlLZXksIChfLCB2YWx1ZSkgPT4ge1xuICAgICAgICBpZiAoaXNQbGFpbk9iamVjdCh2YWx1ZSkpXG4gICAgICAgICAgICByZXR1cm4gT2JqZWN0LmtleXModmFsdWUpXG4gICAgICAgICAgICAgICAgLnNvcnQoKVxuICAgICAgICAgICAgICAgIC5yZWR1Y2UoKHJlc3VsdCwga2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgcmVzdWx0W2tleV0gPSB2YWx1ZVtrZXldO1xuICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICAgICAgICB9LCB7fSk7XG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdiaWdpbnQnKVxuICAgICAgICAgICAgcmV0dXJuIHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9KTtcbn1cbi8vIGJpb21lLWlnbm9yZSBsaW50L2NvbXBsZXhpdHkvbm9CYW5uZWRUeXBlczpcbmZ1bmN0aW9uIGlzUGxhaW5PYmplY3QodmFsdWUpIHtcbiAgICBpZiAoIWhhc09iamVjdFByb3RvdHlwZSh2YWx1ZSkpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICAvLyBJZiBoYXMgbW9kaWZpZWQgY29uc3RydWN0b3JcbiAgICBjb25zdCBjdG9yID0gdmFsdWUuY29uc3RydWN0b3I7XG4gICAgaWYgKHR5cGVvZiBjdG9yID09PSAndW5kZWZpbmVkJylcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgLy8gSWYgaGFzIG1vZGlmaWVkIHByb3RvdHlwZVxuICAgIGNvbnN0IHByb3QgPSBjdG9yLnByb3RvdHlwZTtcbiAgICBpZiAoIWhhc09iamVjdFByb3RvdHlwZShwcm90KSlcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIC8vIElmIGNvbnN0cnVjdG9yIGRvZXMgbm90IGhhdmUgYW4gT2JqZWN0LXNwZWNpZmljIG1ldGhvZFxuICAgIC8vIGJpb21lLWlnbm9yZSBsaW50L3N1c3BpY2lvdXMvbm9Qcm90b3R5cGVCdWlsdGluczogPGV4cGxhbmF0aW9uPlxuICAgIGlmICghcHJvdC5oYXNPd25Qcm9wZXJ0eSgnaXNQcm90b3R5cGVPZicpKVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgLy8gTW9zdCBsaWtlbHkgYSBwbGFpbiBPYmplY3RcbiAgICByZXR1cm4gdHJ1ZTtcbn1cbmZ1bmN0aW9uIGhhc09iamVjdFByb3RvdHlwZShvKSB7XG4gICAgcmV0dXJuIE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChvKSA9PT0gJ1tvYmplY3QgT2JqZWN0XSc7XG59XG5leHBvcnQgZnVuY3Rpb24gZmlsdGVyUXVlcnlPcHRpb25zKG9wdGlvbnMpIHtcbiAgICAvLyBkZXN0cnVjdHVyaW5nIGlzIHN1cGVyIGZhc3RcbiAgICAvLyBiaW9tZS1pZ25vcmUgZm9ybWF0OiBubyBmb3JtYXR0aW5nXG4gICAgY29uc3QgeyBcbiAgICAvLyBpbXBvcnQoJ0B0YW5zdGFjay9xdWVyeS1jb3JlJykuUXVlcnlPcHRpb25zXG4gICAgX2RlZmF1bHRlZCwgYmVoYXZpb3IsIGdjVGltZSwgaW5pdGlhbERhdGEsIGluaXRpYWxEYXRhVXBkYXRlZEF0LCBtYXhQYWdlcywgbWV0YSwgbmV0d29ya01vZGUsIHF1ZXJ5Rm4sIHF1ZXJ5SGFzaCwgcXVlcnlLZXksIHF1ZXJ5S2V5SGFzaEZuLCByZXRyeSwgcmV0cnlEZWxheSwgc3RydWN0dXJhbFNoYXJpbmcsIFxuICAgIC8vIGltcG9ydCgnQHRhbnN0YWNrL3F1ZXJ5LWNvcmUnKS5JbmZpbml0ZVF1ZXJ5T2JzZXJ2ZXJPcHRpb25zXG4gICAgZ2V0UHJldmlvdXNQYWdlUGFyYW0sIGdldE5leHRQYWdlUGFyYW0sIGluaXRpYWxQYWdlUGFyYW0sIFxuICAgIC8vIGltcG9ydCgnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JykuVXNlUXVlcnlPcHRpb25zXG4gICAgX29wdGltaXN0aWNSZXN1bHRzLCBlbmFibGVkLCBub3RpZnlPbkNoYW5nZVByb3BzLCBwbGFjZWhvbGRlckRhdGEsIHJlZmV0Y2hJbnRlcnZhbCwgcmVmZXRjaEludGVydmFsSW5CYWNrZ3JvdW5kLCByZWZldGNoT25Nb3VudCwgcmVmZXRjaE9uUmVjb25uZWN0LCByZWZldGNoT25XaW5kb3dGb2N1cywgcmV0cnlPbk1vdW50LCBzZWxlY3QsIHN0YWxlVGltZSwgc3VzcGVuc2UsIHRocm93T25FcnJvciwgXG4gICAgLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cbiAgICAvLyB3YWdtaVxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXG4gICAgY29uZmlnLCBjb25uZWN0b3IsIHF1ZXJ5LCAuLi5yZXN0IH0gPSBvcHRpb25zO1xuICAgIHJldHVybiByZXN0O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/query/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual)\n/* harmony export */ });\n/** Forked from https://github.com/epoberezkin/fast-deep-equal */\nfunction deepEqual(a, b) {\n    if (a === b)\n        return true;\n    if (a && b && typeof a === 'object' && typeof b === 'object') {\n        if (a.constructor !== b.constructor)\n            return false;\n        let length;\n        let i;\n        if (Array.isArray(a) && Array.isArray(b)) {\n            length = a.length;\n            if (length !== b.length)\n                return false;\n            for (i = length; i-- !== 0;)\n                if (!deepEqual(a[i], b[i]))\n                    return false;\n            return true;\n        }\n        if (a.valueOf !== Object.prototype.valueOf)\n            return a.valueOf() === b.valueOf();\n        if (a.toString !== Object.prototype.toString)\n            return a.toString() === b.toString();\n        const keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length)\n            return false;\n        for (i = length; i-- !== 0;)\n            if (!Object.prototype.hasOwnProperty.call(b, keys[i]))\n                return false;\n        for (i = length; i-- !== 0;) {\n            const key = keys[i];\n            if (key && !deepEqual(a[key], b[key]))\n                return false;\n        }\n        return true;\n    }\n    // true if both NaN, false otherwise\n    // biome-ignore lint/suspicious/noSelfCompare: <explanation>\n    return a !== a && b !== b;\n}\n//# sourceMappingURL=deepEqual.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deepEqual.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js":
/*!****************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/deserialize.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\nfunction deserialize(value, reviver) {\n    return JSON.parse(value, (key, value_) => {\n        let value = value_;\n        if (value?.__type === 'bigint')\n            value = BigInt(value.value);\n        if (value?.__type === 'Map')\n            value = new Map(value.value);\n        return reviver?.(key, value) ?? value;\n    });\n}\n//# sourceMappingURL=deserialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZGVzZXJpYWxpemUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZGVzZXJpYWxpemUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGRlc2VyaWFsaXplKHZhbHVlLCByZXZpdmVyKSB7XG4gICAgcmV0dXJuIEpTT04ucGFyc2UodmFsdWUsIChrZXksIHZhbHVlXykgPT4ge1xuICAgICAgICBsZXQgdmFsdWUgPSB2YWx1ZV87XG4gICAgICAgIGlmICh2YWx1ZT8uX190eXBlID09PSAnYmlnaW50JylcbiAgICAgICAgICAgIHZhbHVlID0gQmlnSW50KHZhbHVlLnZhbHVlKTtcbiAgICAgICAgaWYgKHZhbHVlPy5fX3R5cGUgPT09ICdNYXAnKVxuICAgICAgICAgICAgdmFsdWUgPSBuZXcgTWFwKHZhbHVlLnZhbHVlKTtcbiAgICAgICAgcmV0dXJuIHJldml2ZXI/LihrZXksIHZhbHVlKSA/PyB2YWx1ZTtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRlc2VyaWFsaXplLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getAction.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAction: () => (/* binding */ getAction)\n/* harmony export */ });\n/**\n * Retrieves and returns an action from the client (if exists), and falls\n * back to the tree-shakable action.\n *\n * Useful for extracting overridden actions from a client (ie. if a consumer\n * wants to override the `sendTransaction` implementation).\n */\nfunction getAction(client, actionFn, \n// Some minifiers drop `Function.prototype.name`, or replace it with short letters,\n// meaning that `actionFn.name` will not always work. For that case, the consumer\n// needs to pass the name explicitly.\nname) {\n    const action_implicit = client[actionFn.name];\n    if (typeof action_implicit === 'function')\n        return action_implicit;\n    const action_explicit = client[name];\n    if (typeof action_explicit === 'function')\n        return action_explicit;\n    return (params) => actionFn(client, params);\n}\n//# sourceMappingURL=getAction.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0QWN0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2dldEFjdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFJldHJpZXZlcyBhbmQgcmV0dXJucyBhbiBhY3Rpb24gZnJvbSB0aGUgY2xpZW50IChpZiBleGlzdHMpLCBhbmQgZmFsbHNcbiAqIGJhY2sgdG8gdGhlIHRyZWUtc2hha2FibGUgYWN0aW9uLlxuICpcbiAqIFVzZWZ1bCBmb3IgZXh0cmFjdGluZyBvdmVycmlkZGVuIGFjdGlvbnMgZnJvbSBhIGNsaWVudCAoaWUuIGlmIGEgY29uc3VtZXJcbiAqIHdhbnRzIHRvIG92ZXJyaWRlIHRoZSBgc2VuZFRyYW5zYWN0aW9uYCBpbXBsZW1lbnRhdGlvbikuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRBY3Rpb24oY2xpZW50LCBhY3Rpb25GbiwgXG4vLyBTb21lIG1pbmlmaWVycyBkcm9wIGBGdW5jdGlvbi5wcm90b3R5cGUubmFtZWAsIG9yIHJlcGxhY2UgaXQgd2l0aCBzaG9ydCBsZXR0ZXJzLFxuLy8gbWVhbmluZyB0aGF0IGBhY3Rpb25Gbi5uYW1lYCB3aWxsIG5vdCBhbHdheXMgd29yay4gRm9yIHRoYXQgY2FzZSwgdGhlIGNvbnN1bWVyXG4vLyBuZWVkcyB0byBwYXNzIHRoZSBuYW1lIGV4cGxpY2l0bHkuXG5uYW1lKSB7XG4gICAgY29uc3QgYWN0aW9uX2ltcGxpY2l0ID0gY2xpZW50W2FjdGlvbkZuLm5hbWVdO1xuICAgIGlmICh0eXBlb2YgYWN0aW9uX2ltcGxpY2l0ID09PSAnZnVuY3Rpb24nKVxuICAgICAgICByZXR1cm4gYWN0aW9uX2ltcGxpY2l0O1xuICAgIGNvbnN0IGFjdGlvbl9leHBsaWNpdCA9IGNsaWVudFtuYW1lXTtcbiAgICBpZiAodHlwZW9mIGFjdGlvbl9leHBsaWNpdCA9PT0gJ2Z1bmN0aW9uJylcbiAgICAgICAgcmV0dXJuIGFjdGlvbl9leHBsaWNpdDtcbiAgICByZXR1cm4gKHBhcmFtcykgPT4gYWN0aW9uRm4oY2xpZW50LCBwYXJhbXMpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0QWN0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getAction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getUnit.js":
/*!************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getUnit.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUnit: () => (/* binding */ getUnit)\n/* harmony export */ });\n/* harmony import */ var viem__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! viem */ \"(ssr)/./node_modules/viem/_esm/constants/unit.js\");\n\nfunction getUnit(unit) {\n    if (typeof unit === 'number')\n        return unit;\n    if (unit === 'wei')\n        return 0;\n    return Math.abs(viem__WEBPACK_IMPORTED_MODULE_0__.weiUnits[unit]);\n}\n//# sourceMappingURL=getUnit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VW5pdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnQztBQUN6QjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDBDQUFRO0FBQzVCO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B3YWdtaS9jb3JlL2Rpc3QvZXNtL3V0aWxzL2dldFVuaXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd2VpVW5pdHMgfSBmcm9tICd2aWVtJztcbmV4cG9ydCBmdW5jdGlvbiBnZXRVbml0KHVuaXQpIHtcbiAgICBpZiAodHlwZW9mIHVuaXQgPT09ICdudW1iZXInKVxuICAgICAgICByZXR1cm4gdW5pdDtcbiAgICBpZiAodW5pdCA9PT0gJ3dlaScpXG4gICAgICAgIHJldHVybiAwO1xuICAgIHJldHVybiBNYXRoLmFicyh3ZWlVbml0c1t1bml0XSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nZXRVbml0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getUnit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js":
/*!***************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/getVersion.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getVersion: () => (/* binding */ getVersion)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\");\n\nconst getVersion = () => `@wagmi/core@${_version_js__WEBPACK_IMPORTED_MODULE_0__.version}`;\n//# sourceMappingURL=getVersion.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QztBQUNqQyx3Q0FBd0MsZ0RBQU8sQ0FBQztBQUN2RCIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvZ2V0VmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnLi4vdmVyc2lvbi5qcyc7XG5leHBvcnQgY29uc3QgZ2V0VmVyc2lvbiA9ICgpID0+IGBAd2FnbWkvY29yZUAke3ZlcnNpb259YDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldFZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/getVersion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js":
/*!**************************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/serialize.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/**\n * Get the reference key for the circular value\n *\n * @param keys the keys to build the reference key from\n * @param cutoff the maximum number of keys to include\n * @returns the reference key\n */\nfunction getReferenceKey(keys, cutoff) {\n    return keys.slice(0, cutoff).join('.') || '.';\n}\n/**\n * Faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array, value) {\n    const { length } = array;\n    for (let index = 0; index < length; ++index) {\n        if (array[index] === value) {\n            return index + 1;\n        }\n    }\n    return 0;\n}\n/**\n * Create a replacer method that handles circular values\n *\n * @param [replacer] a custom replacer to use for non-circular values\n * @param [circularReplacer] a custom replacer to use for circular methods\n * @returns the value to stringify\n */\nfunction createReplacer(replacer, circularReplacer) {\n    const hasReplacer = typeof replacer === 'function';\n    const hasCircularReplacer = typeof circularReplacer === 'function';\n    const cache = [];\n    const keys = [];\n    return function replace(key, value) {\n        if (typeof value === 'object') {\n            if (cache.length) {\n                const thisCutoff = getCutoff(cache, this);\n                if (thisCutoff === 0) {\n                    cache[cache.length] = this;\n                }\n                else {\n                    cache.splice(thisCutoff);\n                    keys.splice(thisCutoff);\n                }\n                keys[keys.length] = key;\n                const valueCutoff = getCutoff(cache, value);\n                if (valueCutoff !== 0) {\n                    return hasCircularReplacer\n                        ? circularReplacer.call(this, key, value, getReferenceKey(keys, valueCutoff))\n                        : `[ref=${getReferenceKey(keys, valueCutoff)}]`;\n                }\n            }\n            else {\n                cache[0] = value;\n                keys[0] = key;\n            }\n        }\n        return hasReplacer ? replacer.call(this, key, value) : value;\n    };\n}\n/**\n * Stringifier that handles circular values\n *\n * Forked from https://github.com/planttheidea/fast-stringify\n *\n * @param value to stringify\n * @param [replacer] a custom replacer function for handling standard values\n * @param [indent] the number of spaces to indent the output by\n * @param [circularReplacer] a custom replacer function for handling circular values\n * @returns the stringified output\n */\nfunction serialize(value, replacer, indent, circularReplacer) {\n    return JSON.stringify(value, createReplacer((key, value_) => {\n        let value = value_;\n        if (typeof value === 'bigint')\n            value = { __type: 'bigint', value: value_.toString() };\n        if (value instanceof Map)\n            value = { __type: 'Map', value: Array.from(value_.entries()) };\n        return replacer?.(key, value) ?? value;\n    }, circularReplacer), indent ?? undefined);\n}\n//# sourceMappingURL=serialize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js":
/*!********************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/utils/uid.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uid: () => (/* binding */ uid)\n/* harmony export */ });\nconst size = 256;\nlet index = size;\nlet buffer;\nfunction uid(length = 11) {\n    if (!buffer || index + length > size * 2) {\n        buffer = '';\n        index = 0;\n        for (let i = 0; i < size; i++) {\n            buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1);\n        }\n    }\n    return buffer.substring(index, index++ + length);\n}\n//# sourceMappingURL=uid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvdWlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixVQUFVO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdXRpbHMvdWlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHNpemUgPSAyNTY7XG5sZXQgaW5kZXggPSBzaXplO1xubGV0IGJ1ZmZlcjtcbmV4cG9ydCBmdW5jdGlvbiB1aWQobGVuZ3RoID0gMTEpIHtcbiAgICBpZiAoIWJ1ZmZlciB8fCBpbmRleCArIGxlbmd0aCA+IHNpemUgKiAyKSB7XG4gICAgICAgIGJ1ZmZlciA9ICcnO1xuICAgICAgICBpbmRleCA9IDA7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc2l6ZTsgaSsrKSB7XG4gICAgICAgICAgICBidWZmZXIgKz0gKCgyNTYgKyBNYXRoLnJhbmRvbSgpICogMjU2KSB8IDApLnRvU3RyaW5nKDE2KS5zdWJzdHJpbmcoMSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGJ1ZmZlci5zdWJzdHJpbmcoaW5kZXgsIGluZGV4KysgKyBsZW5ndGgpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dWlkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/utils/uid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/dist/esm/version.js":
/*!******************************************************!*\
  !*** ./node_modules/@wagmi/core/dist/esm/version.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.17.2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvZGlzdC9lc20vdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjE3LjInO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/dist/esm/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/node_modules/eventemitter3/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@wagmi/core/node_modules/eventemitter3/index.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif (true) {\n  module.exports = EventEmitter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/node_modules/eventemitter3/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wagmi/core/node_modules/eventemitter3/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@wagmi/core/node_modules/eventemitter3/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventEmitter: () => (/* reexport default export from named module */ _index_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(ssr)/./node_modules/@wagmi/core/node_modules/eventemitter3/index.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_index_js__WEBPACK_IMPORTED_MODULE_0__);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvbm9kZV9tb2R1bGVzL2V2ZW50ZW1pdHRlcjMvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQzs7QUFFZDtBQUN2QixpRUFBZSxzQ0FBWSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHdhZ21pL2NvcmUvbm9kZV9tb2R1bGVzL2V2ZW50ZW1pdHRlcjMvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBFdmVudEVtaXR0ZXIgZnJvbSAnLi9pbmRleC5qcydcblxuZXhwb3J0IHsgRXZlbnRFbWl0dGVyIH1cbmV4cG9ydCBkZWZhdWx0IEV2ZW50RW1pdHRlclxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wagmi/core/node_modules/eventemitter3/index.mjs\n");

/***/ })

};
;