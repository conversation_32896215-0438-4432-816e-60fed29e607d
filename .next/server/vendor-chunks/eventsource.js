"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventsource";
exports.ids = ["vendor-chunks/eventsource"];
exports.modules = {

/***/ "(ssr)/./node_modules/eventsource/dist/index.js":
/*!************************************************!*\
  !*** ./node_modules/eventsource/dist/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorEvent: () => (/* binding */ ErrorEvent),\n/* harmony export */   EventSource: () => (/* binding */ EventSource)\n/* harmony export */ });\n/* harmony import */ var eventsource_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! eventsource-parser */ \"(ssr)/./node_modules/eventsource-parser/dist/index.js\");\n\nclass ErrorEvent extends Event {\n  /**\n   * Constructs a new `ErrorEvent` instance. This is typically not called directly,\n   * but rather emitted by the `EventSource` object when an error occurs.\n   *\n   * @param type - The type of the event (should be \"error\")\n   * @param errorEventInitDict - Optional properties to include in the error event\n   */\n  constructor(type, errorEventInitDict) {\n    var _a, _b;\n    super(type), this.code = (_a = errorEventInitDict == null ? void 0 : errorEventInitDict.code) != null ? _a : void 0, this.message = (_b = errorEventInitDict == null ? void 0 : errorEventInitDict.message) != null ? _b : void 0;\n  }\n  /**\n   * Node.js \"hides\" the `message` and `code` properties of the `ErrorEvent` instance,\n   * when it is `console.log`'ed. This makes it harder to debug errors. To ease debugging,\n   * we explicitly include the properties in the `inspect` method.\n   *\n   * This is automatically called by Node.js when you `console.log` an instance of this class.\n   *\n   * @param _depth - The current depth\n   * @param options - The options passed to `util.inspect`\n   * @param inspect - The inspect function to use (prevents having to import it from `util`)\n   * @returns A string representation of the error\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")](_depth, options, inspect) {\n    return inspect(inspectableError(this), options);\n  }\n  /**\n   * Deno \"hides\" the `message` and `code` properties of the `ErrorEvent` instance,\n   * when it is `console.log`'ed. This makes it harder to debug errors. To ease debugging,\n   * we explicitly include the properties in the `inspect` method.\n   *\n   * This is automatically called by Deno when you `console.log` an instance of this class.\n   *\n   * @param inspect - The inspect function to use (prevents having to import it from `util`)\n   * @param options - The options passed to `Deno.inspect`\n   * @returns A string representation of the error\n   */\n  [Symbol.for(\"Deno.customInspect\")](inspect, options) {\n    return inspect(inspectableError(this), options);\n  }\n}\nfunction syntaxError(message) {\n  const DomException = globalThis.DOMException;\n  return typeof DomException == \"function\" ? new DomException(message, \"SyntaxError\") : new SyntaxError(message);\n}\nfunction flattenError(err) {\n  return err instanceof Error ? \"errors\" in err && Array.isArray(err.errors) ? err.errors.map(flattenError).join(\", \") : \"cause\" in err && err.cause instanceof Error ? `${err}: ${flattenError(err.cause)}` : err.message : `${err}`;\n}\nfunction inspectableError(err) {\n  return {\n    type: err.type,\n    message: err.message,\n    code: err.code,\n    defaultPrevented: err.defaultPrevented,\n    cancelable: err.cancelable,\n    timeStamp: err.timeStamp\n  };\n}\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n}, __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg), __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj)), __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value), __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), member.set(obj, value), value), __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method), _readyState, _url, _redirectUrl, _withCredentials, _fetch, _reconnectInterval, _reconnectTimer, _lastEventId, _controller, _parser, _onError, _onMessage, _onOpen, _EventSource_instances, connect_fn, _onFetchResponse, _onFetchError, getRequestOptions_fn, _onEvent, _onRetryChange, failConnection_fn, scheduleReconnect_fn, _reconnect;\nclass EventSource extends EventTarget {\n  constructor(url, eventSourceInitDict) {\n    var _a, _b;\n    super(), __privateAdd(this, _EventSource_instances), this.CONNECTING = 0, this.OPEN = 1, this.CLOSED = 2, __privateAdd(this, _readyState), __privateAdd(this, _url), __privateAdd(this, _redirectUrl), __privateAdd(this, _withCredentials), __privateAdd(this, _fetch), __privateAdd(this, _reconnectInterval), __privateAdd(this, _reconnectTimer), __privateAdd(this, _lastEventId, null), __privateAdd(this, _controller), __privateAdd(this, _parser), __privateAdd(this, _onError, null), __privateAdd(this, _onMessage, null), __privateAdd(this, _onOpen, null), __privateAdd(this, _onFetchResponse, async (response) => {\n      var _a2;\n      __privateGet(this, _parser).reset();\n      const { body, redirected, status, headers } = response;\n      if (status === 204) {\n        __privateMethod(this, _EventSource_instances, failConnection_fn).call(this, \"Server sent HTTP 204, not reconnecting\", 204), this.close();\n        return;\n      }\n      if (redirected ? __privateSet(this, _redirectUrl, new URL(response.url)) : __privateSet(this, _redirectUrl, void 0), status !== 200) {\n        __privateMethod(this, _EventSource_instances, failConnection_fn).call(this, `Non-200 status code (${status})`, status);\n        return;\n      }\n      if (!(headers.get(\"content-type\") || \"\").startsWith(\"text/event-stream\")) {\n        __privateMethod(this, _EventSource_instances, failConnection_fn).call(this, 'Invalid content type, expected \"text/event-stream\"', status);\n        return;\n      }\n      if (__privateGet(this, _readyState) === this.CLOSED)\n        return;\n      __privateSet(this, _readyState, this.OPEN);\n      const openEvent = new Event(\"open\");\n      if ((_a2 = __privateGet(this, _onOpen)) == null || _a2.call(this, openEvent), this.dispatchEvent(openEvent), typeof body != \"object\" || !body || !(\"getReader\" in body)) {\n        __privateMethod(this, _EventSource_instances, failConnection_fn).call(this, \"Invalid response body, expected a web ReadableStream\", status), this.close();\n        return;\n      }\n      const decoder = new TextDecoder(), reader = body.getReader();\n      let open = !0;\n      do {\n        const { done, value } = await reader.read();\n        value && __privateGet(this, _parser).feed(decoder.decode(value, { stream: !done })), done && (open = !1, __privateGet(this, _parser).reset(), __privateMethod(this, _EventSource_instances, scheduleReconnect_fn).call(this));\n      } while (open);\n    }), __privateAdd(this, _onFetchError, (err) => {\n      __privateSet(this, _controller, void 0), !(err.name === \"AbortError\" || err.type === \"aborted\") && __privateMethod(this, _EventSource_instances, scheduleReconnect_fn).call(this, flattenError(err));\n    }), __privateAdd(this, _onEvent, (event) => {\n      typeof event.id == \"string\" && __privateSet(this, _lastEventId, event.id);\n      const messageEvent = new MessageEvent(event.event || \"message\", {\n        data: event.data,\n        origin: __privateGet(this, _redirectUrl) ? __privateGet(this, _redirectUrl).origin : __privateGet(this, _url).origin,\n        lastEventId: event.id || \"\"\n      });\n      __privateGet(this, _onMessage) && (!event.event || event.event === \"message\") && __privateGet(this, _onMessage).call(this, messageEvent), this.dispatchEvent(messageEvent);\n    }), __privateAdd(this, _onRetryChange, (value) => {\n      __privateSet(this, _reconnectInterval, value);\n    }), __privateAdd(this, _reconnect, () => {\n      __privateSet(this, _reconnectTimer, void 0), __privateGet(this, _readyState) === this.CONNECTING && __privateMethod(this, _EventSource_instances, connect_fn).call(this);\n    });\n    try {\n      if (url instanceof URL)\n        __privateSet(this, _url, url);\n      else if (typeof url == \"string\")\n        __privateSet(this, _url, new URL(url, getBaseURL()));\n      else\n        throw new Error(\"Invalid URL\");\n    } catch {\n      throw syntaxError(\"An invalid or illegal string was specified\");\n    }\n    __privateSet(this, _parser, (0,eventsource_parser__WEBPACK_IMPORTED_MODULE_0__.createParser)({\n      onEvent: __privateGet(this, _onEvent),\n      onRetry: __privateGet(this, _onRetryChange)\n    })), __privateSet(this, _readyState, this.CONNECTING), __privateSet(this, _reconnectInterval, 3e3), __privateSet(this, _fetch, (_a = eventSourceInitDict == null ? void 0 : eventSourceInitDict.fetch) != null ? _a : globalThis.fetch), __privateSet(this, _withCredentials, (_b = eventSourceInitDict == null ? void 0 : eventSourceInitDict.withCredentials) != null ? _b : !1), __privateMethod(this, _EventSource_instances, connect_fn).call(this);\n  }\n  /**\n   * Returns the state of this EventSource object's connection. It can have the values described below.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/readyState)\n   *\n   * Note: typed as `number` instead of `0 | 1 | 2` for compatibility with the `EventSource` interface,\n   * defined in the TypeScript `dom` library.\n   *\n   * @public\n   */\n  get readyState() {\n    return __privateGet(this, _readyState);\n  }\n  /**\n   * Returns the URL providing the event stream.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/url)\n   *\n   * @public\n   */\n  get url() {\n    return __privateGet(this, _url).href;\n  }\n  /**\n   * Returns true if the credentials mode for connection requests to the URL providing the event stream is set to \"include\", and false otherwise.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/withCredentials)\n   */\n  get withCredentials() {\n    return __privateGet(this, _withCredentials);\n  }\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/error_event) */\n  get onerror() {\n    return __privateGet(this, _onError);\n  }\n  set onerror(value) {\n    __privateSet(this, _onError, value);\n  }\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/message_event) */\n  get onmessage() {\n    return __privateGet(this, _onMessage);\n  }\n  set onmessage(value) {\n    __privateSet(this, _onMessage, value);\n  }\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/open_event) */\n  get onopen() {\n    return __privateGet(this, _onOpen);\n  }\n  set onopen(value) {\n    __privateSet(this, _onOpen, value);\n  }\n  addEventListener(type, listener, options) {\n    const listen = listener;\n    super.addEventListener(type, listen, options);\n  }\n  removeEventListener(type, listener, options) {\n    const listen = listener;\n    super.removeEventListener(type, listen, options);\n  }\n  /**\n   * Aborts any instances of the fetch algorithm started for this EventSource object, and sets the readyState attribute to CLOSED.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/close)\n   *\n   * @public\n   */\n  close() {\n    __privateGet(this, _reconnectTimer) && clearTimeout(__privateGet(this, _reconnectTimer)), __privateGet(this, _readyState) !== this.CLOSED && (__privateGet(this, _controller) && __privateGet(this, _controller).abort(), __privateSet(this, _readyState, this.CLOSED), __privateSet(this, _controller, void 0));\n  }\n}\n_readyState = /* @__PURE__ */ new WeakMap(), _url = /* @__PURE__ */ new WeakMap(), _redirectUrl = /* @__PURE__ */ new WeakMap(), _withCredentials = /* @__PURE__ */ new WeakMap(), _fetch = /* @__PURE__ */ new WeakMap(), _reconnectInterval = /* @__PURE__ */ new WeakMap(), _reconnectTimer = /* @__PURE__ */ new WeakMap(), _lastEventId = /* @__PURE__ */ new WeakMap(), _controller = /* @__PURE__ */ new WeakMap(), _parser = /* @__PURE__ */ new WeakMap(), _onError = /* @__PURE__ */ new WeakMap(), _onMessage = /* @__PURE__ */ new WeakMap(), _onOpen = /* @__PURE__ */ new WeakMap(), _EventSource_instances = /* @__PURE__ */ new WeakSet(), /**\n* Connect to the given URL and start receiving events\n*\n* @internal\n*/\nconnect_fn = function() {\n  __privateSet(this, _readyState, this.CONNECTING), __privateSet(this, _controller, new AbortController()), __privateGet(this, _fetch)(__privateGet(this, _url), __privateMethod(this, _EventSource_instances, getRequestOptions_fn).call(this)).then(__privateGet(this, _onFetchResponse)).catch(__privateGet(this, _onFetchError));\n}, _onFetchResponse = /* @__PURE__ */ new WeakMap(), _onFetchError = /* @__PURE__ */ new WeakMap(), /**\n* Get request options for the `fetch()` request\n*\n* @returns The request options\n* @internal\n*/\ngetRequestOptions_fn = function() {\n  var _a;\n  const init = {\n    // [spec] Let `corsAttributeState` be `Anonymous`…\n    // [spec] …will have their mode set to \"cors\"…\n    mode: \"cors\",\n    redirect: \"follow\",\n    headers: { Accept: \"text/event-stream\", ...__privateGet(this, _lastEventId) ? { \"Last-Event-ID\": __privateGet(this, _lastEventId) } : void 0 },\n    cache: \"no-store\",\n    signal: (_a = __privateGet(this, _controller)) == null ? void 0 : _a.signal\n  };\n  return \"window\" in globalThis && (init.credentials = this.withCredentials ? \"include\" : \"same-origin\"), init;\n}, _onEvent = /* @__PURE__ */ new WeakMap(), _onRetryChange = /* @__PURE__ */ new WeakMap(), /**\n* Handles the process referred to in the EventSource specification as \"failing a connection\".\n*\n* @param error - The error causing the connection to fail\n* @param code - The HTTP status code, if available\n* @internal\n*/\nfailConnection_fn = function(message, code) {\n  var _a;\n  __privateGet(this, _readyState) !== this.CLOSED && __privateSet(this, _readyState, this.CLOSED);\n  const errorEvent = new ErrorEvent(\"error\", { code, message });\n  (_a = __privateGet(this, _onError)) == null || _a.call(this, errorEvent), this.dispatchEvent(errorEvent);\n}, /**\n* Schedules a reconnection attempt against the EventSource endpoint.\n*\n* @param message - The error causing the connection to fail\n* @param code - The HTTP status code, if available\n* @internal\n*/\nscheduleReconnect_fn = function(message, code) {\n  var _a;\n  if (__privateGet(this, _readyState) === this.CLOSED)\n    return;\n  __privateSet(this, _readyState, this.CONNECTING);\n  const errorEvent = new ErrorEvent(\"error\", { code, message });\n  (_a = __privateGet(this, _onError)) == null || _a.call(this, errorEvent), this.dispatchEvent(errorEvent), __privateSet(this, _reconnectTimer, setTimeout(__privateGet(this, _reconnect), __privateGet(this, _reconnectInterval)));\n}, _reconnect = /* @__PURE__ */ new WeakMap(), /**\n* ReadyState representing an EventSource currently trying to connect\n*\n* @public\n*/\nEventSource.CONNECTING = 0, /**\n* ReadyState representing an EventSource connection that is open (eg connected)\n*\n* @public\n*/\nEventSource.OPEN = 1, /**\n* ReadyState representing an EventSource connection that is closed (eg disconnected)\n*\n* @public\n*/\nEventSource.CLOSED = 2;\nfunction getBaseURL() {\n  const doc = \"document\" in globalThis ? globalThis.document : void 0;\n  return doc && typeof doc == \"object\" && \"baseURI\" in doc && typeof doc.baseURI == \"string\" ? doc.baseURI : void 0;\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eventsource/dist/index.js\n");

/***/ })

};
;