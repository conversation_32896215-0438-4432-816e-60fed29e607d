"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pkce-challenge";
exports.ids = ["vendor-chunks/pkce-challenge"];
exports.modules = {

/***/ "(ssr)/./node_modules/pkce-challenge/dist/index.node.js":
/*!********************************************************!*\
  !*** ./node_modules/pkce-challenge/dist/index.node.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pkceChallenge),\n/* harmony export */   generateChallenge: () => (/* binding */ generateChallenge),\n/* harmony export */   verifyChallenge: () => (/* binding */ verifyChallenge)\n/* harmony export */ });\nlet crypto;\ncrypto =\n    globalThis.crypto?.webcrypto ?? // Node.js [18-16] REPL\n        globalThis.crypto ?? // Node.js >18\n        Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! node:crypto */ \"node:crypto\", 19)).then(m => m.webcrypto); // Node.js <18 Non-REPL\n/**\n * Creates an array of length `size` of random bytes\n * @param size\n * @returns Array of random ints (0 to 255)\n */\nasync function getRandomValues(size) {\n    return (await crypto).getRandomValues(new Uint8Array(size));\n}\n/** Generate cryptographically strong random string\n * @param size The desired length of the string\n * @returns The random string\n */\nasync function random(size) {\n    const mask = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._~\";\n    let result = \"\";\n    const randomUints = await getRandomValues(size);\n    for (let i = 0; i < size; i++) {\n        // cap the value of the randomIndex to mask.length - 1\n        const randomIndex = randomUints[i] % mask.length;\n        result += mask[randomIndex];\n    }\n    return result;\n}\n/** Generate a PKCE challenge verifier\n * @param length Length of the verifier\n * @returns A random verifier `length` characters long\n */\nasync function generateVerifier(length) {\n    return await random(length);\n}\n/** Generate a PKCE code challenge from a code verifier\n * @param code_verifier\n * @returns The base64 url encoded code challenge\n */\nasync function generateChallenge(code_verifier) {\n    const buffer = await (await crypto).subtle.digest(\"SHA-256\", new TextEncoder().encode(code_verifier));\n    // Generate base64url string\n    // btoa is deprecated in Node.js but is used here for web browser compatibility\n    // (which has no good replacement yet, see also https://github.com/whatwg/html/issues/6811)\n    return btoa(String.fromCharCode(...new Uint8Array(buffer)))\n        .replace(/\\//g, '_')\n        .replace(/\\+/g, '-')\n        .replace(/=/g, '');\n}\n/** Generate a PKCE challenge pair\n * @param length Length of the verifer (between 43-128). Defaults to 43.\n * @returns PKCE challenge pair\n */\nasync function pkceChallenge(length) {\n    if (!length)\n        length = 43;\n    if (length < 43 || length > 128) {\n        throw `Expected a length between 43 and 128. Received ${length}.`;\n    }\n    const verifier = await generateVerifier(length);\n    const challenge = await generateChallenge(verifier);\n    return {\n        code_verifier: verifier,\n        code_challenge: challenge,\n    };\n}\n/** Verify that a code_verifier produces the expected code challenge\n * @param code_verifier\n * @param expectedChallenge The code challenge to verify\n * @returns True if challenges are equal. False otherwise.\n */\nasync function verifyChallenge(code_verifier, expectedChallenge) {\n    const actualChallenge = await generateChallenge(code_verifier);\n    return actualChallenge === expectedChallenge;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pkce-challenge/dist/index.node.js\n");

/***/ })

};
;