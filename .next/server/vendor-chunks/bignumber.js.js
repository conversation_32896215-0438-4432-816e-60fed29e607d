"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bignumber.js";
exports.ids = ["vendor-chunks/bignumber.js"];
exports.modules = {

/***/ "(rsc)/./node_modules/bignumber.js/bignumber.mjs":
/*!*************************************************!*\
  !*** ./node_modules/bignumber.js/bignumber.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BigNumber: () => (/* binding */ BigNumber),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *      bignumber.js v9.3.0\r\n *      A JavaScript library for arbitrary-precision arithmetic.\r\n *      https://github.com/MikeMcl/bignumber.js\r\n *      Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>\r\n *      MIT Licensed.\r\n *\r\n *      BigNumber.prototype methods     |  BigNumber methods\r\n *                                      |\r\n *      absoluteValue            abs    |  clone\r\n *      comparedTo                      |  config               set\r\n *      decimalPlaces            dp     |      DECIMAL_PLACES\r\n *      dividedBy                div    |      ROUNDING_MODE\r\n *      dividedToIntegerBy       idiv   |      EXPONENTIAL_AT\r\n *      exponentiatedBy          pow    |      RANGE\r\n *      integerValue                    |      CRYPTO\r\n *      isEqualTo                eq     |      MODULO_MODE\r\n *      isFinite                        |      POW_PRECISION\r\n *      isGreaterThan            gt     |      FORMAT\r\n *      isGreaterThanOrEqualTo   gte    |      ALPHABET\r\n *      isInteger                       |  isBigNumber\r\n *      isLessThan               lt     |  maximum              max\r\n *      isLessThanOrEqualTo      lte    |  minimum              min\r\n *      isNaN                           |  random\r\n *      isNegative                      |  sum\r\n *      isPositive                      |\r\n *      isZero                          |\r\n *      minus                           |\r\n *      modulo                   mod    |\r\n *      multipliedBy             times  |\r\n *      negated                         |\r\n *      plus                            |\r\n *      precision                sd     |\r\n *      shiftedBy                       |\r\n *      squareRoot               sqrt   |\r\n *      toExponential                   |\r\n *      toFixed                         |\r\n *      toFormat                        |\r\n *      toFraction                      |\r\n *      toJSON                          |\r\n *      toNumber                        |\r\n *      toPrecision                     |\r\n *      toString                        |\r\n *      valueOf                         |\r\n *\r\n */\r\n\r\n\r\nvar\r\n  isNumeric = /^-?(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?$/i,\r\n  mathceil = Math.ceil,\r\n  mathfloor = Math.floor,\r\n\r\n  bignumberError = '[BigNumber Error] ',\r\n  tooManyDigits = bignumberError + 'Number primitive has more than 15 significant digits: ',\r\n\r\n  BASE = 1e14,\r\n  LOG_BASE = 14,\r\n  MAX_SAFE_INTEGER = 0x1fffffffffffff,         // 2^53 - 1\r\n  // MAX_INT32 = 0x7fffffff,                   // 2^31 - 1\r\n  POWS_TEN = [1, 10, 100, 1e3, 1e4, 1e5, 1e6, 1e7, 1e8, 1e9, 1e10, 1e11, 1e12, 1e13],\r\n  SQRT_BASE = 1e7,\r\n\r\n  // EDITABLE\r\n  // The limit on the value of DECIMAL_PLACES, TO_EXP_NEG, TO_EXP_POS, MIN_EXP, MAX_EXP, and\r\n  // the arguments to toExponential, toFixed, toFormat, and toPrecision.\r\n  MAX = 1E9;                                   // 0 to MAX_INT32\r\n\r\n\r\n/*\r\n * Create and return a BigNumber constructor.\r\n */\r\nfunction clone(configObject) {\r\n  var div, convertBase, parseNumeric,\r\n    P = BigNumber.prototype = { constructor: BigNumber, toString: null, valueOf: null },\r\n    ONE = new BigNumber(1),\r\n\r\n\r\n    //----------------------------- EDITABLE CONFIG DEFAULTS -------------------------------\r\n\r\n\r\n    // The default values below must be integers within the inclusive ranges stated.\r\n    // The values can also be changed at run-time using BigNumber.set.\r\n\r\n    // The maximum number of decimal places for operations involving division.\r\n    DECIMAL_PLACES = 20,                     // 0 to MAX\r\n\r\n    // The rounding mode used when rounding to the above decimal places, and when using\r\n    // toExponential, toFixed, toFormat and toPrecision, and round (default value).\r\n    // UP         0 Away from zero.\r\n    // DOWN       1 Towards zero.\r\n    // CEIL       2 Towards +Infinity.\r\n    // FLOOR      3 Towards -Infinity.\r\n    // HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n    // HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n    // HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n    // HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n    // HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n    ROUNDING_MODE = 4,                       // 0 to 8\r\n\r\n    // EXPONENTIAL_AT : [TO_EXP_NEG , TO_EXP_POS]\r\n\r\n    // The exponent value at and beneath which toString returns exponential notation.\r\n    // Number type: -7\r\n    TO_EXP_NEG = -7,                         // 0 to -MAX\r\n\r\n    // The exponent value at and above which toString returns exponential notation.\r\n    // Number type: 21\r\n    TO_EXP_POS = 21,                         // 0 to MAX\r\n\r\n    // RANGE : [MIN_EXP, MAX_EXP]\r\n\r\n    // The minimum exponent value, beneath which underflow to zero occurs.\r\n    // Number type: -324  (5e-324)\r\n    MIN_EXP = -1e7,                          // -1 to -MAX\r\n\r\n    // The maximum exponent value, above which overflow to Infinity occurs.\r\n    // Number type:  308  (1.7976931348623157e+308)\r\n    // For MAX_EXP > 1e7, e.g. new BigNumber('1e100000000').plus(1) may be slow.\r\n    MAX_EXP = 1e7,                           // 1 to MAX\r\n\r\n    // Whether to use cryptographically-secure random number generation, if available.\r\n    CRYPTO = false,                          // true or false\r\n\r\n    // The modulo mode used when calculating the modulus: a mod n.\r\n    // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\r\n    // The remainder (r) is calculated as: r = a - n * q.\r\n    //\r\n    // UP        0 The remainder is positive if the dividend is negative, else is negative.\r\n    // DOWN      1 The remainder has the same sign as the dividend.\r\n    //             This modulo mode is commonly known as 'truncated division' and is\r\n    //             equivalent to (a % n) in JavaScript.\r\n    // FLOOR     3 The remainder has the same sign as the divisor (Python %).\r\n    // HALF_EVEN 6 This modulo mode implements the IEEE 754 remainder function.\r\n    // EUCLID    9 Euclidian division. q = sign(n) * floor(a / abs(n)).\r\n    //             The remainder is always positive.\r\n    //\r\n    // The truncated division, floored division, Euclidian division and IEEE 754 remainder\r\n    // modes are commonly used for the modulus operation.\r\n    // Although the other rounding modes can also be used, they may not give useful results.\r\n    MODULO_MODE = 1,                         // 0 to 9\r\n\r\n    // The maximum number of significant digits of the result of the exponentiatedBy operation.\r\n    // If POW_PRECISION is 0, there will be unlimited significant digits.\r\n    POW_PRECISION = 0,                       // 0 to MAX\r\n\r\n    // The format specification used by the BigNumber.prototype.toFormat method.\r\n    FORMAT = {\r\n      prefix: '',\r\n      groupSize: 3,\r\n      secondaryGroupSize: 0,\r\n      groupSeparator: ',',\r\n      decimalSeparator: '.',\r\n      fractionGroupSize: 0,\r\n      fractionGroupSeparator: '\\xA0',        // non-breaking space\r\n      suffix: ''\r\n    },\r\n\r\n    // The alphabet used for base conversion. It must be at least 2 characters long, with no '+',\r\n    // '-', '.', whitespace, or repeated character.\r\n    // '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_'\r\n    ALPHABET = '0123456789abcdefghijklmnopqrstuvwxyz',\r\n    alphabetHasNormalDecimalDigits = true;\r\n\r\n\r\n  //------------------------------------------------------------------------------------------\r\n\r\n\r\n  // CONSTRUCTOR\r\n\r\n\r\n  /*\r\n   * The BigNumber constructor and exported function.\r\n   * Create and return a new instance of a BigNumber object.\r\n   *\r\n   * v {number|string|BigNumber} A numeric value.\r\n   * [b] {number} The base of v. Integer, 2 to ALPHABET.length inclusive.\r\n   */\r\n  function BigNumber(v, b) {\r\n    var alphabet, c, caseChanged, e, i, isNum, len, str,\r\n      x = this;\r\n\r\n    // Enable constructor call without `new`.\r\n    if (!(x instanceof BigNumber)) return new BigNumber(v, b);\r\n\r\n    if (b == null) {\r\n\r\n      if (v && v._isBigNumber === true) {\r\n        x.s = v.s;\r\n\r\n        if (!v.c || v.e > MAX_EXP) {\r\n          x.c = x.e = null;\r\n        } else if (v.e < MIN_EXP) {\r\n          x.c = [x.e = 0];\r\n        } else {\r\n          x.e = v.e;\r\n          x.c = v.c.slice();\r\n        }\r\n\r\n        return;\r\n      }\r\n\r\n      if ((isNum = typeof v == 'number') && v * 0 == 0) {\r\n\r\n        // Use `1 / n` to handle minus zero also.\r\n        x.s = 1 / v < 0 ? (v = -v, -1) : 1;\r\n\r\n        // Fast path for integers, where n < 2147483648 (2**31).\r\n        if (v === ~~v) {\r\n          for (e = 0, i = v; i >= 10; i /= 10, e++);\r\n\r\n          if (e > MAX_EXP) {\r\n            x.c = x.e = null;\r\n          } else {\r\n            x.e = e;\r\n            x.c = [v];\r\n          }\r\n\r\n          return;\r\n        }\r\n\r\n        str = String(v);\r\n      } else {\r\n\r\n        if (!isNumeric.test(str = String(v))) return parseNumeric(x, str, isNum);\r\n\r\n        x.s = str.charCodeAt(0) == 45 ? (str = str.slice(1), -1) : 1;\r\n      }\r\n\r\n      // Decimal point?\r\n      if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n      // Exponential form?\r\n      if ((i = str.search(/e/i)) > 0) {\r\n\r\n        // Determine exponent.\r\n        if (e < 0) e = i;\r\n        e += +str.slice(i + 1);\r\n        str = str.substring(0, i);\r\n      } else if (e < 0) {\r\n\r\n        // Integer.\r\n        e = str.length;\r\n      }\r\n\r\n    } else {\r\n\r\n      // '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n      intCheck(b, 2, ALPHABET.length, 'Base');\r\n\r\n      // Allow exponential notation to be used with base 10 argument, while\r\n      // also rounding to DECIMAL_PLACES as with other bases.\r\n      if (b == 10 && alphabetHasNormalDecimalDigits) {\r\n        x = new BigNumber(v);\r\n        return round(x, DECIMAL_PLACES + x.e + 1, ROUNDING_MODE);\r\n      }\r\n\r\n      str = String(v);\r\n\r\n      if (isNum = typeof v == 'number') {\r\n\r\n        // Avoid potential interpretation of Infinity and NaN as base 44+ values.\r\n        if (v * 0 != 0) return parseNumeric(x, str, isNum, b);\r\n\r\n        x.s = 1 / v < 0 ? (str = str.slice(1), -1) : 1;\r\n\r\n        // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\r\n        if (BigNumber.DEBUG && str.replace(/^0\\.0*|\\./, '').length > 15) {\r\n          throw Error\r\n           (tooManyDigits + v);\r\n        }\r\n      } else {\r\n        x.s = str.charCodeAt(0) === 45 ? (str = str.slice(1), -1) : 1;\r\n      }\r\n\r\n      alphabet = ALPHABET.slice(0, b);\r\n      e = i = 0;\r\n\r\n      // Check that str is a valid base b number.\r\n      // Don't use RegExp, so alphabet can contain special characters.\r\n      for (len = str.length; i < len; i++) {\r\n        if (alphabet.indexOf(c = str.charAt(i)) < 0) {\r\n          if (c == '.') {\r\n\r\n            // If '.' is not the first character and it has not be found before.\r\n            if (i > e) {\r\n              e = len;\r\n              continue;\r\n            }\r\n          } else if (!caseChanged) {\r\n\r\n            // Allow e.g. hexadecimal 'FF' as well as 'ff'.\r\n            if (str == str.toUpperCase() && (str = str.toLowerCase()) ||\r\n                str == str.toLowerCase() && (str = str.toUpperCase())) {\r\n              caseChanged = true;\r\n              i = -1;\r\n              e = 0;\r\n              continue;\r\n            }\r\n          }\r\n\r\n          return parseNumeric(x, String(v), isNum, b);\r\n        }\r\n      }\r\n\r\n      // Prevent later check for length on converted number.\r\n      isNum = false;\r\n      str = convertBase(str, b, 10, x.s);\r\n\r\n      // Decimal point?\r\n      if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n      else e = str.length;\r\n    }\r\n\r\n    // Determine leading zeros.\r\n    for (i = 0; str.charCodeAt(i) === 48; i++);\r\n\r\n    // Determine trailing zeros.\r\n    for (len = str.length; str.charCodeAt(--len) === 48;);\r\n\r\n    if (str = str.slice(i, ++len)) {\r\n      len -= i;\r\n\r\n      // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\r\n      if (isNum && BigNumber.DEBUG &&\r\n        len > 15 && (v > MAX_SAFE_INTEGER || v !== mathfloor(v))) {\r\n          throw Error\r\n           (tooManyDigits + (x.s * v));\r\n      }\r\n\r\n       // Overflow?\r\n      if ((e = e - i - 1) > MAX_EXP) {\r\n\r\n        // Infinity.\r\n        x.c = x.e = null;\r\n\r\n      // Underflow?\r\n      } else if (e < MIN_EXP) {\r\n\r\n        // Zero.\r\n        x.c = [x.e = 0];\r\n      } else {\r\n        x.e = e;\r\n        x.c = [];\r\n\r\n        // Transform base\r\n\r\n        // e is the base 10 exponent.\r\n        // i is where to slice str to get the first element of the coefficient array.\r\n        i = (e + 1) % LOG_BASE;\r\n        if (e < 0) i += LOG_BASE;  // i < 1\r\n\r\n        if (i < len) {\r\n          if (i) x.c.push(+str.slice(0, i));\r\n\r\n          for (len -= LOG_BASE; i < len;) {\r\n            x.c.push(+str.slice(i, i += LOG_BASE));\r\n          }\r\n\r\n          i = LOG_BASE - (str = str.slice(i)).length;\r\n        } else {\r\n          i -= len;\r\n        }\r\n\r\n        for (; i--; str += '0');\r\n        x.c.push(+str);\r\n      }\r\n    } else {\r\n\r\n      // Zero.\r\n      x.c = [x.e = 0];\r\n    }\r\n  }\r\n\r\n\r\n  // CONSTRUCTOR PROPERTIES\r\n\r\n\r\n  BigNumber.clone = clone;\r\n\r\n  BigNumber.ROUND_UP = 0;\r\n  BigNumber.ROUND_DOWN = 1;\r\n  BigNumber.ROUND_CEIL = 2;\r\n  BigNumber.ROUND_FLOOR = 3;\r\n  BigNumber.ROUND_HALF_UP = 4;\r\n  BigNumber.ROUND_HALF_DOWN = 5;\r\n  BigNumber.ROUND_HALF_EVEN = 6;\r\n  BigNumber.ROUND_HALF_CEIL = 7;\r\n  BigNumber.ROUND_HALF_FLOOR = 8;\r\n  BigNumber.EUCLID = 9;\r\n\r\n\r\n  /*\r\n   * Configure infrequently-changing library-wide settings.\r\n   *\r\n   * Accept an object with the following optional properties (if the value of a property is\r\n   * a number, it must be an integer within the inclusive range stated):\r\n   *\r\n   *   DECIMAL_PLACES   {number}           0 to MAX\r\n   *   ROUNDING_MODE    {number}           0 to 8\r\n   *   EXPONENTIAL_AT   {number|number[]}  -MAX to MAX  or  [-MAX to 0, 0 to MAX]\r\n   *   RANGE            {number|number[]}  -MAX to MAX (not zero)  or  [-MAX to -1, 1 to MAX]\r\n   *   CRYPTO           {boolean}          true or false\r\n   *   MODULO_MODE      {number}           0 to 9\r\n   *   POW_PRECISION       {number}           0 to MAX\r\n   *   ALPHABET         {string}           A string of two or more unique characters which does\r\n   *                                       not contain '.'.\r\n   *   FORMAT           {object}           An object with some of the following properties:\r\n   *     prefix                 {string}\r\n   *     groupSize              {number}\r\n   *     secondaryGroupSize     {number}\r\n   *     groupSeparator         {string}\r\n   *     decimalSeparator       {string}\r\n   *     fractionGroupSize      {number}\r\n   *     fractionGroupSeparator {string}\r\n   *     suffix                 {string}\r\n   *\r\n   * (The values assigned to the above FORMAT object properties are not checked for validity.)\r\n   *\r\n   * E.g.\r\n   * BigNumber.config({ DECIMAL_PLACES : 20, ROUNDING_MODE : 4 })\r\n   *\r\n   * Ignore properties/parameters set to null or undefined, except for ALPHABET.\r\n   *\r\n   * Return an object with the properties current values.\r\n   */\r\n  BigNumber.config = BigNumber.set = function (obj) {\r\n    var p, v;\r\n\r\n    if (obj != null) {\r\n\r\n      if (typeof obj == 'object') {\r\n\r\n        // DECIMAL_PLACES {number} Integer, 0 to MAX inclusive.\r\n        // '[BigNumber Error] DECIMAL_PLACES {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'DECIMAL_PLACES')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, MAX, p);\r\n          DECIMAL_PLACES = v;\r\n        }\r\n\r\n        // ROUNDING_MODE {number} Integer, 0 to 8 inclusive.\r\n        // '[BigNumber Error] ROUNDING_MODE {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'ROUNDING_MODE')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, 8, p);\r\n          ROUNDING_MODE = v;\r\n        }\r\n\r\n        // EXPONENTIAL_AT {number|number[]}\r\n        // Integer, -MAX to MAX inclusive or\r\n        // [integer -MAX to 0 inclusive, 0 to MAX inclusive].\r\n        // '[BigNumber Error] EXPONENTIAL_AT {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'EXPONENTIAL_AT')) {\r\n          v = obj[p];\r\n          if (v && v.pop) {\r\n            intCheck(v[0], -MAX, 0, p);\r\n            intCheck(v[1], 0, MAX, p);\r\n            TO_EXP_NEG = v[0];\r\n            TO_EXP_POS = v[1];\r\n          } else {\r\n            intCheck(v, -MAX, MAX, p);\r\n            TO_EXP_NEG = -(TO_EXP_POS = v < 0 ? -v : v);\r\n          }\r\n        }\r\n\r\n        // RANGE {number|number[]} Non-zero integer, -MAX to MAX inclusive or\r\n        // [integer -MAX to -1 inclusive, integer 1 to MAX inclusive].\r\n        // '[BigNumber Error] RANGE {not a primitive number|not an integer|out of range|cannot be zero}: {v}'\r\n        if (obj.hasOwnProperty(p = 'RANGE')) {\r\n          v = obj[p];\r\n          if (v && v.pop) {\r\n            intCheck(v[0], -MAX, -1, p);\r\n            intCheck(v[1], 1, MAX, p);\r\n            MIN_EXP = v[0];\r\n            MAX_EXP = v[1];\r\n          } else {\r\n            intCheck(v, -MAX, MAX, p);\r\n            if (v) {\r\n              MIN_EXP = -(MAX_EXP = v < 0 ? -v : v);\r\n            } else {\r\n              throw Error\r\n               (bignumberError + p + ' cannot be zero: ' + v);\r\n            }\r\n          }\r\n        }\r\n\r\n        // CRYPTO {boolean} true or false.\r\n        // '[BigNumber Error] CRYPTO not true or false: {v}'\r\n        // '[BigNumber Error] crypto unavailable'\r\n        if (obj.hasOwnProperty(p = 'CRYPTO')) {\r\n          v = obj[p];\r\n          if (v === !!v) {\r\n            if (v) {\r\n              if (typeof crypto != 'undefined' && crypto &&\r\n               (crypto.getRandomValues || crypto.randomBytes)) {\r\n                CRYPTO = v;\r\n              } else {\r\n                CRYPTO = !v;\r\n                throw Error\r\n                 (bignumberError + 'crypto unavailable');\r\n              }\r\n            } else {\r\n              CRYPTO = v;\r\n            }\r\n          } else {\r\n            throw Error\r\n             (bignumberError + p + ' not true or false: ' + v);\r\n          }\r\n        }\r\n\r\n        // MODULO_MODE {number} Integer, 0 to 9 inclusive.\r\n        // '[BigNumber Error] MODULO_MODE {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'MODULO_MODE')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, 9, p);\r\n          MODULO_MODE = v;\r\n        }\r\n\r\n        // POW_PRECISION {number} Integer, 0 to MAX inclusive.\r\n        // '[BigNumber Error] POW_PRECISION {not a primitive number|not an integer|out of range}: {v}'\r\n        if (obj.hasOwnProperty(p = 'POW_PRECISION')) {\r\n          v = obj[p];\r\n          intCheck(v, 0, MAX, p);\r\n          POW_PRECISION = v;\r\n        }\r\n\r\n        // FORMAT {object}\r\n        // '[BigNumber Error] FORMAT not an object: {v}'\r\n        if (obj.hasOwnProperty(p = 'FORMAT')) {\r\n          v = obj[p];\r\n          if (typeof v == 'object') FORMAT = v;\r\n          else throw Error\r\n           (bignumberError + p + ' not an object: ' + v);\r\n        }\r\n\r\n        // ALPHABET {string}\r\n        // '[BigNumber Error] ALPHABET invalid: {v}'\r\n        if (obj.hasOwnProperty(p = 'ALPHABET')) {\r\n          v = obj[p];\r\n\r\n          // Disallow if less than two characters,\r\n          // or if it contains '+', '-', '.', whitespace, or a repeated character.\r\n          if (typeof v == 'string' && !/^.?$|[+\\-.\\s]|(.).*\\1/.test(v)) {\r\n            alphabetHasNormalDecimalDigits = v.slice(0, 10) == '0123456789';\r\n            ALPHABET = v;\r\n          } else {\r\n            throw Error\r\n             (bignumberError + p + ' invalid: ' + v);\r\n          }\r\n        }\r\n\r\n      } else {\r\n\r\n        // '[BigNumber Error] Object expected: {v}'\r\n        throw Error\r\n         (bignumberError + 'Object expected: ' + obj);\r\n      }\r\n    }\r\n\r\n    return {\r\n      DECIMAL_PLACES: DECIMAL_PLACES,\r\n      ROUNDING_MODE: ROUNDING_MODE,\r\n      EXPONENTIAL_AT: [TO_EXP_NEG, TO_EXP_POS],\r\n      RANGE: [MIN_EXP, MAX_EXP],\r\n      CRYPTO: CRYPTO,\r\n      MODULO_MODE: MODULO_MODE,\r\n      POW_PRECISION: POW_PRECISION,\r\n      FORMAT: FORMAT,\r\n      ALPHABET: ALPHABET\r\n    };\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if v is a BigNumber instance, otherwise return false.\r\n   *\r\n   * If BigNumber.DEBUG is true, throw if a BigNumber instance is not well-formed.\r\n   *\r\n   * v {any}\r\n   *\r\n   * '[BigNumber Error] Invalid BigNumber: {v}'\r\n   */\r\n  BigNumber.isBigNumber = function (v) {\r\n    if (!v || v._isBigNumber !== true) return false;\r\n    if (!BigNumber.DEBUG) return true;\r\n\r\n    var i, n,\r\n      c = v.c,\r\n      e = v.e,\r\n      s = v.s;\r\n\r\n    out: if ({}.toString.call(c) == '[object Array]') {\r\n\r\n      if ((s === 1 || s === -1) && e >= -MAX && e <= MAX && e === mathfloor(e)) {\r\n\r\n        // If the first element is zero, the BigNumber value must be zero.\r\n        if (c[0] === 0) {\r\n          if (e === 0 && c.length === 1) return true;\r\n          break out;\r\n        }\r\n\r\n        // Calculate number of digits that c[0] should have, based on the exponent.\r\n        i = (e + 1) % LOG_BASE;\r\n        if (i < 1) i += LOG_BASE;\r\n\r\n        // Calculate number of digits of c[0].\r\n        //if (Math.ceil(Math.log(c[0] + 1) / Math.LN10) == i) {\r\n        if (String(c[0]).length == i) {\r\n\r\n          for (i = 0; i < c.length; i++) {\r\n            n = c[i];\r\n            if (n < 0 || n >= BASE || n !== mathfloor(n)) break out;\r\n          }\r\n\r\n          // Last element cannot be zero, unless it is the only element.\r\n          if (n !== 0) return true;\r\n        }\r\n      }\r\n\r\n    // Infinity/NaN\r\n    } else if (c === null && e === null && (s === null || s === 1 || s === -1)) {\r\n      return true;\r\n    }\r\n\r\n    throw Error\r\n      (bignumberError + 'Invalid BigNumber: ' + v);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the maximum of the arguments.\r\n   *\r\n   * arguments {number|string|BigNumber}\r\n   */\r\n  BigNumber.maximum = BigNumber.max = function () {\r\n    return maxOrMin(arguments, -1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the minimum of the arguments.\r\n   *\r\n   * arguments {number|string|BigNumber}\r\n   */\r\n  BigNumber.minimum = BigNumber.min = function () {\r\n    return maxOrMin(arguments, 1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber with a random value equal to or greater than 0 and less than 1,\r\n   * and with dp, or DECIMAL_PLACES if dp is omitted, decimal places (or less if trailing\r\n   * zeros are produced).\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp}'\r\n   * '[BigNumber Error] crypto unavailable'\r\n   */\r\n  BigNumber.random = (function () {\r\n    var pow2_53 = 0x20000000000000;\r\n\r\n    // Return a 53 bit integer n, where 0 <= n < 9007199254740992.\r\n    // Check if Math.random() produces more than 32 bits of randomness.\r\n    // If it does, assume at least 53 bits are produced, otherwise assume at least 30 bits.\r\n    // 0x40000000 is 2^30, 0x800000 is 2^23, 0x1fffff is 2^21 - 1.\r\n    var random53bitInt = (Math.random() * pow2_53) & 0x1fffff\r\n     ? function () { return mathfloor(Math.random() * pow2_53); }\r\n     : function () { return ((Math.random() * 0x40000000 | 0) * 0x800000) +\r\n       (Math.random() * 0x800000 | 0); };\r\n\r\n    return function (dp) {\r\n      var a, b, e, k, v,\r\n        i = 0,\r\n        c = [],\r\n        rand = new BigNumber(ONE);\r\n\r\n      if (dp == null) dp = DECIMAL_PLACES;\r\n      else intCheck(dp, 0, MAX);\r\n\r\n      k = mathceil(dp / LOG_BASE);\r\n\r\n      if (CRYPTO) {\r\n\r\n        // Browsers supporting crypto.getRandomValues.\r\n        if (crypto.getRandomValues) {\r\n\r\n          a = crypto.getRandomValues(new Uint32Array(k *= 2));\r\n\r\n          for (; i < k;) {\r\n\r\n            // 53 bits:\r\n            // ((Math.pow(2, 32) - 1) * Math.pow(2, 21)).toString(2)\r\n            // 11111 11111111 11111111 11111111 11100000 00000000 00000000\r\n            // ((Math.pow(2, 32) - 1) >>> 11).toString(2)\r\n            //                                     11111 11111111 11111111\r\n            // 0x20000 is 2^21.\r\n            v = a[i] * 0x20000 + (a[i + 1] >>> 11);\r\n\r\n            // Rejection sampling:\r\n            // 0 <= v < 9007199254740992\r\n            // Probability that v >= 9e15, is\r\n            // 7199254740992 / 9007199254740992 ~= 0.0008, i.e. 1 in 1251\r\n            if (v >= 9e15) {\r\n              b = crypto.getRandomValues(new Uint32Array(2));\r\n              a[i] = b[0];\r\n              a[i + 1] = b[1];\r\n            } else {\r\n\r\n              // 0 <= v <= 8999999999999999\r\n              // 0 <= (v % 1e14) <= 99999999999999\r\n              c.push(v % 1e14);\r\n              i += 2;\r\n            }\r\n          }\r\n          i = k / 2;\r\n\r\n        // Node.js supporting crypto.randomBytes.\r\n        } else if (crypto.randomBytes) {\r\n\r\n          // buffer\r\n          a = crypto.randomBytes(k *= 7);\r\n\r\n          for (; i < k;) {\r\n\r\n            // 0x1000000000000 is 2^48, 0x10000000000 is 2^40\r\n            // 0x100000000 is 2^32, 0x1000000 is 2^24\r\n            // 11111 11111111 11111111 11111111 11111111 11111111 11111111\r\n            // 0 <= v < 9007199254740992\r\n            v = ((a[i] & 31) * 0x1000000000000) + (a[i + 1] * 0x10000000000) +\r\n               (a[i + 2] * 0x100000000) + (a[i + 3] * 0x1000000) +\r\n               (a[i + 4] << 16) + (a[i + 5] << 8) + a[i + 6];\r\n\r\n            if (v >= 9e15) {\r\n              crypto.randomBytes(7).copy(a, i);\r\n            } else {\r\n\r\n              // 0 <= (v % 1e14) <= 99999999999999\r\n              c.push(v % 1e14);\r\n              i += 7;\r\n            }\r\n          }\r\n          i = k / 7;\r\n        } else {\r\n          CRYPTO = false;\r\n          throw Error\r\n           (bignumberError + 'crypto unavailable');\r\n        }\r\n      }\r\n\r\n      // Use Math.random.\r\n      if (!CRYPTO) {\r\n\r\n        for (; i < k;) {\r\n          v = random53bitInt();\r\n          if (v < 9e15) c[i++] = v % 1e14;\r\n        }\r\n      }\r\n\r\n      k = c[--i];\r\n      dp %= LOG_BASE;\r\n\r\n      // Convert trailing digits to zeros according to dp.\r\n      if (k && dp) {\r\n        v = POWS_TEN[LOG_BASE - dp];\r\n        c[i] = mathfloor(k / v) * v;\r\n      }\r\n\r\n      // Remove trailing elements which are zero.\r\n      for (; c[i] === 0; c.pop(), i--);\r\n\r\n      // Zero?\r\n      if (i < 0) {\r\n        c = [e = 0];\r\n      } else {\r\n\r\n        // Remove leading elements which are zero and adjust exponent accordingly.\r\n        for (e = -1 ; c[0] === 0; c.splice(0, 1), e -= LOG_BASE);\r\n\r\n        // Count the digits of the first element of c to determine leading zeros, and...\r\n        for (i = 1, v = c[0]; v >= 10; v /= 10, i++);\r\n\r\n        // adjust the exponent accordingly.\r\n        if (i < LOG_BASE) e -= LOG_BASE - i;\r\n      }\r\n\r\n      rand.e = e;\r\n      rand.c = c;\r\n      return rand;\r\n    };\r\n  })();\r\n\r\n\r\n   /*\r\n   * Return a BigNumber whose value is the sum of the arguments.\r\n   *\r\n   * arguments {number|string|BigNumber}\r\n   */\r\n  BigNumber.sum = function () {\r\n    var i = 1,\r\n      args = arguments,\r\n      sum = new BigNumber(args[0]);\r\n    for (; i < args.length;) sum = sum.plus(args[i++]);\r\n    return sum;\r\n  };\r\n\r\n\r\n  // PRIVATE FUNCTIONS\r\n\r\n\r\n  // Called by BigNumber and BigNumber.prototype.toString.\r\n  convertBase = (function () {\r\n    var decimal = '0123456789';\r\n\r\n    /*\r\n     * Convert string of baseIn to an array of numbers of baseOut.\r\n     * Eg. toBaseOut('255', 10, 16) returns [15, 15].\r\n     * Eg. toBaseOut('ff', 16, 10) returns [2, 5, 5].\r\n     */\r\n    function toBaseOut(str, baseIn, baseOut, alphabet) {\r\n      var j,\r\n        arr = [0],\r\n        arrL,\r\n        i = 0,\r\n        len = str.length;\r\n\r\n      for (; i < len;) {\r\n        for (arrL = arr.length; arrL--; arr[arrL] *= baseIn);\r\n\r\n        arr[0] += alphabet.indexOf(str.charAt(i++));\r\n\r\n        for (j = 0; j < arr.length; j++) {\r\n\r\n          if (arr[j] > baseOut - 1) {\r\n            if (arr[j + 1] == null) arr[j + 1] = 0;\r\n            arr[j + 1] += arr[j] / baseOut | 0;\r\n            arr[j] %= baseOut;\r\n          }\r\n        }\r\n      }\r\n\r\n      return arr.reverse();\r\n    }\r\n\r\n    // Convert a numeric string of baseIn to a numeric string of baseOut.\r\n    // If the caller is toString, we are converting from base 10 to baseOut.\r\n    // If the caller is BigNumber, we are converting from baseIn to base 10.\r\n    return function (str, baseIn, baseOut, sign, callerIsToString) {\r\n      var alphabet, d, e, k, r, x, xc, y,\r\n        i = str.indexOf('.'),\r\n        dp = DECIMAL_PLACES,\r\n        rm = ROUNDING_MODE;\r\n\r\n      // Non-integer.\r\n      if (i >= 0) {\r\n        k = POW_PRECISION;\r\n\r\n        // Unlimited precision.\r\n        POW_PRECISION = 0;\r\n        str = str.replace('.', '');\r\n        y = new BigNumber(baseIn);\r\n        x = y.pow(str.length - i);\r\n        POW_PRECISION = k;\r\n\r\n        // Convert str as if an integer, then restore the fraction part by dividing the\r\n        // result by its base raised to a power.\r\n\r\n        y.c = toBaseOut(toFixedPoint(coeffToString(x.c), x.e, '0'),\r\n         10, baseOut, decimal);\r\n        y.e = y.c.length;\r\n      }\r\n\r\n      // Convert the number as integer.\r\n\r\n      xc = toBaseOut(str, baseIn, baseOut, callerIsToString\r\n       ? (alphabet = ALPHABET, decimal)\r\n       : (alphabet = decimal, ALPHABET));\r\n\r\n      // xc now represents str as an integer and converted to baseOut. e is the exponent.\r\n      e = k = xc.length;\r\n\r\n      // Remove trailing zeros.\r\n      for (; xc[--k] == 0; xc.pop());\r\n\r\n      // Zero?\r\n      if (!xc[0]) return alphabet.charAt(0);\r\n\r\n      // Does str represent an integer? If so, no need for the division.\r\n      if (i < 0) {\r\n        --e;\r\n      } else {\r\n        x.c = xc;\r\n        x.e = e;\r\n\r\n        // The sign is needed for correct rounding.\r\n        x.s = sign;\r\n        x = div(x, y, dp, rm, baseOut);\r\n        xc = x.c;\r\n        r = x.r;\r\n        e = x.e;\r\n      }\r\n\r\n      // xc now represents str converted to baseOut.\r\n\r\n      // The index of the rounding digit.\r\n      d = e + dp + 1;\r\n\r\n      // The rounding digit: the digit to the right of the digit that may be rounded up.\r\n      i = xc[d];\r\n\r\n      // Look at the rounding digits and mode to determine whether to round up.\r\n\r\n      k = baseOut / 2;\r\n      r = r || d < 0 || xc[d + 1] != null;\r\n\r\n      r = rm < 4 ? (i != null || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n            : i > k || i == k &&(rm == 4 || r || rm == 6 && xc[d - 1] & 1 ||\r\n             rm == (x.s < 0 ? 8 : 7));\r\n\r\n      // If the index of the rounding digit is not greater than zero, or xc represents\r\n      // zero, then the result of the base conversion is zero or, if rounding up, a value\r\n      // such as 0.00001.\r\n      if (d < 1 || !xc[0]) {\r\n\r\n        // 1^-dp or 0\r\n        str = r ? toFixedPoint(alphabet.charAt(1), -dp, alphabet.charAt(0)) : alphabet.charAt(0);\r\n      } else {\r\n\r\n        // Truncate xc to the required number of decimal places.\r\n        xc.length = d;\r\n\r\n        // Round up?\r\n        if (r) {\r\n\r\n          // Rounding up may mean the previous digit has to be rounded up and so on.\r\n          for (--baseOut; ++xc[--d] > baseOut;) {\r\n            xc[d] = 0;\r\n\r\n            if (!d) {\r\n              ++e;\r\n              xc = [1].concat(xc);\r\n            }\r\n          }\r\n        }\r\n\r\n        // Determine trailing zeros.\r\n        for (k = xc.length; !xc[--k];);\r\n\r\n        // E.g. [4, 11, 15] becomes 4bf.\r\n        for (i = 0, str = ''; i <= k; str += alphabet.charAt(xc[i++]));\r\n\r\n        // Add leading zeros, decimal point and trailing zeros as required.\r\n        str = toFixedPoint(str, e, alphabet.charAt(0));\r\n      }\r\n\r\n      // The caller will add the sign.\r\n      return str;\r\n    };\r\n  })();\r\n\r\n\r\n  // Perform division in the specified base. Called by div and convertBase.\r\n  div = (function () {\r\n\r\n    // Assume non-zero x and k.\r\n    function multiply(x, k, base) {\r\n      var m, temp, xlo, xhi,\r\n        carry = 0,\r\n        i = x.length,\r\n        klo = k % SQRT_BASE,\r\n        khi = k / SQRT_BASE | 0;\r\n\r\n      for (x = x.slice(); i--;) {\r\n        xlo = x[i] % SQRT_BASE;\r\n        xhi = x[i] / SQRT_BASE | 0;\r\n        m = khi * xlo + xhi * klo;\r\n        temp = klo * xlo + ((m % SQRT_BASE) * SQRT_BASE) + carry;\r\n        carry = (temp / base | 0) + (m / SQRT_BASE | 0) + khi * xhi;\r\n        x[i] = temp % base;\r\n      }\r\n\r\n      if (carry) x = [carry].concat(x);\r\n\r\n      return x;\r\n    }\r\n\r\n    function compare(a, b, aL, bL) {\r\n      var i, cmp;\r\n\r\n      if (aL != bL) {\r\n        cmp = aL > bL ? 1 : -1;\r\n      } else {\r\n\r\n        for (i = cmp = 0; i < aL; i++) {\r\n\r\n          if (a[i] != b[i]) {\r\n            cmp = a[i] > b[i] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      return cmp;\r\n    }\r\n\r\n    function subtract(a, b, aL, base) {\r\n      var i = 0;\r\n\r\n      // Subtract b from a.\r\n      for (; aL--;) {\r\n        a[aL] -= i;\r\n        i = a[aL] < b[aL] ? 1 : 0;\r\n        a[aL] = i * base + a[aL] - b[aL];\r\n      }\r\n\r\n      // Remove leading zeros.\r\n      for (; !a[0] && a.length > 1; a.splice(0, 1));\r\n    }\r\n\r\n    // x: dividend, y: divisor.\r\n    return function (x, y, dp, rm, base) {\r\n      var cmp, e, i, more, n, prod, prodL, q, qc, rem, remL, rem0, xi, xL, yc0,\r\n        yL, yz,\r\n        s = x.s == y.s ? 1 : -1,\r\n        xc = x.c,\r\n        yc = y.c;\r\n\r\n      // Either NaN, Infinity or 0?\r\n      if (!xc || !xc[0] || !yc || !yc[0]) {\r\n\r\n        return new BigNumber(\r\n\r\n         // Return NaN if either NaN, or both Infinity or 0.\r\n         !x.s || !y.s || (xc ? yc && xc[0] == yc[0] : !yc) ? NaN :\r\n\r\n          // Return ±0 if x is ±0 or y is ±Infinity, or return ±Infinity as y is ±0.\r\n          xc && xc[0] == 0 || !yc ? s * 0 : s / 0\r\n       );\r\n      }\r\n\r\n      q = new BigNumber(s);\r\n      qc = q.c = [];\r\n      e = x.e - y.e;\r\n      s = dp + e + 1;\r\n\r\n      if (!base) {\r\n        base = BASE;\r\n        e = bitFloor(x.e / LOG_BASE) - bitFloor(y.e / LOG_BASE);\r\n        s = s / LOG_BASE | 0;\r\n      }\r\n\r\n      // Result exponent may be one less then the current value of e.\r\n      // The coefficients of the BigNumbers from convertBase may have trailing zeros.\r\n      for (i = 0; yc[i] == (xc[i] || 0); i++);\r\n\r\n      if (yc[i] > (xc[i] || 0)) e--;\r\n\r\n      if (s < 0) {\r\n        qc.push(1);\r\n        more = true;\r\n      } else {\r\n        xL = xc.length;\r\n        yL = yc.length;\r\n        i = 0;\r\n        s += 2;\r\n\r\n        // Normalise xc and yc so highest order digit of yc is >= base / 2.\r\n\r\n        n = mathfloor(base / (yc[0] + 1));\r\n\r\n        // Not necessary, but to handle odd bases where yc[0] == (base / 2) - 1.\r\n        // if (n > 1 || n++ == 1 && yc[0] < base / 2) {\r\n        if (n > 1) {\r\n          yc = multiply(yc, n, base);\r\n          xc = multiply(xc, n, base);\r\n          yL = yc.length;\r\n          xL = xc.length;\r\n        }\r\n\r\n        xi = yL;\r\n        rem = xc.slice(0, yL);\r\n        remL = rem.length;\r\n\r\n        // Add zeros to make remainder as long as divisor.\r\n        for (; remL < yL; rem[remL++] = 0);\r\n        yz = yc.slice();\r\n        yz = [0].concat(yz);\r\n        yc0 = yc[0];\r\n        if (yc[1] >= base / 2) yc0++;\r\n        // Not necessary, but to prevent trial digit n > base, when using base 3.\r\n        // else if (base == 3 && yc0 == 1) yc0 = 1 + 1e-15;\r\n\r\n        do {\r\n          n = 0;\r\n\r\n          // Compare divisor and remainder.\r\n          cmp = compare(yc, rem, yL, remL);\r\n\r\n          // If divisor < remainder.\r\n          if (cmp < 0) {\r\n\r\n            // Calculate trial digit, n.\r\n\r\n            rem0 = rem[0];\r\n            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\r\n\r\n            // n is how many times the divisor goes into the current remainder.\r\n            n = mathfloor(rem0 / yc0);\r\n\r\n            //  Algorithm:\r\n            //  product = divisor multiplied by trial digit (n).\r\n            //  Compare product and remainder.\r\n            //  If product is greater than remainder:\r\n            //    Subtract divisor from product, decrement trial digit.\r\n            //  Subtract product from remainder.\r\n            //  If product was less than remainder at the last compare:\r\n            //    Compare new remainder and divisor.\r\n            //    If remainder is greater than divisor:\r\n            //      Subtract divisor from remainder, increment trial digit.\r\n\r\n            if (n > 1) {\r\n\r\n              // n may be > base only when base is 3.\r\n              if (n >= base) n = base - 1;\r\n\r\n              // product = divisor * trial digit.\r\n              prod = multiply(yc, n, base);\r\n              prodL = prod.length;\r\n              remL = rem.length;\r\n\r\n              // Compare product and remainder.\r\n              // If product > remainder then trial digit n too high.\r\n              // n is 1 too high about 5% of the time, and is not known to have\r\n              // ever been more than 1 too high.\r\n              while (compare(prod, rem, prodL, remL) == 1) {\r\n                n--;\r\n\r\n                // Subtract divisor from product.\r\n                subtract(prod, yL < prodL ? yz : yc, prodL, base);\r\n                prodL = prod.length;\r\n                cmp = 1;\r\n              }\r\n            } else {\r\n\r\n              // n is 0 or 1, cmp is -1.\r\n              // If n is 0, there is no need to compare yc and rem again below,\r\n              // so change cmp to 1 to avoid it.\r\n              // If n is 1, leave cmp as -1, so yc and rem are compared again.\r\n              if (n == 0) {\r\n\r\n                // divisor < remainder, so n must be at least 1.\r\n                cmp = n = 1;\r\n              }\r\n\r\n              // product = divisor\r\n              prod = yc.slice();\r\n              prodL = prod.length;\r\n            }\r\n\r\n            if (prodL < remL) prod = [0].concat(prod);\r\n\r\n            // Subtract product from remainder.\r\n            subtract(rem, prod, remL, base);\r\n            remL = rem.length;\r\n\r\n             // If product was < remainder.\r\n            if (cmp == -1) {\r\n\r\n              // Compare divisor and new remainder.\r\n              // If divisor < new remainder, subtract divisor from remainder.\r\n              // Trial digit n too low.\r\n              // n is 1 too low about 5% of the time, and very rarely 2 too low.\r\n              while (compare(yc, rem, yL, remL) < 1) {\r\n                n++;\r\n\r\n                // Subtract divisor from remainder.\r\n                subtract(rem, yL < remL ? yz : yc, remL, base);\r\n                remL = rem.length;\r\n              }\r\n            }\r\n          } else if (cmp === 0) {\r\n            n++;\r\n            rem = [0];\r\n          } // else cmp === 1 and n will be 0\r\n\r\n          // Add the next digit, n, to the result array.\r\n          qc[i++] = n;\r\n\r\n          // Update the remainder.\r\n          if (rem[0]) {\r\n            rem[remL++] = xc[xi] || 0;\r\n          } else {\r\n            rem = [xc[xi]];\r\n            remL = 1;\r\n          }\r\n        } while ((xi++ < xL || rem[0] != null) && s--);\r\n\r\n        more = rem[0] != null;\r\n\r\n        // Leading zero?\r\n        if (!qc[0]) qc.splice(0, 1);\r\n      }\r\n\r\n      if (base == BASE) {\r\n\r\n        // To calculate q.e, first get the number of digits of qc[0].\r\n        for (i = 1, s = qc[0]; s >= 10; s /= 10, i++);\r\n\r\n        round(q, dp + (q.e = i + e * LOG_BASE - 1) + 1, rm, more);\r\n\r\n      // Caller is convertBase.\r\n      } else {\r\n        q.e = e;\r\n        q.r = +more;\r\n      }\r\n\r\n      return q;\r\n    };\r\n  })();\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of BigNumber n in fixed-point or exponential\r\n   * notation rounded to the specified decimal places or significant digits.\r\n   *\r\n   * n: a BigNumber.\r\n   * i: the index of the last digit required (i.e. the digit that may be rounded up).\r\n   * rm: the rounding mode.\r\n   * id: 1 (toExponential) or 2 (toPrecision).\r\n   */\r\n  function format(n, i, rm, id) {\r\n    var c0, e, ne, len, str;\r\n\r\n    if (rm == null) rm = ROUNDING_MODE;\r\n    else intCheck(rm, 0, 8);\r\n\r\n    if (!n.c) return n.toString();\r\n\r\n    c0 = n.c[0];\r\n    ne = n.e;\r\n\r\n    if (i == null) {\r\n      str = coeffToString(n.c);\r\n      str = id == 1 || id == 2 && (ne <= TO_EXP_NEG || ne >= TO_EXP_POS)\r\n       ? toExponential(str, ne)\r\n       : toFixedPoint(str, ne, '0');\r\n    } else {\r\n      n = round(new BigNumber(n), i, rm);\r\n\r\n      // n.e may have changed if the value was rounded up.\r\n      e = n.e;\r\n\r\n      str = coeffToString(n.c);\r\n      len = str.length;\r\n\r\n      // toPrecision returns exponential notation if the number of significant digits\r\n      // specified is less than the number of digits necessary to represent the integer\r\n      // part of the value in fixed-point notation.\r\n\r\n      // Exponential notation.\r\n      if (id == 1 || id == 2 && (i <= e || e <= TO_EXP_NEG)) {\r\n\r\n        // Append zeros?\r\n        for (; len < i; str += '0', len++);\r\n        str = toExponential(str, e);\r\n\r\n      // Fixed-point notation.\r\n      } else {\r\n        i -= ne;\r\n        str = toFixedPoint(str, e, '0');\r\n\r\n        // Append zeros?\r\n        if (e + 1 > len) {\r\n          if (--i > 0) for (str += '.'; i--; str += '0');\r\n        } else {\r\n          i += e - len;\r\n          if (i > 0) {\r\n            if (e + 1 == len) str += '.';\r\n            for (; i--; str += '0');\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return n.s < 0 && c0 ? '-' + str : str;\r\n  }\r\n\r\n\r\n  // Handle BigNumber.max and BigNumber.min.\r\n  // If any number is NaN, return NaN.\r\n  function maxOrMin(args, n) {\r\n    var k, y,\r\n      i = 1,\r\n      x = new BigNumber(args[0]);\r\n\r\n    for (; i < args.length; i++) {\r\n      y = new BigNumber(args[i]);\r\n      if (!y.s || (k = compare(x, y)) === n || k === 0 && x.s === n) {\r\n        x = y;\r\n      }\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  /*\r\n   * Strip trailing zeros, calculate base 10 exponent and check against MIN_EXP and MAX_EXP.\r\n   * Called by minus, plus and times.\r\n   */\r\n  function normalise(n, c, e) {\r\n    var i = 1,\r\n      j = c.length;\r\n\r\n     // Remove trailing zeros.\r\n    for (; !c[--j]; c.pop());\r\n\r\n    // Calculate the base 10 exponent. First get the number of digits of c[0].\r\n    for (j = c[0]; j >= 10; j /= 10, i++);\r\n\r\n    // Overflow?\r\n    if ((e = i + e * LOG_BASE - 1) > MAX_EXP) {\r\n\r\n      // Infinity.\r\n      n.c = n.e = null;\r\n\r\n    // Underflow?\r\n    } else if (e < MIN_EXP) {\r\n\r\n      // Zero.\r\n      n.c = [n.e = 0];\r\n    } else {\r\n      n.e = e;\r\n      n.c = c;\r\n    }\r\n\r\n    return n;\r\n  }\r\n\r\n\r\n  // Handle values that fail the validity test in BigNumber.\r\n  parseNumeric = (function () {\r\n    var basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i,\r\n      dotAfter = /^([^.]+)\\.$/,\r\n      dotBefore = /^\\.([^.]+)$/,\r\n      isInfinityOrNaN = /^-?(Infinity|NaN)$/,\r\n      whitespaceOrPlus = /^\\s*\\+(?=[\\w.])|^\\s+|\\s+$/g;\r\n\r\n    return function (x, str, isNum, b) {\r\n      var base,\r\n        s = isNum ? str : str.replace(whitespaceOrPlus, '');\r\n\r\n      // No exception on ±Infinity or NaN.\r\n      if (isInfinityOrNaN.test(s)) {\r\n        x.s = isNaN(s) ? null : s < 0 ? -1 : 1;\r\n      } else {\r\n        if (!isNum) {\r\n\r\n          // basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i\r\n          s = s.replace(basePrefix, function (m, p1, p2) {\r\n            base = (p2 = p2.toLowerCase()) == 'x' ? 16 : p2 == 'b' ? 2 : 8;\r\n            return !b || b == base ? p1 : m;\r\n          });\r\n\r\n          if (b) {\r\n            base = b;\r\n\r\n            // E.g. '1.' to '1', '.1' to '0.1'\r\n            s = s.replace(dotAfter, '$1').replace(dotBefore, '0.$1');\r\n          }\r\n\r\n          if (str != s) return new BigNumber(s, base);\r\n        }\r\n\r\n        // '[BigNumber Error] Not a number: {n}'\r\n        // '[BigNumber Error] Not a base {b} number: {n}'\r\n        if (BigNumber.DEBUG) {\r\n          throw Error\r\n            (bignumberError + 'Not a' + (b ? ' base ' + b : '') + ' number: ' + str);\r\n        }\r\n\r\n        // NaN\r\n        x.s = null;\r\n      }\r\n\r\n      x.c = x.e = null;\r\n    }\r\n  })();\r\n\r\n\r\n  /*\r\n   * Round x to sd significant digits using rounding mode rm. Check for over/under-flow.\r\n   * If r is truthy, it is known that there are more digits after the rounding digit.\r\n   */\r\n  function round(x, sd, rm, r) {\r\n    var d, i, j, k, n, ni, rd,\r\n      xc = x.c,\r\n      pows10 = POWS_TEN;\r\n\r\n    // if x is not Infinity or NaN...\r\n    if (xc) {\r\n\r\n      // rd is the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n      // n is a base 1e14 number, the value of the element of array x.c containing rd.\r\n      // ni is the index of n within x.c.\r\n      // d is the number of digits of n.\r\n      // i is the index of rd within n including leading zeros.\r\n      // j is the actual index of rd within n (if < 0, rd is a leading zero).\r\n      out: {\r\n\r\n        // Get the number of digits of the first element of xc.\r\n        for (d = 1, k = xc[0]; k >= 10; k /= 10, d++);\r\n        i = sd - d;\r\n\r\n        // If the rounding digit is in the first element of xc...\r\n        if (i < 0) {\r\n          i += LOG_BASE;\r\n          j = sd;\r\n          n = xc[ni = 0];\r\n\r\n          // Get the rounding digit at index j of n.\r\n          rd = mathfloor(n / pows10[d - j - 1] % 10);\r\n        } else {\r\n          ni = mathceil((i + 1) / LOG_BASE);\r\n\r\n          if (ni >= xc.length) {\r\n\r\n            if (r) {\r\n\r\n              // Needed by sqrt.\r\n              for (; xc.length <= ni; xc.push(0));\r\n              n = rd = 0;\r\n              d = 1;\r\n              i %= LOG_BASE;\r\n              j = i - LOG_BASE + 1;\r\n            } else {\r\n              break out;\r\n            }\r\n          } else {\r\n            n = k = xc[ni];\r\n\r\n            // Get the number of digits of n.\r\n            for (d = 1; k >= 10; k /= 10, d++);\r\n\r\n            // Get the index of rd within n.\r\n            i %= LOG_BASE;\r\n\r\n            // Get the index of rd within n, adjusted for leading zeros.\r\n            // The number of leading zeros of n is given by LOG_BASE - d.\r\n            j = i - LOG_BASE + d;\r\n\r\n            // Get the rounding digit at index j of n.\r\n            rd = j < 0 ? 0 : mathfloor(n / pows10[d - j - 1] % 10);\r\n          }\r\n        }\r\n\r\n        r = r || sd < 0 ||\r\n\r\n        // Are there any non-zero digits after the rounding digit?\r\n        // The expression  n % pows10[d - j - 1]  returns all digits of n to the right\r\n        // of the digit at j, e.g. if n is 908714 and j is 2, the expression gives 714.\r\n         xc[ni + 1] != null || (j < 0 ? n : n % pows10[d - j - 1]);\r\n\r\n        r = rm < 4\r\n         ? (rd || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n         : rd > 5 || rd == 5 && (rm == 4 || r || rm == 6 &&\r\n\r\n          // Check whether the digit to the left of the rounding digit is odd.\r\n          ((i > 0 ? j > 0 ? n / pows10[d - j] : 0 : xc[ni - 1]) % 10) & 1 ||\r\n           rm == (x.s < 0 ? 8 : 7));\r\n\r\n        if (sd < 1 || !xc[0]) {\r\n          xc.length = 0;\r\n\r\n          if (r) {\r\n\r\n            // Convert sd to decimal places.\r\n            sd -= x.e + 1;\r\n\r\n            // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n            xc[0] = pows10[(LOG_BASE - sd % LOG_BASE) % LOG_BASE];\r\n            x.e = -sd || 0;\r\n          } else {\r\n\r\n            // Zero.\r\n            xc[0] = x.e = 0;\r\n          }\r\n\r\n          return x;\r\n        }\r\n\r\n        // Remove excess digits.\r\n        if (i == 0) {\r\n          xc.length = ni;\r\n          k = 1;\r\n          ni--;\r\n        } else {\r\n          xc.length = ni + 1;\r\n          k = pows10[LOG_BASE - i];\r\n\r\n          // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n          // j > 0 means i > number of leading zeros of n.\r\n          xc[ni] = j > 0 ? mathfloor(n / pows10[d - j] % pows10[j]) * k : 0;\r\n        }\r\n\r\n        // Round up?\r\n        if (r) {\r\n\r\n          for (; ;) {\r\n\r\n            // If the digit to be rounded up is in the first element of xc...\r\n            if (ni == 0) {\r\n\r\n              // i will be the length of xc[0] before k is added.\r\n              for (i = 1, j = xc[0]; j >= 10; j /= 10, i++);\r\n              j = xc[0] += k;\r\n              for (k = 1; j >= 10; j /= 10, k++);\r\n\r\n              // if i != k the length has increased.\r\n              if (i != k) {\r\n                x.e++;\r\n                if (xc[0] == BASE) xc[0] = 1;\r\n              }\r\n\r\n              break;\r\n            } else {\r\n              xc[ni] += k;\r\n              if (xc[ni] != BASE) break;\r\n              xc[ni--] = 0;\r\n              k = 1;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Remove trailing zeros.\r\n        for (i = xc.length; xc[--i] === 0; xc.pop());\r\n      }\r\n\r\n      // Overflow? Infinity.\r\n      if (x.e > MAX_EXP) {\r\n        x.c = x.e = null;\r\n\r\n      // Underflow? Zero.\r\n      } else if (x.e < MIN_EXP) {\r\n        x.c = [x.e = 0];\r\n      }\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  function valueOf(n) {\r\n    var str,\r\n      e = n.e;\r\n\r\n    if (e === null) return n.toString();\r\n\r\n    str = coeffToString(n.c);\r\n\r\n    str = e <= TO_EXP_NEG || e >= TO_EXP_POS\r\n      ? toExponential(str, e)\r\n      : toFixedPoint(str, e, '0');\r\n\r\n    return n.s < 0 ? '-' + str : str;\r\n  }\r\n\r\n\r\n  // PROTOTYPE/INSTANCE METHODS\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the absolute value of this BigNumber.\r\n   */\r\n  P.absoluteValue = P.abs = function () {\r\n    var x = new BigNumber(this);\r\n    if (x.s < 0) x.s = 1;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return\r\n   *   1 if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n   *   -1 if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n   *   0 if they have the same value,\r\n   *   or null if the value of either is NaN.\r\n   */\r\n  P.comparedTo = function (y, b) {\r\n    return compare(this, new BigNumber(y, b));\r\n  };\r\n\r\n\r\n  /*\r\n   * If dp is undefined or null or true or false, return the number of decimal places of the\r\n   * value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n   *\r\n   * Otherwise, if dp is a number, return a new BigNumber whose value is the value of this\r\n   * BigNumber rounded to a maximum of dp decimal places using rounding mode rm, or\r\n   * ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * [dp] {number} Decimal places: integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   */\r\n  P.decimalPlaces = P.dp = function (dp, rm) {\r\n    var c, n, v,\r\n      x = this;\r\n\r\n    if (dp != null) {\r\n      intCheck(dp, 0, MAX);\r\n      if (rm == null) rm = ROUNDING_MODE;\r\n      else intCheck(rm, 0, 8);\r\n\r\n      return round(new BigNumber(x), dp + x.e + 1, rm);\r\n    }\r\n\r\n    if (!(c = x.c)) return null;\r\n    n = ((v = c.length - 1) - bitFloor(this.e / LOG_BASE)) * LOG_BASE;\r\n\r\n    // Subtract the number of trailing zeros of the last number.\r\n    if (v = c[v]) for (; v % 10 == 0; v /= 10, n--);\r\n    if (n < 0) n = 0;\r\n\r\n    return n;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n / 0 = I\r\n   *  n / N = N\r\n   *  n / I = 0\r\n   *  0 / n = 0\r\n   *  0 / 0 = N\r\n   *  0 / N = N\r\n   *  0 / I = 0\r\n   *  N / n = N\r\n   *  N / 0 = N\r\n   *  N / N = N\r\n   *  N / I = N\r\n   *  I / n = I\r\n   *  I / 0 = I\r\n   *  I / N = N\r\n   *  I / I = N\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber divided by the value of\r\n   * BigNumber(y, b), rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n   */\r\n  P.dividedBy = P.div = function (y, b) {\r\n    return div(this, new BigNumber(y, b), DECIMAL_PLACES, ROUNDING_MODE);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the integer part of dividing the value of this\r\n   * BigNumber by the value of BigNumber(y, b).\r\n   */\r\n  P.dividedToIntegerBy = P.idiv = function (y, b) {\r\n    return div(this, new BigNumber(y, b), 0, 1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a BigNumber whose value is the value of this BigNumber exponentiated by n.\r\n   *\r\n   * If m is present, return the result modulo m.\r\n   * If n is negative round according to DECIMAL_PLACES and ROUNDING_MODE.\r\n   * If POW_PRECISION is non-zero and m is not present, round to POW_PRECISION using ROUNDING_MODE.\r\n   *\r\n   * The modular power operation works efficiently when x, n, and m are integers, otherwise it\r\n   * is equivalent to calculating x.exponentiatedBy(n).modulo(m) with a POW_PRECISION of 0.\r\n   *\r\n   * n {number|string|BigNumber} The exponent. An integer.\r\n   * [m] {number|string|BigNumber} The modulus.\r\n   *\r\n   * '[BigNumber Error] Exponent not an integer: {n}'\r\n   */\r\n  P.exponentiatedBy = P.pow = function (n, m) {\r\n    var half, isModExp, i, k, more, nIsBig, nIsNeg, nIsOdd, y,\r\n      x = this;\r\n\r\n    n = new BigNumber(n);\r\n\r\n    // Allow NaN and ±Infinity, but not other non-integers.\r\n    if (n.c && !n.isInteger()) {\r\n      throw Error\r\n        (bignumberError + 'Exponent not an integer: ' + valueOf(n));\r\n    }\r\n\r\n    if (m != null) m = new BigNumber(m);\r\n\r\n    // Exponent of MAX_SAFE_INTEGER is 15.\r\n    nIsBig = n.e > 14;\r\n\r\n    // If x is NaN, ±Infinity, ±0 or ±1, or n is ±Infinity, NaN or ±0.\r\n    if (!x.c || !x.c[0] || x.c[0] == 1 && !x.e && x.c.length == 1 || !n.c || !n.c[0]) {\r\n\r\n      // The sign of the result of pow when x is negative depends on the evenness of n.\r\n      // If +n overflows to ±Infinity, the evenness of n would be not be known.\r\n      y = new BigNumber(Math.pow(+valueOf(x), nIsBig ? n.s * (2 - isOdd(n)) : +valueOf(n)));\r\n      return m ? y.mod(m) : y;\r\n    }\r\n\r\n    nIsNeg = n.s < 0;\r\n\r\n    if (m) {\r\n\r\n      // x % m returns NaN if abs(m) is zero, or m is NaN.\r\n      if (m.c ? !m.c[0] : !m.s) return new BigNumber(NaN);\r\n\r\n      isModExp = !nIsNeg && x.isInteger() && m.isInteger();\r\n\r\n      if (isModExp) x = x.mod(m);\r\n\r\n    // Overflow to ±Infinity: >=2**1e10 or >=1.0000024**1e15.\r\n    // Underflow to ±0: <=0.79**1e10 or <=0.9999975**1e15.\r\n    } else if (n.e > 9 && (x.e > 0 || x.e < -1 || (x.e == 0\r\n      // [1, 240000000]\r\n      ? x.c[0] > 1 || nIsBig && x.c[1] >= 24e7\r\n      // [80000000000000]  [99999750000000]\r\n      : x.c[0] < 8e13 || nIsBig && x.c[0] <= 9999975e7))) {\r\n\r\n      // If x is negative and n is odd, k = -0, else k = 0.\r\n      k = x.s < 0 && isOdd(n) ? -0 : 0;\r\n\r\n      // If x >= 1, k = ±Infinity.\r\n      if (x.e > -1) k = 1 / k;\r\n\r\n      // If n is negative return ±0, else return ±Infinity.\r\n      return new BigNumber(nIsNeg ? 1 / k : k);\r\n\r\n    } else if (POW_PRECISION) {\r\n\r\n      // Truncating each coefficient array to a length of k after each multiplication\r\n      // equates to truncating significant digits to POW_PRECISION + [28, 41],\r\n      // i.e. there will be a minimum of 28 guard digits retained.\r\n      k = mathceil(POW_PRECISION / LOG_BASE + 2);\r\n    }\r\n\r\n    if (nIsBig) {\r\n      half = new BigNumber(0.5);\r\n      if (nIsNeg) n.s = 1;\r\n      nIsOdd = isOdd(n);\r\n    } else {\r\n      i = Math.abs(+valueOf(n));\r\n      nIsOdd = i % 2;\r\n    }\r\n\r\n    y = new BigNumber(ONE);\r\n\r\n    // Performs 54 loop iterations for n of 9007199254740991.\r\n    for (; ;) {\r\n\r\n      if (nIsOdd) {\r\n        y = y.times(x);\r\n        if (!y.c) break;\r\n\r\n        if (k) {\r\n          if (y.c.length > k) y.c.length = k;\r\n        } else if (isModExp) {\r\n          y = y.mod(m);    //y = y.minus(div(y, m, 0, MODULO_MODE).times(m));\r\n        }\r\n      }\r\n\r\n      if (i) {\r\n        i = mathfloor(i / 2);\r\n        if (i === 0) break;\r\n        nIsOdd = i % 2;\r\n      } else {\r\n        n = n.times(half);\r\n        round(n, n.e + 1, 1);\r\n\r\n        if (n.e > 14) {\r\n          nIsOdd = isOdd(n);\r\n        } else {\r\n          i = +valueOf(n);\r\n          if (i === 0) break;\r\n          nIsOdd = i % 2;\r\n        }\r\n      }\r\n\r\n      x = x.times(x);\r\n\r\n      if (k) {\r\n        if (x.c && x.c.length > k) x.c.length = k;\r\n      } else if (isModExp) {\r\n        x = x.mod(m);    //x = x.minus(div(x, m, 0, MODULO_MODE).times(m));\r\n      }\r\n    }\r\n\r\n    if (isModExp) return y;\r\n    if (nIsNeg) y = ONE.div(y);\r\n\r\n    return m ? y.mod(m) : k ? round(y, POW_PRECISION, ROUNDING_MODE, more) : y;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the value of this BigNumber rounded to an integer\r\n   * using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {rm}'\r\n   */\r\n  P.integerValue = function (rm) {\r\n    var n = new BigNumber(this);\r\n    if (rm == null) rm = ROUNDING_MODE;\r\n    else intCheck(rm, 0, 8);\r\n    return round(n, n.e + 1, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is equal to the value of BigNumber(y, b),\r\n   * otherwise return false.\r\n   */\r\n  P.isEqualTo = P.eq = function (y, b) {\r\n    return compare(this, new BigNumber(y, b)) === 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is a finite number, otherwise return false.\r\n   */\r\n  P.isFinite = function () {\r\n    return !!this.c;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n   * otherwise return false.\r\n   */\r\n  P.isGreaterThan = P.gt = function (y, b) {\r\n    return compare(this, new BigNumber(y, b)) > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is greater than or equal to the value of\r\n   * BigNumber(y, b), otherwise return false.\r\n   */\r\n  P.isGreaterThanOrEqualTo = P.gte = function (y, b) {\r\n    return (b = compare(this, new BigNumber(y, b))) === 1 || b === 0;\r\n\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is an integer, otherwise return false.\r\n   */\r\n  P.isInteger = function () {\r\n    return !!this.c && bitFloor(this.e / LOG_BASE) > this.c.length - 2;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n   * otherwise return false.\r\n   */\r\n  P.isLessThan = P.lt = function (y, b) {\r\n    return compare(this, new BigNumber(y, b)) < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is less than or equal to the value of\r\n   * BigNumber(y, b), otherwise return false.\r\n   */\r\n  P.isLessThanOrEqualTo = P.lte = function (y, b) {\r\n    return (b = compare(this, new BigNumber(y, b))) === -1 || b === 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is NaN, otherwise return false.\r\n   */\r\n  P.isNaN = function () {\r\n    return !this.s;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is negative, otherwise return false.\r\n   */\r\n  P.isNegative = function () {\r\n    return this.s < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is positive, otherwise return false.\r\n   */\r\n  P.isPositive = function () {\r\n    return this.s > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this BigNumber is 0 or -0, otherwise return false.\r\n   */\r\n  P.isZero = function () {\r\n    return !!this.c && this.c[0] == 0;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n - 0 = n\r\n   *  n - N = N\r\n   *  n - I = -I\r\n   *  0 - n = -n\r\n   *  0 - 0 = 0\r\n   *  0 - N = N\r\n   *  0 - I = -I\r\n   *  N - n = N\r\n   *  N - 0 = N\r\n   *  N - N = N\r\n   *  N - I = N\r\n   *  I - n = I\r\n   *  I - 0 = I\r\n   *  I - N = N\r\n   *  I - I = N\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber minus the value of\r\n   * BigNumber(y, b).\r\n   */\r\n  P.minus = function (y, b) {\r\n    var i, j, t, xLTy,\r\n      x = this,\r\n      a = x.s;\r\n\r\n    y = new BigNumber(y, b);\r\n    b = y.s;\r\n\r\n    // Either NaN?\r\n    if (!a || !b) return new BigNumber(NaN);\r\n\r\n    // Signs differ?\r\n    if (a != b) {\r\n      y.s = -b;\r\n      return x.plus(y);\r\n    }\r\n\r\n    var xe = x.e / LOG_BASE,\r\n      ye = y.e / LOG_BASE,\r\n      xc = x.c,\r\n      yc = y.c;\r\n\r\n    if (!xe || !ye) {\r\n\r\n      // Either Infinity?\r\n      if (!xc || !yc) return xc ? (y.s = -b, y) : new BigNumber(yc ? x : NaN);\r\n\r\n      // Either zero?\r\n      if (!xc[0] || !yc[0]) {\r\n\r\n        // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\r\n        return yc[0] ? (y.s = -b, y) : new BigNumber(xc[0] ? x :\r\n\r\n         // IEEE 754 (2008) 6.3: n - n = -0 when rounding to -Infinity\r\n         ROUNDING_MODE == 3 ? -0 : 0);\r\n      }\r\n    }\r\n\r\n    xe = bitFloor(xe);\r\n    ye = bitFloor(ye);\r\n    xc = xc.slice();\r\n\r\n    // Determine which is the bigger number.\r\n    if (a = xe - ye) {\r\n\r\n      if (xLTy = a < 0) {\r\n        a = -a;\r\n        t = xc;\r\n      } else {\r\n        ye = xe;\r\n        t = yc;\r\n      }\r\n\r\n      t.reverse();\r\n\r\n      // Prepend zeros to equalise exponents.\r\n      for (b = a; b--; t.push(0));\r\n      t.reverse();\r\n    } else {\r\n\r\n      // Exponents equal. Check digit by digit.\r\n      j = (xLTy = (a = xc.length) < (b = yc.length)) ? a : b;\r\n\r\n      for (a = b = 0; b < j; b++) {\r\n\r\n        if (xc[b] != yc[b]) {\r\n          xLTy = xc[b] < yc[b];\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    // x < y? Point xc to the array of the bigger number.\r\n    if (xLTy) {\r\n      t = xc;\r\n      xc = yc;\r\n      yc = t;\r\n      y.s = -y.s;\r\n    }\r\n\r\n    b = (j = yc.length) - (i = xc.length);\r\n\r\n    // Append zeros to xc if shorter.\r\n    // No need to add zeros to yc if shorter as subtract only needs to start at yc.length.\r\n    if (b > 0) for (; b--; xc[i++] = 0);\r\n    b = BASE - 1;\r\n\r\n    // Subtract yc from xc.\r\n    for (; j > a;) {\r\n\r\n      if (xc[--j] < yc[j]) {\r\n        for (i = j; i && !xc[--i]; xc[i] = b);\r\n        --xc[i];\r\n        xc[j] += BASE;\r\n      }\r\n\r\n      xc[j] -= yc[j];\r\n    }\r\n\r\n    // Remove leading zeros and adjust exponent accordingly.\r\n    for (; xc[0] == 0; xc.splice(0, 1), --ye);\r\n\r\n    // Zero?\r\n    if (!xc[0]) {\r\n\r\n      // Following IEEE 754 (2008) 6.3,\r\n      // n - n = +0  but  n - n = -0  when rounding towards -Infinity.\r\n      y.s = ROUNDING_MODE == 3 ? -1 : 1;\r\n      y.c = [y.e = 0];\r\n      return y;\r\n    }\r\n\r\n    // No need to check for Infinity as +x - +y != Infinity && -x - -y != Infinity\r\n    // for finite x and y.\r\n    return normalise(y, xc, ye);\r\n  };\r\n\r\n\r\n  /*\r\n   *   n % 0 =  N\r\n   *   n % N =  N\r\n   *   n % I =  n\r\n   *   0 % n =  0\r\n   *  -0 % n = -0\r\n   *   0 % 0 =  N\r\n   *   0 % N =  N\r\n   *   0 % I =  0\r\n   *   N % n =  N\r\n   *   N % 0 =  N\r\n   *   N % N =  N\r\n   *   N % I =  N\r\n   *   I % n =  N\r\n   *   I % 0 =  N\r\n   *   I % N =  N\r\n   *   I % I =  N\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber modulo the value of\r\n   * BigNumber(y, b). The result depends on the value of MODULO_MODE.\r\n   */\r\n  P.modulo = P.mod = function (y, b) {\r\n    var q, s,\r\n      x = this;\r\n\r\n    y = new BigNumber(y, b);\r\n\r\n    // Return NaN if x is Infinity or NaN, or y is NaN or zero.\r\n    if (!x.c || !y.s || y.c && !y.c[0]) {\r\n      return new BigNumber(NaN);\r\n\r\n    // Return x if y is Infinity or x is zero.\r\n    } else if (!y.c || x.c && !x.c[0]) {\r\n      return new BigNumber(x);\r\n    }\r\n\r\n    if (MODULO_MODE == 9) {\r\n\r\n      // Euclidian division: q = sign(y) * floor(x / abs(y))\r\n      // r = x - qy    where  0 <= r < abs(y)\r\n      s = y.s;\r\n      y.s = 1;\r\n      q = div(x, y, 0, 3);\r\n      y.s = s;\r\n      q.s *= s;\r\n    } else {\r\n      q = div(x, y, 0, MODULO_MODE);\r\n    }\r\n\r\n    y = x.minus(q.times(y));\r\n\r\n    // To match JavaScript %, ensure sign of zero is sign of dividend.\r\n    if (!y.c[0] && MODULO_MODE == 1) y.s = x.s;\r\n\r\n    return y;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n * 0 = 0\r\n   *  n * N = N\r\n   *  n * I = I\r\n   *  0 * n = 0\r\n   *  0 * 0 = 0\r\n   *  0 * N = N\r\n   *  0 * I = N\r\n   *  N * n = N\r\n   *  N * 0 = N\r\n   *  N * N = N\r\n   *  N * I = N\r\n   *  I * n = I\r\n   *  I * 0 = N\r\n   *  I * N = N\r\n   *  I * I = I\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber multiplied by the value\r\n   * of BigNumber(y, b).\r\n   */\r\n  P.multipliedBy = P.times = function (y, b) {\r\n    var c, e, i, j, k, m, xcL, xlo, xhi, ycL, ylo, yhi, zc,\r\n      base, sqrtBase,\r\n      x = this,\r\n      xc = x.c,\r\n      yc = (y = new BigNumber(y, b)).c;\r\n\r\n    // Either NaN, ±Infinity or ±0?\r\n    if (!xc || !yc || !xc[0] || !yc[0]) {\r\n\r\n      // Return NaN if either is NaN, or one is 0 and the other is Infinity.\r\n      if (!x.s || !y.s || xc && !xc[0] && !yc || yc && !yc[0] && !xc) {\r\n        y.c = y.e = y.s = null;\r\n      } else {\r\n        y.s *= x.s;\r\n\r\n        // Return ±Infinity if either is ±Infinity.\r\n        if (!xc || !yc) {\r\n          y.c = y.e = null;\r\n\r\n        // Return ±0 if either is ±0.\r\n        } else {\r\n          y.c = [0];\r\n          y.e = 0;\r\n        }\r\n      }\r\n\r\n      return y;\r\n    }\r\n\r\n    e = bitFloor(x.e / LOG_BASE) + bitFloor(y.e / LOG_BASE);\r\n    y.s *= x.s;\r\n    xcL = xc.length;\r\n    ycL = yc.length;\r\n\r\n    // Ensure xc points to longer array and xcL to its length.\r\n    if (xcL < ycL) {\r\n      zc = xc;\r\n      xc = yc;\r\n      yc = zc;\r\n      i = xcL;\r\n      xcL = ycL;\r\n      ycL = i;\r\n    }\r\n\r\n    // Initialise the result array with zeros.\r\n    for (i = xcL + ycL, zc = []; i--; zc.push(0));\r\n\r\n    base = BASE;\r\n    sqrtBase = SQRT_BASE;\r\n\r\n    for (i = ycL; --i >= 0;) {\r\n      c = 0;\r\n      ylo = yc[i] % sqrtBase;\r\n      yhi = yc[i] / sqrtBase | 0;\r\n\r\n      for (k = xcL, j = i + k; j > i;) {\r\n        xlo = xc[--k] % sqrtBase;\r\n        xhi = xc[k] / sqrtBase | 0;\r\n        m = yhi * xlo + xhi * ylo;\r\n        xlo = ylo * xlo + ((m % sqrtBase) * sqrtBase) + zc[j] + c;\r\n        c = (xlo / base | 0) + (m / sqrtBase | 0) + yhi * xhi;\r\n        zc[j--] = xlo % base;\r\n      }\r\n\r\n      zc[j] = c;\r\n    }\r\n\r\n    if (c) {\r\n      ++e;\r\n    } else {\r\n      zc.splice(0, 1);\r\n    }\r\n\r\n    return normalise(y, zc, e);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the value of this BigNumber negated,\r\n   * i.e. multiplied by -1.\r\n   */\r\n  P.negated = function () {\r\n    var x = new BigNumber(this);\r\n    x.s = -x.s || null;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   *  n + 0 = n\r\n   *  n + N = N\r\n   *  n + I = I\r\n   *  0 + n = n\r\n   *  0 + 0 = 0\r\n   *  0 + N = N\r\n   *  0 + I = I\r\n   *  N + n = N\r\n   *  N + 0 = N\r\n   *  N + N = N\r\n   *  N + I = N\r\n   *  I + n = I\r\n   *  I + 0 = I\r\n   *  I + N = N\r\n   *  I + I = I\r\n   *\r\n   * Return a new BigNumber whose value is the value of this BigNumber plus the value of\r\n   * BigNumber(y, b).\r\n   */\r\n  P.plus = function (y, b) {\r\n    var t,\r\n      x = this,\r\n      a = x.s;\r\n\r\n    y = new BigNumber(y, b);\r\n    b = y.s;\r\n\r\n    // Either NaN?\r\n    if (!a || !b) return new BigNumber(NaN);\r\n\r\n    // Signs differ?\r\n     if (a != b) {\r\n      y.s = -b;\r\n      return x.minus(y);\r\n    }\r\n\r\n    var xe = x.e / LOG_BASE,\r\n      ye = y.e / LOG_BASE,\r\n      xc = x.c,\r\n      yc = y.c;\r\n\r\n    if (!xe || !ye) {\r\n\r\n      // Return ±Infinity if either ±Infinity.\r\n      if (!xc || !yc) return new BigNumber(a / 0);\r\n\r\n      // Either zero?\r\n      // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\r\n      if (!xc[0] || !yc[0]) return yc[0] ? y : new BigNumber(xc[0] ? x : a * 0);\r\n    }\r\n\r\n    xe = bitFloor(xe);\r\n    ye = bitFloor(ye);\r\n    xc = xc.slice();\r\n\r\n    // Prepend zeros to equalise exponents. Faster to use reverse then do unshifts.\r\n    if (a = xe - ye) {\r\n      if (a > 0) {\r\n        ye = xe;\r\n        t = yc;\r\n      } else {\r\n        a = -a;\r\n        t = xc;\r\n      }\r\n\r\n      t.reverse();\r\n      for (; a--; t.push(0));\r\n      t.reverse();\r\n    }\r\n\r\n    a = xc.length;\r\n    b = yc.length;\r\n\r\n    // Point xc to the longer array, and b to the shorter length.\r\n    if (a - b < 0) {\r\n      t = yc;\r\n      yc = xc;\r\n      xc = t;\r\n      b = a;\r\n    }\r\n\r\n    // Only start adding at yc.length - 1 as the further digits of xc can be ignored.\r\n    for (a = 0; b;) {\r\n      a = (xc[--b] = xc[b] + yc[b] + a) / BASE | 0;\r\n      xc[b] = BASE === xc[b] ? 0 : xc[b] % BASE;\r\n    }\r\n\r\n    if (a) {\r\n      xc = [a].concat(xc);\r\n      ++ye;\r\n    }\r\n\r\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n    // ye = MAX_EXP + 1 possible\r\n    return normalise(y, xc, ye);\r\n  };\r\n\r\n\r\n  /*\r\n   * If sd is undefined or null or true or false, return the number of significant digits of\r\n   * the value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n   * If sd is true include integer-part trailing zeros in the count.\r\n   *\r\n   * Otherwise, if sd is a number, return a new BigNumber whose value is the value of this\r\n   * BigNumber rounded to a maximum of sd significant digits using rounding mode rm, or\r\n   * ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * sd {number|boolean} number: significant digits: integer, 1 to MAX inclusive.\r\n   *                     boolean: whether to count integer-part trailing zeros: true or false.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n   */\r\n  P.precision = P.sd = function (sd, rm) {\r\n    var c, n, v,\r\n      x = this;\r\n\r\n    if (sd != null && sd !== !!sd) {\r\n      intCheck(sd, 1, MAX);\r\n      if (rm == null) rm = ROUNDING_MODE;\r\n      else intCheck(rm, 0, 8);\r\n\r\n      return round(new BigNumber(x), sd, rm);\r\n    }\r\n\r\n    if (!(c = x.c)) return null;\r\n    v = c.length - 1;\r\n    n = v * LOG_BASE + 1;\r\n\r\n    if (v = c[v]) {\r\n\r\n      // Subtract the number of trailing zeros of the last element.\r\n      for (; v % 10 == 0; v /= 10, n--);\r\n\r\n      // Add the number of digits of the first element.\r\n      for (v = c[0]; v >= 10; v /= 10, n++);\r\n    }\r\n\r\n    if (sd && x.e + 1 > n) n = x.e + 1;\r\n\r\n    return n;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new BigNumber whose value is the value of this BigNumber shifted by k places\r\n   * (powers of 10). Shift to the right if n > 0, and to the left if n < 0.\r\n   *\r\n   * k {number} Integer, -MAX_SAFE_INTEGER to MAX_SAFE_INTEGER inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {k}'\r\n   */\r\n  P.shiftedBy = function (k) {\r\n    intCheck(k, -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);\r\n    return this.times('1e' + k);\r\n  };\r\n\r\n\r\n  /*\r\n   *  sqrt(-n) =  N\r\n   *  sqrt(N) =  N\r\n   *  sqrt(-I) =  N\r\n   *  sqrt(I) =  I\r\n   *  sqrt(0) =  0\r\n   *  sqrt(-0) = -0\r\n   *\r\n   * Return a new BigNumber whose value is the square root of the value of this BigNumber,\r\n   * rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n   */\r\n  P.squareRoot = P.sqrt = function () {\r\n    var m, n, r, rep, t,\r\n      x = this,\r\n      c = x.c,\r\n      s = x.s,\r\n      e = x.e,\r\n      dp = DECIMAL_PLACES + 4,\r\n      half = new BigNumber('0.5');\r\n\r\n    // Negative/NaN/Infinity/zero?\r\n    if (s !== 1 || !c || !c[0]) {\r\n      return new BigNumber(!s || s < 0 && (!c || c[0]) ? NaN : c ? x : 1 / 0);\r\n    }\r\n\r\n    // Initial estimate.\r\n    s = Math.sqrt(+valueOf(x));\r\n\r\n    // Math.sqrt underflow/overflow?\r\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n    if (s == 0 || s == 1 / 0) {\r\n      n = coeffToString(c);\r\n      if ((n.length + e) % 2 == 0) n += '0';\r\n      s = Math.sqrt(+n);\r\n      e = bitFloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n      if (s == 1 / 0) {\r\n        n = '5e' + e;\r\n      } else {\r\n        n = s.toExponential();\r\n        n = n.slice(0, n.indexOf('e') + 1) + e;\r\n      }\r\n\r\n      r = new BigNumber(n);\r\n    } else {\r\n      r = new BigNumber(s + '');\r\n    }\r\n\r\n    // Check for zero.\r\n    // r could be zero if MIN_EXP is changed after the this value was created.\r\n    // This would cause a division by zero (x/t) and hence Infinity below, which would cause\r\n    // coeffToString to throw.\r\n    if (r.c[0]) {\r\n      e = r.e;\r\n      s = e + dp;\r\n      if (s < 3) s = 0;\r\n\r\n      // Newton-Raphson iteration.\r\n      for (; ;) {\r\n        t = r;\r\n        r = half.times(t.plus(div(x, t, dp, 1)));\r\n\r\n        if (coeffToString(t.c).slice(0, s) === (n = coeffToString(r.c)).slice(0, s)) {\r\n\r\n          // The exponent of r may here be one less than the final result exponent,\r\n          // e.g 0.0009999 (e-4) --> 0.001 (e-3), so adjust s so the rounding digits\r\n          // are indexed correctly.\r\n          if (r.e < e) --s;\r\n          n = n.slice(s - 3, s + 1);\r\n\r\n          // The 4th rounding digit may be in error by -1 so if the 4 rounding digits\r\n          // are 9999 or 4999 (i.e. approaching a rounding boundary) continue the\r\n          // iteration.\r\n          if (n == '9999' || !rep && n == '4999') {\r\n\r\n            // On the first iteration only, check to see if rounding up gives the\r\n            // exact result as the nines may infinitely repeat.\r\n            if (!rep) {\r\n              round(t, t.e + DECIMAL_PLACES + 2, 0);\r\n\r\n              if (t.times(t).eq(x)) {\r\n                r = t;\r\n                break;\r\n              }\r\n            }\r\n\r\n            dp += 4;\r\n            s += 4;\r\n            rep = 1;\r\n          } else {\r\n\r\n            // If rounding digits are null, 0{0,4} or 50{0,3}, check for exact\r\n            // result. If not, then there are further digits and m will be truthy.\r\n            if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n              // Truncate to the first rounding digit.\r\n              round(r, r.e + DECIMAL_PLACES + 2, 1);\r\n              m = !r.times(r).eq(x);\r\n            }\r\n\r\n            break;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    return round(r, r.e + DECIMAL_PLACES + 1, ROUNDING_MODE, m);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in exponential notation and\r\n   * rounded using ROUNDING_MODE to dp fixed decimal places.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   */\r\n  P.toExponential = function (dp, rm) {\r\n    if (dp != null) {\r\n      intCheck(dp, 0, MAX);\r\n      dp++;\r\n    }\r\n    return format(this, dp, rm, 1);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in fixed-point notation rounding\r\n   * to dp fixed decimal places using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n   *\r\n   * Note: as with JavaScript's number type, (-0).toFixed(0) is '0',\r\n   * but e.g. (-0.00001).toFixed(0) is '-0'.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   */\r\n  P.toFixed = function (dp, rm) {\r\n    if (dp != null) {\r\n      intCheck(dp, 0, MAX);\r\n      dp = dp + this.e + 1;\r\n    }\r\n    return format(this, dp, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in fixed-point notation rounded\r\n   * using rm or ROUNDING_MODE to dp decimal places, and formatted according to the properties\r\n   * of the format or FORMAT object (see BigNumber.set).\r\n   *\r\n   * The formatting object may contain some or all of the properties shown below.\r\n   *\r\n   * FORMAT = {\r\n   *   prefix: '',\r\n   *   groupSize: 3,\r\n   *   secondaryGroupSize: 0,\r\n   *   groupSeparator: ',',\r\n   *   decimalSeparator: '.',\r\n   *   fractionGroupSize: 0,\r\n   *   fractionGroupSeparator: '\\xA0',      // non-breaking space\r\n   *   suffix: ''\r\n   * };\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   * [format] {object} Formatting options. See FORMAT pbject above.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n   * '[BigNumber Error] Argument not an object: {format}'\r\n   */\r\n  P.toFormat = function (dp, rm, format) {\r\n    var str,\r\n      x = this;\r\n\r\n    if (format == null) {\r\n      if (dp != null && rm && typeof rm == 'object') {\r\n        format = rm;\r\n        rm = null;\r\n      } else if (dp && typeof dp == 'object') {\r\n        format = dp;\r\n        dp = rm = null;\r\n      } else {\r\n        format = FORMAT;\r\n      }\r\n    } else if (typeof format != 'object') {\r\n      throw Error\r\n        (bignumberError + 'Argument not an object: ' + format);\r\n    }\r\n\r\n    str = x.toFixed(dp, rm);\r\n\r\n    if (x.c) {\r\n      var i,\r\n        arr = str.split('.'),\r\n        g1 = +format.groupSize,\r\n        g2 = +format.secondaryGroupSize,\r\n        groupSeparator = format.groupSeparator || '',\r\n        intPart = arr[0],\r\n        fractionPart = arr[1],\r\n        isNeg = x.s < 0,\r\n        intDigits = isNeg ? intPart.slice(1) : intPart,\r\n        len = intDigits.length;\r\n\r\n      if (g2) {\r\n        i = g1;\r\n        g1 = g2;\r\n        g2 = i;\r\n        len -= i;\r\n      }\r\n\r\n      if (g1 > 0 && len > 0) {\r\n        i = len % g1 || g1;\r\n        intPart = intDigits.substr(0, i);\r\n        for (; i < len; i += g1) intPart += groupSeparator + intDigits.substr(i, g1);\r\n        if (g2 > 0) intPart += groupSeparator + intDigits.slice(i);\r\n        if (isNeg) intPart = '-' + intPart;\r\n      }\r\n\r\n      str = fractionPart\r\n       ? intPart + (format.decimalSeparator || '') + ((g2 = +format.fractionGroupSize)\r\n        ? fractionPart.replace(new RegExp('\\\\d{' + g2 + '}\\\\B', 'g'),\r\n         '$&' + (format.fractionGroupSeparator || ''))\r\n        : fractionPart)\r\n       : intPart;\r\n    }\r\n\r\n    return (format.prefix || '') + str + (format.suffix || '');\r\n  };\r\n\r\n\r\n  /*\r\n   * Return an array of two BigNumbers representing the value of this BigNumber as a simple\r\n   * fraction with an integer numerator and an integer denominator.\r\n   * The denominator will be a positive non-zero value less than or equal to the specified\r\n   * maximum denominator. If a maximum denominator is not specified, the denominator will be\r\n   * the lowest value necessary to represent the number exactly.\r\n   *\r\n   * [md] {number|string|BigNumber} Integer >= 1, or Infinity. The maximum denominator.\r\n   *\r\n   * '[BigNumber Error] Argument {not an integer|out of range} : {md}'\r\n   */\r\n  P.toFraction = function (md) {\r\n    var d, d0, d1, d2, e, exp, n, n0, n1, q, r, s,\r\n      x = this,\r\n      xc = x.c;\r\n\r\n    if (md != null) {\r\n      n = new BigNumber(md);\r\n\r\n      // Throw if md is less than one or is not an integer, unless it is Infinity.\r\n      if (!n.isInteger() && (n.c || n.s !== 1) || n.lt(ONE)) {\r\n        throw Error\r\n          (bignumberError + 'Argument ' +\r\n            (n.isInteger() ? 'out of range: ' : 'not an integer: ') + valueOf(n));\r\n      }\r\n    }\r\n\r\n    if (!xc) return new BigNumber(x);\r\n\r\n    d = new BigNumber(ONE);\r\n    n1 = d0 = new BigNumber(ONE);\r\n    d1 = n0 = new BigNumber(ONE);\r\n    s = coeffToString(xc);\r\n\r\n    // Determine initial denominator.\r\n    // d is a power of 10 and the minimum max denominator that specifies the value exactly.\r\n    e = d.e = s.length - x.e - 1;\r\n    d.c[0] = POWS_TEN[(exp = e % LOG_BASE) < 0 ? LOG_BASE + exp : exp];\r\n    md = !md || n.comparedTo(d) > 0 ? (e > 0 ? d : n1) : n;\r\n\r\n    exp = MAX_EXP;\r\n    MAX_EXP = 1 / 0;\r\n    n = new BigNumber(s);\r\n\r\n    // n0 = d1 = 0\r\n    n0.c[0] = 0;\r\n\r\n    for (; ;)  {\r\n      q = div(n, d, 0, 1);\r\n      d2 = d0.plus(q.times(d1));\r\n      if (d2.comparedTo(md) == 1) break;\r\n      d0 = d1;\r\n      d1 = d2;\r\n      n1 = n0.plus(q.times(d2 = n1));\r\n      n0 = d2;\r\n      d = n.minus(q.times(d2 = d));\r\n      n = d2;\r\n    }\r\n\r\n    d2 = div(md.minus(d0), d1, 0, 1);\r\n    n0 = n0.plus(d2.times(n1));\r\n    d0 = d0.plus(d2.times(d1));\r\n    n0.s = n1.s = x.s;\r\n    e = e * 2;\r\n\r\n    // Determine which fraction is closer to x, n0/d0 or n1/d1\r\n    r = div(n1, d1, e, ROUNDING_MODE).minus(x).abs().comparedTo(\r\n        div(n0, d0, e, ROUNDING_MODE).minus(x).abs()) < 1 ? [n1, d1] : [n0, d0];\r\n\r\n    MAX_EXP = exp;\r\n\r\n    return r;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the value of this BigNumber converted to a number primitive.\r\n   */\r\n  P.toNumber = function () {\r\n    return +valueOf(this);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber rounded to sd significant digits\r\n   * using rounding mode rm or ROUNDING_MODE. If sd is less than the number of digits\r\n   * necessary to represent the integer part of the value in fixed-point notation, then use\r\n   * exponential notation.\r\n   *\r\n   * [sd] {number} Significant digits. Integer, 1 to MAX inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n   */\r\n  P.toPrecision = function (sd, rm) {\r\n    if (sd != null) intCheck(sd, 1, MAX);\r\n    return format(this, sd, rm, 2);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this BigNumber in base b, or base 10 if b is\r\n   * omitted. If a base is specified, including base 10, round according to DECIMAL_PLACES and\r\n   * ROUNDING_MODE. If a base is not specified, and this BigNumber has a positive exponent\r\n   * that is equal to or greater than TO_EXP_POS, or a negative exponent equal to or less than\r\n   * TO_EXP_NEG, return exponential notation.\r\n   *\r\n   * [b] {number} Integer, 2 to ALPHABET.length inclusive.\r\n   *\r\n   * '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n   */\r\n  P.toString = function (b) {\r\n    var str,\r\n      n = this,\r\n      s = n.s,\r\n      e = n.e;\r\n\r\n    // Infinity or NaN?\r\n    if (e === null) {\r\n      if (s) {\r\n        str = 'Infinity';\r\n        if (s < 0) str = '-' + str;\r\n      } else {\r\n        str = 'NaN';\r\n      }\r\n    } else {\r\n      if (b == null) {\r\n        str = e <= TO_EXP_NEG || e >= TO_EXP_POS\r\n         ? toExponential(coeffToString(n.c), e)\r\n         : toFixedPoint(coeffToString(n.c), e, '0');\r\n      } else if (b === 10 && alphabetHasNormalDecimalDigits) {\r\n        n = round(new BigNumber(n), DECIMAL_PLACES + e + 1, ROUNDING_MODE);\r\n        str = toFixedPoint(coeffToString(n.c), n.e, '0');\r\n      } else {\r\n        intCheck(b, 2, ALPHABET.length, 'Base');\r\n        str = convertBase(toFixedPoint(coeffToString(n.c), e, '0'), 10, b, s, true);\r\n      }\r\n\r\n      if (s < 0 && n.c[0]) str = '-' + str;\r\n    }\r\n\r\n    return str;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return as toString, but do not accept a base argument, and include the minus sign for\r\n   * negative zero.\r\n   */\r\n  P.valueOf = P.toJSON = function () {\r\n    return valueOf(this);\r\n  };\r\n\r\n\r\n  P._isBigNumber = true;\r\n\r\n  P[Symbol.toStringTag] = 'BigNumber';\r\n\r\n  // Node.js v10.12.0+\r\n  P[Symbol.for('nodejs.util.inspect.custom')] = P.valueOf;\r\n\r\n  if (configObject != null) BigNumber.set(configObject);\r\n\r\n  return BigNumber;\r\n}\r\n\r\n\r\n// PRIVATE HELPER FUNCTIONS\r\n\r\n// These functions don't need access to variables,\r\n// e.g. DECIMAL_PLACES, in the scope of the `clone` function above.\r\n\r\n\r\nfunction bitFloor(n) {\r\n  var i = n | 0;\r\n  return n > 0 || n === i ? i : i - 1;\r\n}\r\n\r\n\r\n// Return a coefficient array as a string of base 10 digits.\r\nfunction coeffToString(a) {\r\n  var s, z,\r\n    i = 1,\r\n    j = a.length,\r\n    r = a[0] + '';\r\n\r\n  for (; i < j;) {\r\n    s = a[i++] + '';\r\n    z = LOG_BASE - s.length;\r\n    for (; z--; s = '0' + s);\r\n    r += s;\r\n  }\r\n\r\n  // Determine trailing zeros.\r\n  for (j = r.length; r.charCodeAt(--j) === 48;);\r\n\r\n  return r.slice(0, j + 1 || 1);\r\n}\r\n\r\n\r\n// Compare the value of BigNumbers x and y.\r\nfunction compare(x, y) {\r\n  var a, b,\r\n    xc = x.c,\r\n    yc = y.c,\r\n    i = x.s,\r\n    j = y.s,\r\n    k = x.e,\r\n    l = y.e;\r\n\r\n  // Either NaN?\r\n  if (!i || !j) return null;\r\n\r\n  a = xc && !xc[0];\r\n  b = yc && !yc[0];\r\n\r\n  // Either zero?\r\n  if (a || b) return a ? b ? 0 : -j : i;\r\n\r\n  // Signs differ?\r\n  if (i != j) return i;\r\n\r\n  a = i < 0;\r\n  b = k == l;\r\n\r\n  // Either Infinity?\r\n  if (!xc || !yc) return b ? 0 : !xc ^ a ? 1 : -1;\r\n\r\n  // Compare exponents.\r\n  if (!b) return k > l ^ a ? 1 : -1;\r\n\r\n  j = (k = xc.length) < (l = yc.length) ? k : l;\r\n\r\n  // Compare digit by digit.\r\n  for (i = 0; i < j; i++) if (xc[i] != yc[i]) return xc[i] > yc[i] ^ a ? 1 : -1;\r\n\r\n  // Compare lengths.\r\n  return k == l ? 0 : k > l ^ a ? 1 : -1;\r\n}\r\n\r\n\r\n/*\r\n * Check that n is a primitive number, an integer, and in range, otherwise throw.\r\n */\r\nfunction intCheck(n, min, max, name) {\r\n  if (n < min || n > max || n !== mathfloor(n)) {\r\n    throw Error\r\n     (bignumberError + (name || 'Argument') + (typeof n == 'number'\r\n       ? n < min || n > max ? ' out of range: ' : ' not an integer: '\r\n       : ' not a primitive number: ') + String(n));\r\n  }\r\n}\r\n\r\n\r\n// Assumes finite n.\r\nfunction isOdd(n) {\r\n  var k = n.c.length - 1;\r\n  return bitFloor(n.e / LOG_BASE) == k && n.c[k] % 2 != 0;\r\n}\r\n\r\n\r\nfunction toExponential(str, e) {\r\n  return (str.length > 1 ? str.charAt(0) + '.' + str.slice(1) : str) +\r\n   (e < 0 ? 'e' : 'e+') + e;\r\n}\r\n\r\n\r\nfunction toFixedPoint(str, e, z) {\r\n  var len, zs;\r\n\r\n  // Negative exponent?\r\n  if (e < 0) {\r\n\r\n    // Prepend zeros.\r\n    for (zs = z + '.'; ++e; zs += z);\r\n    str = zs + str;\r\n\r\n  // Positive exponent\r\n  } else {\r\n    len = str.length;\r\n\r\n    // Append zeros.\r\n    if (++e > len) {\r\n      for (zs = z, e -= len; --e; zs += z);\r\n      str += zs;\r\n    } else if (e < len) {\r\n      str = str.slice(0, e) + '.' + str.slice(e);\r\n    }\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\n\r\n// EXPORT\r\n\r\n\r\nvar BigNumber = clone();\r\n\r\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BigNumber);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bignumber.js/bignumber.mjs\n");

/***/ })

};
;