/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/borsh";
exports.ids = ["vendor-chunks/borsh"];
exports.modules = {

/***/ "(rsc)/./node_modules/borsh/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/borsh/lib/index.js ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.deserializeUnchecked = exports.deserialize = exports.serialize = exports.BinaryReader = exports.BinaryWriter = exports.BorshError = exports.baseDecode = exports.baseEncode = void 0;\nconst bn_js_1 = __importDefault(__webpack_require__(/*! bn.js */ \"(rsc)/./node_modules/bn.js/lib/bn.js\"));\nconst bs58_1 = __importDefault(__webpack_require__(/*! bs58 */ \"(rsc)/./node_modules/borsh/node_modules/bs58/index.js\"));\n// TODO: Make sure this polyfill not included when not required\nconst encoding = __importStar(__webpack_require__(/*! text-encoding-utf-8 */ \"(rsc)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js\"));\nconst ResolvedTextDecoder = typeof TextDecoder !== \"function\" ? encoding.TextDecoder : TextDecoder;\nconst textDecoder = new ResolvedTextDecoder(\"utf-8\", { fatal: true });\nfunction baseEncode(value) {\n    if (typeof value === \"string\") {\n        value = Buffer.from(value, \"utf8\");\n    }\n    return bs58_1.default.encode(Buffer.from(value));\n}\nexports.baseEncode = baseEncode;\nfunction baseDecode(value) {\n    return Buffer.from(bs58_1.default.decode(value));\n}\nexports.baseDecode = baseDecode;\nconst INITIAL_LENGTH = 1024;\nclass BorshError extends Error {\n    constructor(message) {\n        super(message);\n        this.fieldPath = [];\n        this.originalMessage = message;\n    }\n    addToFieldPath(fieldName) {\n        this.fieldPath.splice(0, 0, fieldName);\n        // NOTE: Modifying message directly as jest doesn't use .toString()\n        this.message = this.originalMessage + \": \" + this.fieldPath.join(\".\");\n    }\n}\nexports.BorshError = BorshError;\n/// Binary encoder.\nclass BinaryWriter {\n    constructor() {\n        this.buf = Buffer.alloc(INITIAL_LENGTH);\n        this.length = 0;\n    }\n    maybeResize() {\n        if (this.buf.length < 16 + this.length) {\n            this.buf = Buffer.concat([this.buf, Buffer.alloc(INITIAL_LENGTH)]);\n        }\n    }\n    writeU8(value) {\n        this.maybeResize();\n        this.buf.writeUInt8(value, this.length);\n        this.length += 1;\n    }\n    writeU16(value) {\n        this.maybeResize();\n        this.buf.writeUInt16LE(value, this.length);\n        this.length += 2;\n    }\n    writeU32(value) {\n        this.maybeResize();\n        this.buf.writeUInt32LE(value, this.length);\n        this.length += 4;\n    }\n    writeU64(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 8)));\n    }\n    writeU128(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 16)));\n    }\n    writeU256(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 32)));\n    }\n    writeU512(value) {\n        this.maybeResize();\n        this.writeBuffer(Buffer.from(new bn_js_1.default(value).toArray(\"le\", 64)));\n    }\n    writeBuffer(buffer) {\n        // Buffer.from is needed as this.buf.subarray can return plain Uint8Array in browser\n        this.buf = Buffer.concat([\n            Buffer.from(this.buf.subarray(0, this.length)),\n            buffer,\n            Buffer.alloc(INITIAL_LENGTH),\n        ]);\n        this.length += buffer.length;\n    }\n    writeString(str) {\n        this.maybeResize();\n        const b = Buffer.from(str, \"utf8\");\n        this.writeU32(b.length);\n        this.writeBuffer(b);\n    }\n    writeFixedArray(array) {\n        this.writeBuffer(Buffer.from(array));\n    }\n    writeArray(array, fn) {\n        this.maybeResize();\n        this.writeU32(array.length);\n        for (const elem of array) {\n            this.maybeResize();\n            fn(elem);\n        }\n    }\n    toArray() {\n        return this.buf.subarray(0, this.length);\n    }\n}\nexports.BinaryWriter = BinaryWriter;\nfunction handlingRangeError(target, propertyKey, propertyDescriptor) {\n    const originalMethod = propertyDescriptor.value;\n    propertyDescriptor.value = function (...args) {\n        try {\n            return originalMethod.apply(this, args);\n        }\n        catch (e) {\n            if (e instanceof RangeError) {\n                const code = e.code;\n                if ([\"ERR_BUFFER_OUT_OF_BOUNDS\", \"ERR_OUT_OF_RANGE\"].indexOf(code) >= 0) {\n                    throw new BorshError(\"Reached the end of buffer when deserializing\");\n                }\n            }\n            throw e;\n        }\n    };\n}\nclass BinaryReader {\n    constructor(buf) {\n        this.buf = buf;\n        this.offset = 0;\n    }\n    readU8() {\n        const value = this.buf.readUInt8(this.offset);\n        this.offset += 1;\n        return value;\n    }\n    readU16() {\n        const value = this.buf.readUInt16LE(this.offset);\n        this.offset += 2;\n        return value;\n    }\n    readU32() {\n        const value = this.buf.readUInt32LE(this.offset);\n        this.offset += 4;\n        return value;\n    }\n    readU64() {\n        const buf = this.readBuffer(8);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU128() {\n        const buf = this.readBuffer(16);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU256() {\n        const buf = this.readBuffer(32);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readU512() {\n        const buf = this.readBuffer(64);\n        return new bn_js_1.default(buf, \"le\");\n    }\n    readBuffer(len) {\n        if (this.offset + len > this.buf.length) {\n            throw new BorshError(`Expected buffer length ${len} isn't within bounds`);\n        }\n        const result = this.buf.slice(this.offset, this.offset + len);\n        this.offset += len;\n        return result;\n    }\n    readString() {\n        const len = this.readU32();\n        const buf = this.readBuffer(len);\n        try {\n            // NOTE: Using TextDecoder to fail on invalid UTF-8\n            return textDecoder.decode(buf);\n        }\n        catch (e) {\n            throw new BorshError(`Error decoding UTF-8 string: ${e}`);\n        }\n    }\n    readFixedArray(len) {\n        return new Uint8Array(this.readBuffer(len));\n    }\n    readArray(fn) {\n        const len = this.readU32();\n        const result = Array();\n        for (let i = 0; i < len; ++i) {\n            result.push(fn());\n        }\n        return result;\n    }\n}\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU8\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU16\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU32\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU64\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU128\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU256\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readU512\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readString\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readFixedArray\", null);\n__decorate([\n    handlingRangeError\n], BinaryReader.prototype, \"readArray\", null);\nexports.BinaryReader = BinaryReader;\nfunction capitalizeFirstLetter(string) {\n    return string.charAt(0).toUpperCase() + string.slice(1);\n}\nfunction serializeField(schema, fieldName, value, fieldType, writer) {\n    try {\n        // TODO: Handle missing values properly (make sure they never result in just skipped write)\n        if (typeof fieldType === \"string\") {\n            writer[`write${capitalizeFirstLetter(fieldType)}`](value);\n        }\n        else if (fieldType instanceof Array) {\n            if (typeof fieldType[0] === \"number\") {\n                if (value.length !== fieldType[0]) {\n                    throw new BorshError(`Expecting byte array of length ${fieldType[0]}, but got ${value.length} bytes`);\n                }\n                writer.writeFixedArray(value);\n            }\n            else if (fieldType.length === 2 && typeof fieldType[1] === \"number\") {\n                if (value.length !== fieldType[1]) {\n                    throw new BorshError(`Expecting byte array of length ${fieldType[1]}, but got ${value.length} bytes`);\n                }\n                for (let i = 0; i < fieldType[1]; i++) {\n                    serializeField(schema, null, value[i], fieldType[0], writer);\n                }\n            }\n            else {\n                writer.writeArray(value, (item) => {\n                    serializeField(schema, fieldName, item, fieldType[0], writer);\n                });\n            }\n        }\n        else if (fieldType.kind !== undefined) {\n            switch (fieldType.kind) {\n                case \"option\": {\n                    if (value === null || value === undefined) {\n                        writer.writeU8(0);\n                    }\n                    else {\n                        writer.writeU8(1);\n                        serializeField(schema, fieldName, value, fieldType.type, writer);\n                    }\n                    break;\n                }\n                case \"map\": {\n                    writer.writeU32(value.size);\n                    value.forEach((val, key) => {\n                        serializeField(schema, fieldName, key, fieldType.key, writer);\n                        serializeField(schema, fieldName, val, fieldType.value, writer);\n                    });\n                    break;\n                }\n                default:\n                    throw new BorshError(`FieldType ${fieldType} unrecognized`);\n            }\n        }\n        else {\n            serializeStruct(schema, value, writer);\n        }\n    }\n    catch (error) {\n        if (error instanceof BorshError) {\n            error.addToFieldPath(fieldName);\n        }\n        throw error;\n    }\n}\nfunction serializeStruct(schema, obj, writer) {\n    if (typeof obj.borshSerialize === \"function\") {\n        obj.borshSerialize(writer);\n        return;\n    }\n    const structSchema = schema.get(obj.constructor);\n    if (!structSchema) {\n        throw new BorshError(`Class ${obj.constructor.name} is missing in schema`);\n    }\n    if (structSchema.kind === \"struct\") {\n        structSchema.fields.map(([fieldName, fieldType]) => {\n            serializeField(schema, fieldName, obj[fieldName], fieldType, writer);\n        });\n    }\n    else if (structSchema.kind === \"enum\") {\n        const name = obj[structSchema.field];\n        for (let idx = 0; idx < structSchema.values.length; ++idx) {\n            const [fieldName, fieldType] = structSchema.values[idx];\n            if (fieldName === name) {\n                writer.writeU8(idx);\n                serializeField(schema, fieldName, obj[fieldName], fieldType, writer);\n                break;\n            }\n        }\n    }\n    else {\n        throw new BorshError(`Unexpected schema kind: ${structSchema.kind} for ${obj.constructor.name}`);\n    }\n}\n/// Serialize given object using schema of the form:\n/// { class_name -> [ [field_name, field_type], .. ], .. }\nfunction serialize(schema, obj, Writer = BinaryWriter) {\n    const writer = new Writer();\n    serializeStruct(schema, obj, writer);\n    return writer.toArray();\n}\nexports.serialize = serialize;\nfunction deserializeField(schema, fieldName, fieldType, reader) {\n    try {\n        if (typeof fieldType === \"string\") {\n            return reader[`read${capitalizeFirstLetter(fieldType)}`]();\n        }\n        if (fieldType instanceof Array) {\n            if (typeof fieldType[0] === \"number\") {\n                return reader.readFixedArray(fieldType[0]);\n            }\n            else if (typeof fieldType[1] === \"number\") {\n                const arr = [];\n                for (let i = 0; i < fieldType[1]; i++) {\n                    arr.push(deserializeField(schema, null, fieldType[0], reader));\n                }\n                return arr;\n            }\n            else {\n                return reader.readArray(() => deserializeField(schema, fieldName, fieldType[0], reader));\n            }\n        }\n        if (fieldType.kind === \"option\") {\n            const option = reader.readU8();\n            if (option) {\n                return deserializeField(schema, fieldName, fieldType.type, reader);\n            }\n            return undefined;\n        }\n        if (fieldType.kind === \"map\") {\n            let map = new Map();\n            const length = reader.readU32();\n            for (let i = 0; i < length; i++) {\n                const key = deserializeField(schema, fieldName, fieldType.key, reader);\n                const val = deserializeField(schema, fieldName, fieldType.value, reader);\n                map.set(key, val);\n            }\n            return map;\n        }\n        return deserializeStruct(schema, fieldType, reader);\n    }\n    catch (error) {\n        if (error instanceof BorshError) {\n            error.addToFieldPath(fieldName);\n        }\n        throw error;\n    }\n}\nfunction deserializeStruct(schema, classType, reader) {\n    if (typeof classType.borshDeserialize === \"function\") {\n        return classType.borshDeserialize(reader);\n    }\n    const structSchema = schema.get(classType);\n    if (!structSchema) {\n        throw new BorshError(`Class ${classType.name} is missing in schema`);\n    }\n    if (structSchema.kind === \"struct\") {\n        const result = {};\n        for (const [fieldName, fieldType] of schema.get(classType).fields) {\n            result[fieldName] = deserializeField(schema, fieldName, fieldType, reader);\n        }\n        return new classType(result);\n    }\n    if (structSchema.kind === \"enum\") {\n        const idx = reader.readU8();\n        if (idx >= structSchema.values.length) {\n            throw new BorshError(`Enum index: ${idx} is out of range`);\n        }\n        const [fieldName, fieldType] = structSchema.values[idx];\n        const fieldValue = deserializeField(schema, fieldName, fieldType, reader);\n        return new classType({ [fieldName]: fieldValue });\n    }\n    throw new BorshError(`Unexpected schema kind: ${structSchema.kind} for ${classType.constructor.name}`);\n}\n/// Deserializes object from bytes using schema.\nfunction deserialize(schema, classType, buffer, Reader = BinaryReader) {\n    const reader = new Reader(buffer);\n    const result = deserializeStruct(schema, classType, reader);\n    if (reader.offset < buffer.length) {\n        throw new BorshError(`Unexpected ${buffer.length - reader.offset} bytes after deserialized data`);\n    }\n    return result;\n}\nexports.deserialize = deserialize;\n/// Deserializes object from bytes using schema, without checking the length read\nfunction deserializeUnchecked(schema, classType, buffer, Reader = BinaryReader) {\n    const reader = new Reader(buffer);\n    return deserializeStruct(schema, classType, reader);\n}\nexports.deserializeUnchecked = deserializeUnchecked;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYm9yc2gvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0EsMENBQTBDLDRCQUE0QjtBQUN0RSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLFFBQVE7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsNEJBQTRCLEdBQUcsbUJBQW1CLEdBQUcsaUJBQWlCLEdBQUcsb0JBQW9CLEdBQUcsb0JBQW9CLEdBQUcsa0JBQWtCLEdBQUcsa0JBQWtCLEdBQUcsa0JBQWtCO0FBQ25MLGdDQUFnQyxtQkFBTyxDQUFDLG1EQUFPO0FBQy9DLCtCQUErQixtQkFBTyxDQUFDLG1FQUFNO0FBQzdDO0FBQ0EsOEJBQThCLG1CQUFPLENBQUMseUZBQXFCO0FBQzNEO0FBQ0EsdURBQXVELGFBQWE7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsS0FBSztBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxFQUFFO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsU0FBUztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLGlDQUFpQztBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJFQUEyRSxhQUFhLFlBQVksY0FBYztBQUNsSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkVBQTJFLGFBQWEsWUFBWSxjQUFjO0FBQ2xIO0FBQ0EsZ0NBQWdDLGtCQUFrQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELFdBQVc7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxzQkFBc0I7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLGtDQUFrQztBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0QsbUJBQW1CLE1BQU0scUJBQXFCO0FBQ3RHO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxpQ0FBaUM7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msa0JBQWtCO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixZQUFZO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxnQkFBZ0I7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRCxLQUFLO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix5QkFBeUI7QUFDeEQ7QUFDQSxvREFBb0QsbUJBQW1CLE1BQU0sMkJBQTJCO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQywrQkFBK0I7QUFDMUU7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL2JvcnNoL2xpYi9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19zZXRNb2R1bGVEZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX3NldE1vZHVsZURlZmF1bHQpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIHYpIHtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgXCJkZWZhdWx0XCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgdmFsdWU6IHYgfSk7XG59KSA6IGZ1bmN0aW9uKG8sIHYpIHtcbiAgICBvW1wiZGVmYXVsdFwiXSA9IHY7XG59KTtcbnZhciBfX2RlY29yYXRlID0gKHRoaXMgJiYgdGhpcy5fX2RlY29yYXRlKSB8fCBmdW5jdGlvbiAoZGVjb3JhdG9ycywgdGFyZ2V0LCBrZXksIGRlc2MpIHtcbiAgICB2YXIgYyA9IGFyZ3VtZW50cy5sZW5ndGgsIHIgPSBjIDwgMyA/IHRhcmdldCA6IGRlc2MgPT09IG51bGwgPyBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcih0YXJnZXQsIGtleSkgOiBkZXNjLCBkO1xuICAgIGlmICh0eXBlb2YgUmVmbGVjdCA9PT0gXCJvYmplY3RcIiAmJiB0eXBlb2YgUmVmbGVjdC5kZWNvcmF0ZSA9PT0gXCJmdW5jdGlvblwiKSByID0gUmVmbGVjdC5kZWNvcmF0ZShkZWNvcmF0b3JzLCB0YXJnZXQsIGtleSwgZGVzYyk7XG4gICAgZWxzZSBmb3IgKHZhciBpID0gZGVjb3JhdG9ycy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkgaWYgKGQgPSBkZWNvcmF0b3JzW2ldKSByID0gKGMgPCAzID8gZChyKSA6IGMgPiAzID8gZCh0YXJnZXQsIGtleSwgcikgOiBkKHRhcmdldCwga2V5KSkgfHwgcjtcbiAgICByZXR1cm4gYyA+IDMgJiYgciAmJiBPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBrZXksIHIpLCByO1xufTtcbnZhciBfX2ltcG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9faW1wb3J0U3RhcikgfHwgZnVuY3Rpb24gKG1vZCkge1xuICAgIGlmIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpIHJldHVybiBtb2Q7XG4gICAgdmFyIHJlc3VsdCA9IHt9O1xuICAgIGlmIChtb2QgIT0gbnVsbCkgZm9yICh2YXIgayBpbiBtb2QpIGlmIChrICE9PSBcImRlZmF1bHRcIiAmJiBPYmplY3QuaGFzT3duUHJvcGVydHkuY2FsbChtb2QsIGspKSBfX2NyZWF0ZUJpbmRpbmcocmVzdWx0LCBtb2QsIGspO1xuICAgIF9fc2V0TW9kdWxlRGVmYXVsdChyZXN1bHQsIG1vZCk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbn07XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmRlc2VyaWFsaXplVW5jaGVja2VkID0gZXhwb3J0cy5kZXNlcmlhbGl6ZSA9IGV4cG9ydHMuc2VyaWFsaXplID0gZXhwb3J0cy5CaW5hcnlSZWFkZXIgPSBleHBvcnRzLkJpbmFyeVdyaXRlciA9IGV4cG9ydHMuQm9yc2hFcnJvciA9IGV4cG9ydHMuYmFzZURlY29kZSA9IGV4cG9ydHMuYmFzZUVuY29kZSA9IHZvaWQgMDtcbmNvbnN0IGJuX2pzXzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcImJuLmpzXCIpKTtcbmNvbnN0IGJzNThfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiYnM1OFwiKSk7XG4vLyBUT0RPOiBNYWtlIHN1cmUgdGhpcyBwb2x5ZmlsbCBub3QgaW5jbHVkZWQgd2hlbiBub3QgcmVxdWlyZWRcbmNvbnN0IGVuY29kaW5nID0gX19pbXBvcnRTdGFyKHJlcXVpcmUoXCJ0ZXh0LWVuY29kaW5nLXV0Zi04XCIpKTtcbmNvbnN0IFJlc29sdmVkVGV4dERlY29kZXIgPSB0eXBlb2YgVGV4dERlY29kZXIgIT09IFwiZnVuY3Rpb25cIiA/IGVuY29kaW5nLlRleHREZWNvZGVyIDogVGV4dERlY29kZXI7XG5jb25zdCB0ZXh0RGVjb2RlciA9IG5ldyBSZXNvbHZlZFRleHREZWNvZGVyKFwidXRmLThcIiwgeyBmYXRhbDogdHJ1ZSB9KTtcbmZ1bmN0aW9uIGJhc2VFbmNvZGUodmFsdWUpIHtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHZhbHVlID0gQnVmZmVyLmZyb20odmFsdWUsIFwidXRmOFwiKTtcbiAgICB9XG4gICAgcmV0dXJuIGJzNThfMS5kZWZhdWx0LmVuY29kZShCdWZmZXIuZnJvbSh2YWx1ZSkpO1xufVxuZXhwb3J0cy5iYXNlRW5jb2RlID0gYmFzZUVuY29kZTtcbmZ1bmN0aW9uIGJhc2VEZWNvZGUodmFsdWUpIHtcbiAgICByZXR1cm4gQnVmZmVyLmZyb20oYnM1OF8xLmRlZmF1bHQuZGVjb2RlKHZhbHVlKSk7XG59XG5leHBvcnRzLmJhc2VEZWNvZGUgPSBiYXNlRGVjb2RlO1xuY29uc3QgSU5JVElBTF9MRU5HVEggPSAxMDI0O1xuY2xhc3MgQm9yc2hFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihtZXNzYWdlKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLmZpZWxkUGF0aCA9IFtdO1xuICAgICAgICB0aGlzLm9yaWdpbmFsTWVzc2FnZSA9IG1lc3NhZ2U7XG4gICAgfVxuICAgIGFkZFRvRmllbGRQYXRoKGZpZWxkTmFtZSkge1xuICAgICAgICB0aGlzLmZpZWxkUGF0aC5zcGxpY2UoMCwgMCwgZmllbGROYW1lKTtcbiAgICAgICAgLy8gTk9URTogTW9kaWZ5aW5nIG1lc3NhZ2UgZGlyZWN0bHkgYXMgamVzdCBkb2Vzbid0IHVzZSAudG9TdHJpbmcoKVxuICAgICAgICB0aGlzLm1lc3NhZ2UgPSB0aGlzLm9yaWdpbmFsTWVzc2FnZSArIFwiOiBcIiArIHRoaXMuZmllbGRQYXRoLmpvaW4oXCIuXCIpO1xuICAgIH1cbn1cbmV4cG9ydHMuQm9yc2hFcnJvciA9IEJvcnNoRXJyb3I7XG4vLy8gQmluYXJ5IGVuY29kZXIuXG5jbGFzcyBCaW5hcnlXcml0ZXIge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLmJ1ZiA9IEJ1ZmZlci5hbGxvYyhJTklUSUFMX0xFTkdUSCk7XG4gICAgICAgIHRoaXMubGVuZ3RoID0gMDtcbiAgICB9XG4gICAgbWF5YmVSZXNpemUoKSB7XG4gICAgICAgIGlmICh0aGlzLmJ1Zi5sZW5ndGggPCAxNiArIHRoaXMubGVuZ3RoKSB7XG4gICAgICAgICAgICB0aGlzLmJ1ZiA9IEJ1ZmZlci5jb25jYXQoW3RoaXMuYnVmLCBCdWZmZXIuYWxsb2MoSU5JVElBTF9MRU5HVEgpXSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgd3JpdGVVOCh2YWx1ZSkge1xuICAgICAgICB0aGlzLm1heWJlUmVzaXplKCk7XG4gICAgICAgIHRoaXMuYnVmLndyaXRlVUludDgodmFsdWUsIHRoaXMubGVuZ3RoKTtcbiAgICAgICAgdGhpcy5sZW5ndGggKz0gMTtcbiAgICB9XG4gICAgd3JpdGVVMTYodmFsdWUpIHtcbiAgICAgICAgdGhpcy5tYXliZVJlc2l6ZSgpO1xuICAgICAgICB0aGlzLmJ1Zi53cml0ZVVJbnQxNkxFKHZhbHVlLCB0aGlzLmxlbmd0aCk7XG4gICAgICAgIHRoaXMubGVuZ3RoICs9IDI7XG4gICAgfVxuICAgIHdyaXRlVTMyKHZhbHVlKSB7XG4gICAgICAgIHRoaXMubWF5YmVSZXNpemUoKTtcbiAgICAgICAgdGhpcy5idWYud3JpdGVVSW50MzJMRSh2YWx1ZSwgdGhpcy5sZW5ndGgpO1xuICAgICAgICB0aGlzLmxlbmd0aCArPSA0O1xuICAgIH1cbiAgICB3cml0ZVU2NCh2YWx1ZSkge1xuICAgICAgICB0aGlzLm1heWJlUmVzaXplKCk7XG4gICAgICAgIHRoaXMud3JpdGVCdWZmZXIoQnVmZmVyLmZyb20obmV3IGJuX2pzXzEuZGVmYXVsdCh2YWx1ZSkudG9BcnJheShcImxlXCIsIDgpKSk7XG4gICAgfVxuICAgIHdyaXRlVTEyOCh2YWx1ZSkge1xuICAgICAgICB0aGlzLm1heWJlUmVzaXplKCk7XG4gICAgICAgIHRoaXMud3JpdGVCdWZmZXIoQnVmZmVyLmZyb20obmV3IGJuX2pzXzEuZGVmYXVsdCh2YWx1ZSkudG9BcnJheShcImxlXCIsIDE2KSkpO1xuICAgIH1cbiAgICB3cml0ZVUyNTYodmFsdWUpIHtcbiAgICAgICAgdGhpcy5tYXliZVJlc2l6ZSgpO1xuICAgICAgICB0aGlzLndyaXRlQnVmZmVyKEJ1ZmZlci5mcm9tKG5ldyBibl9qc18xLmRlZmF1bHQodmFsdWUpLnRvQXJyYXkoXCJsZVwiLCAzMikpKTtcbiAgICB9XG4gICAgd3JpdGVVNTEyKHZhbHVlKSB7XG4gICAgICAgIHRoaXMubWF5YmVSZXNpemUoKTtcbiAgICAgICAgdGhpcy53cml0ZUJ1ZmZlcihCdWZmZXIuZnJvbShuZXcgYm5fanNfMS5kZWZhdWx0KHZhbHVlKS50b0FycmF5KFwibGVcIiwgNjQpKSk7XG4gICAgfVxuICAgIHdyaXRlQnVmZmVyKGJ1ZmZlcikge1xuICAgICAgICAvLyBCdWZmZXIuZnJvbSBpcyBuZWVkZWQgYXMgdGhpcy5idWYuc3ViYXJyYXkgY2FuIHJldHVybiBwbGFpbiBVaW50OEFycmF5IGluIGJyb3dzZXJcbiAgICAgICAgdGhpcy5idWYgPSBCdWZmZXIuY29uY2F0KFtcbiAgICAgICAgICAgIEJ1ZmZlci5mcm9tKHRoaXMuYnVmLnN1YmFycmF5KDAsIHRoaXMubGVuZ3RoKSksXG4gICAgICAgICAgICBidWZmZXIsXG4gICAgICAgICAgICBCdWZmZXIuYWxsb2MoSU5JVElBTF9MRU5HVEgpLFxuICAgICAgICBdKTtcbiAgICAgICAgdGhpcy5sZW5ndGggKz0gYnVmZmVyLmxlbmd0aDtcbiAgICB9XG4gICAgd3JpdGVTdHJpbmcoc3RyKSB7XG4gICAgICAgIHRoaXMubWF5YmVSZXNpemUoKTtcbiAgICAgICAgY29uc3QgYiA9IEJ1ZmZlci5mcm9tKHN0ciwgXCJ1dGY4XCIpO1xuICAgICAgICB0aGlzLndyaXRlVTMyKGIubGVuZ3RoKTtcbiAgICAgICAgdGhpcy53cml0ZUJ1ZmZlcihiKTtcbiAgICB9XG4gICAgd3JpdGVGaXhlZEFycmF5KGFycmF5KSB7XG4gICAgICAgIHRoaXMud3JpdGVCdWZmZXIoQnVmZmVyLmZyb20oYXJyYXkpKTtcbiAgICB9XG4gICAgd3JpdGVBcnJheShhcnJheSwgZm4pIHtcbiAgICAgICAgdGhpcy5tYXliZVJlc2l6ZSgpO1xuICAgICAgICB0aGlzLndyaXRlVTMyKGFycmF5Lmxlbmd0aCk7XG4gICAgICAgIGZvciAoY29uc3QgZWxlbSBvZiBhcnJheSkge1xuICAgICAgICAgICAgdGhpcy5tYXliZVJlc2l6ZSgpO1xuICAgICAgICAgICAgZm4oZWxlbSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgdG9BcnJheSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuYnVmLnN1YmFycmF5KDAsIHRoaXMubGVuZ3RoKTtcbiAgICB9XG59XG5leHBvcnRzLkJpbmFyeVdyaXRlciA9IEJpbmFyeVdyaXRlcjtcbmZ1bmN0aW9uIGhhbmRsaW5nUmFuZ2VFcnJvcih0YXJnZXQsIHByb3BlcnR5S2V5LCBwcm9wZXJ0eURlc2NyaXB0b3IpIHtcbiAgICBjb25zdCBvcmlnaW5hbE1ldGhvZCA9IHByb3BlcnR5RGVzY3JpcHRvci52YWx1ZTtcbiAgICBwcm9wZXJ0eURlc2NyaXB0b3IudmFsdWUgPSBmdW5jdGlvbiAoLi4uYXJncykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsTWV0aG9kLmFwcGx5KHRoaXMsIGFyZ3MpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICBpZiAoZSBpbnN0YW5jZW9mIFJhbmdlRXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBjb2RlID0gZS5jb2RlO1xuICAgICAgICAgICAgICAgIGlmIChbXCJFUlJfQlVGRkVSX09VVF9PRl9CT1VORFNcIiwgXCJFUlJfT1VUX09GX1JBTkdFXCJdLmluZGV4T2YoY29kZSkgPj0gMCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgQm9yc2hFcnJvcihcIlJlYWNoZWQgdGhlIGVuZCBvZiBidWZmZXIgd2hlbiBkZXNlcmlhbGl6aW5nXCIpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRocm93IGU7XG4gICAgICAgIH1cbiAgICB9O1xufVxuY2xhc3MgQmluYXJ5UmVhZGVyIHtcbiAgICBjb25zdHJ1Y3RvcihidWYpIHtcbiAgICAgICAgdGhpcy5idWYgPSBidWY7XG4gICAgICAgIHRoaXMub2Zmc2V0ID0gMDtcbiAgICB9XG4gICAgcmVhZFU4KCkge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IHRoaXMuYnVmLnJlYWRVSW50OCh0aGlzLm9mZnNldCk7XG4gICAgICAgIHRoaXMub2Zmc2V0ICs9IDE7XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gICAgcmVhZFUxNigpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLmJ1Zi5yZWFkVUludDE2TEUodGhpcy5vZmZzZXQpO1xuICAgICAgICB0aGlzLm9mZnNldCArPSAyO1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIHJlYWRVMzIoKSB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gdGhpcy5idWYucmVhZFVJbnQzMkxFKHRoaXMub2Zmc2V0KTtcbiAgICAgICAgdGhpcy5vZmZzZXQgKz0gNDtcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICByZWFkVTY0KCkge1xuICAgICAgICBjb25zdCBidWYgPSB0aGlzLnJlYWRCdWZmZXIoOCk7XG4gICAgICAgIHJldHVybiBuZXcgYm5fanNfMS5kZWZhdWx0KGJ1ZiwgXCJsZVwiKTtcbiAgICB9XG4gICAgcmVhZFUxMjgoKSB7XG4gICAgICAgIGNvbnN0IGJ1ZiA9IHRoaXMucmVhZEJ1ZmZlcigxNik7XG4gICAgICAgIHJldHVybiBuZXcgYm5fanNfMS5kZWZhdWx0KGJ1ZiwgXCJsZVwiKTtcbiAgICB9XG4gICAgcmVhZFUyNTYoKSB7XG4gICAgICAgIGNvbnN0IGJ1ZiA9IHRoaXMucmVhZEJ1ZmZlcigzMik7XG4gICAgICAgIHJldHVybiBuZXcgYm5fanNfMS5kZWZhdWx0KGJ1ZiwgXCJsZVwiKTtcbiAgICB9XG4gICAgcmVhZFU1MTIoKSB7XG4gICAgICAgIGNvbnN0IGJ1ZiA9IHRoaXMucmVhZEJ1ZmZlcig2NCk7XG4gICAgICAgIHJldHVybiBuZXcgYm5fanNfMS5kZWZhdWx0KGJ1ZiwgXCJsZVwiKTtcbiAgICB9XG4gICAgcmVhZEJ1ZmZlcihsZW4pIHtcbiAgICAgICAgaWYgKHRoaXMub2Zmc2V0ICsgbGVuID4gdGhpcy5idWYubGVuZ3RoKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQm9yc2hFcnJvcihgRXhwZWN0ZWQgYnVmZmVyIGxlbmd0aCAke2xlbn0gaXNuJ3Qgd2l0aGluIGJvdW5kc2ApO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMuYnVmLnNsaWNlKHRoaXMub2Zmc2V0LCB0aGlzLm9mZnNldCArIGxlbik7XG4gICAgICAgIHRoaXMub2Zmc2V0ICs9IGxlbjtcbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9XG4gICAgcmVhZFN0cmluZygpIHtcbiAgICAgICAgY29uc3QgbGVuID0gdGhpcy5yZWFkVTMyKCk7XG4gICAgICAgIGNvbnN0IGJ1ZiA9IHRoaXMucmVhZEJ1ZmZlcihsZW4pO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gTk9URTogVXNpbmcgVGV4dERlY29kZXIgdG8gZmFpbCBvbiBpbnZhbGlkIFVURi04XG4gICAgICAgICAgICByZXR1cm4gdGV4dERlY29kZXIuZGVjb2RlKGJ1Zik7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBCb3JzaEVycm9yKGBFcnJvciBkZWNvZGluZyBVVEYtOCBzdHJpbmc6ICR7ZX1gKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZWFkRml4ZWRBcnJheShsZW4pIHtcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KHRoaXMucmVhZEJ1ZmZlcihsZW4pKTtcbiAgICB9XG4gICAgcmVhZEFycmF5KGZuKSB7XG4gICAgICAgIGNvbnN0IGxlbiA9IHRoaXMucmVhZFUzMigpO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBBcnJheSgpO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbjsgKytpKSB7XG4gICAgICAgICAgICByZXN1bHQucHVzaChmbigpKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH1cbn1cbl9fZGVjb3JhdGUoW1xuICAgIGhhbmRsaW5nUmFuZ2VFcnJvclxuXSwgQmluYXJ5UmVhZGVyLnByb3RvdHlwZSwgXCJyZWFkVThcIiwgbnVsbCk7XG5fX2RlY29yYXRlKFtcbiAgICBoYW5kbGluZ1JhbmdlRXJyb3Jcbl0sIEJpbmFyeVJlYWRlci5wcm90b3R5cGUsIFwicmVhZFUxNlwiLCBudWxsKTtcbl9fZGVjb3JhdGUoW1xuICAgIGhhbmRsaW5nUmFuZ2VFcnJvclxuXSwgQmluYXJ5UmVhZGVyLnByb3RvdHlwZSwgXCJyZWFkVTMyXCIsIG51bGwpO1xuX19kZWNvcmF0ZShbXG4gICAgaGFuZGxpbmdSYW5nZUVycm9yXG5dLCBCaW5hcnlSZWFkZXIucHJvdG90eXBlLCBcInJlYWRVNjRcIiwgbnVsbCk7XG5fX2RlY29yYXRlKFtcbiAgICBoYW5kbGluZ1JhbmdlRXJyb3Jcbl0sIEJpbmFyeVJlYWRlci5wcm90b3R5cGUsIFwicmVhZFUxMjhcIiwgbnVsbCk7XG5fX2RlY29yYXRlKFtcbiAgICBoYW5kbGluZ1JhbmdlRXJyb3Jcbl0sIEJpbmFyeVJlYWRlci5wcm90b3R5cGUsIFwicmVhZFUyNTZcIiwgbnVsbCk7XG5fX2RlY29yYXRlKFtcbiAgICBoYW5kbGluZ1JhbmdlRXJyb3Jcbl0sIEJpbmFyeVJlYWRlci5wcm90b3R5cGUsIFwicmVhZFU1MTJcIiwgbnVsbCk7XG5fX2RlY29yYXRlKFtcbiAgICBoYW5kbGluZ1JhbmdlRXJyb3Jcbl0sIEJpbmFyeVJlYWRlci5wcm90b3R5cGUsIFwicmVhZFN0cmluZ1wiLCBudWxsKTtcbl9fZGVjb3JhdGUoW1xuICAgIGhhbmRsaW5nUmFuZ2VFcnJvclxuXSwgQmluYXJ5UmVhZGVyLnByb3RvdHlwZSwgXCJyZWFkRml4ZWRBcnJheVwiLCBudWxsKTtcbl9fZGVjb3JhdGUoW1xuICAgIGhhbmRsaW5nUmFuZ2VFcnJvclxuXSwgQmluYXJ5UmVhZGVyLnByb3RvdHlwZSwgXCJyZWFkQXJyYXlcIiwgbnVsbCk7XG5leHBvcnRzLkJpbmFyeVJlYWRlciA9IEJpbmFyeVJlYWRlcjtcbmZ1bmN0aW9uIGNhcGl0YWxpemVGaXJzdExldHRlcihzdHJpbmcpIHtcbiAgICByZXR1cm4gc3RyaW5nLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgc3RyaW5nLnNsaWNlKDEpO1xufVxuZnVuY3Rpb24gc2VyaWFsaXplRmllbGQoc2NoZW1hLCBmaWVsZE5hbWUsIHZhbHVlLCBmaWVsZFR5cGUsIHdyaXRlcikge1xuICAgIHRyeSB7XG4gICAgICAgIC8vIFRPRE86IEhhbmRsZSBtaXNzaW5nIHZhbHVlcyBwcm9wZXJseSAobWFrZSBzdXJlIHRoZXkgbmV2ZXIgcmVzdWx0IGluIGp1c3Qgc2tpcHBlZCB3cml0ZSlcbiAgICAgICAgaWYgKHR5cGVvZiBmaWVsZFR5cGUgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgICAgIHdyaXRlcltgd3JpdGUke2NhcGl0YWxpemVGaXJzdExldHRlcihmaWVsZFR5cGUpfWBdKHZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChmaWVsZFR5cGUgaW5zdGFuY2VvZiBBcnJheSkge1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBmaWVsZFR5cGVbMF0gPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgICAgICBpZiAodmFsdWUubGVuZ3RoICE9PSBmaWVsZFR5cGVbMF0pIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEJvcnNoRXJyb3IoYEV4cGVjdGluZyBieXRlIGFycmF5IG9mIGxlbmd0aCAke2ZpZWxkVHlwZVswXX0sIGJ1dCBnb3QgJHt2YWx1ZS5sZW5ndGh9IGJ5dGVzYCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHdyaXRlci53cml0ZUZpeGVkQXJyYXkodmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoZmllbGRUeXBlLmxlbmd0aCA9PT0gMiAmJiB0eXBlb2YgZmllbGRUeXBlWzFdID09PSBcIm51bWJlclwiKSB7XG4gICAgICAgICAgICAgICAgaWYgKHZhbHVlLmxlbmd0aCAhPT0gZmllbGRUeXBlWzFdKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBCb3JzaEVycm9yKGBFeHBlY3RpbmcgYnl0ZSBhcnJheSBvZiBsZW5ndGggJHtmaWVsZFR5cGVbMV19LCBidXQgZ290ICR7dmFsdWUubGVuZ3RofSBieXRlc2ApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGZpZWxkVHlwZVsxXTsgaSsrKSB7XG4gICAgICAgICAgICAgICAgICAgIHNlcmlhbGl6ZUZpZWxkKHNjaGVtYSwgbnVsbCwgdmFsdWVbaV0sIGZpZWxkVHlwZVswXSwgd3JpdGVyKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB3cml0ZXIud3JpdGVBcnJheSh2YWx1ZSwgKGl0ZW0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgc2VyaWFsaXplRmllbGQoc2NoZW1hLCBmaWVsZE5hbWUsIGl0ZW0sIGZpZWxkVHlwZVswXSwgd3JpdGVyKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChmaWVsZFR5cGUua2luZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBzd2l0Y2ggKGZpZWxkVHlwZS5raW5kKSB7XG4gICAgICAgICAgICAgICAgY2FzZSBcIm9wdGlvblwiOiB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB3cml0ZXIud3JpdGVVOCgwKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHdyaXRlci53cml0ZVU4KDEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgc2VyaWFsaXplRmllbGQoc2NoZW1hLCBmaWVsZE5hbWUsIHZhbHVlLCBmaWVsZFR5cGUudHlwZSwgd3JpdGVyKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSBcIm1hcFwiOiB7XG4gICAgICAgICAgICAgICAgICAgIHdyaXRlci53cml0ZVUzMih2YWx1ZS5zaXplKTtcbiAgICAgICAgICAgICAgICAgICAgdmFsdWUuZm9yRWFjaCgodmFsLCBrZXkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNlcmlhbGl6ZUZpZWxkKHNjaGVtYSwgZmllbGROYW1lLCBrZXksIGZpZWxkVHlwZS5rZXksIHdyaXRlcik7XG4gICAgICAgICAgICAgICAgICAgICAgICBzZXJpYWxpemVGaWVsZChzY2hlbWEsIGZpZWxkTmFtZSwgdmFsLCBmaWVsZFR5cGUudmFsdWUsIHdyaXRlcik7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEJvcnNoRXJyb3IoYEZpZWxkVHlwZSAke2ZpZWxkVHlwZX0gdW5yZWNvZ25pemVkYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBzZXJpYWxpemVTdHJ1Y3Qoc2NoZW1hLCB2YWx1ZSwgd3JpdGVyKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgQm9yc2hFcnJvcikge1xuICAgICAgICAgICAgZXJyb3IuYWRkVG9GaWVsZFBhdGgoZmllbGROYW1lKTtcbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG59XG5mdW5jdGlvbiBzZXJpYWxpemVTdHJ1Y3Qoc2NoZW1hLCBvYmosIHdyaXRlcikge1xuICAgIGlmICh0eXBlb2Ygb2JqLmJvcnNoU2VyaWFsaXplID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgb2JqLmJvcnNoU2VyaWFsaXplKHdyaXRlcik7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY29uc3Qgc3RydWN0U2NoZW1hID0gc2NoZW1hLmdldChvYmouY29uc3RydWN0b3IpO1xuICAgIGlmICghc3RydWN0U2NoZW1hKSB7XG4gICAgICAgIHRocm93IG5ldyBCb3JzaEVycm9yKGBDbGFzcyAke29iai5jb25zdHJ1Y3Rvci5uYW1lfSBpcyBtaXNzaW5nIGluIHNjaGVtYWApO1xuICAgIH1cbiAgICBpZiAoc3RydWN0U2NoZW1hLmtpbmQgPT09IFwic3RydWN0XCIpIHtcbiAgICAgICAgc3RydWN0U2NoZW1hLmZpZWxkcy5tYXAoKFtmaWVsZE5hbWUsIGZpZWxkVHlwZV0pID0+IHtcbiAgICAgICAgICAgIHNlcmlhbGl6ZUZpZWxkKHNjaGVtYSwgZmllbGROYW1lLCBvYmpbZmllbGROYW1lXSwgZmllbGRUeXBlLCB3cml0ZXIpO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgZWxzZSBpZiAoc3RydWN0U2NoZW1hLmtpbmQgPT09IFwiZW51bVwiKSB7XG4gICAgICAgIGNvbnN0IG5hbWUgPSBvYmpbc3RydWN0U2NoZW1hLmZpZWxkXTtcbiAgICAgICAgZm9yIChsZXQgaWR4ID0gMDsgaWR4IDwgc3RydWN0U2NoZW1hLnZhbHVlcy5sZW5ndGg7ICsraWR4KSB7XG4gICAgICAgICAgICBjb25zdCBbZmllbGROYW1lLCBmaWVsZFR5cGVdID0gc3RydWN0U2NoZW1hLnZhbHVlc1tpZHhdO1xuICAgICAgICAgICAgaWYgKGZpZWxkTmFtZSA9PT0gbmFtZSkge1xuICAgICAgICAgICAgICAgIHdyaXRlci53cml0ZVU4KGlkeCk7XG4gICAgICAgICAgICAgICAgc2VyaWFsaXplRmllbGQoc2NoZW1hLCBmaWVsZE5hbWUsIG9ialtmaWVsZE5hbWVdLCBmaWVsZFR5cGUsIHdyaXRlcik7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBCb3JzaEVycm9yKGBVbmV4cGVjdGVkIHNjaGVtYSBraW5kOiAke3N0cnVjdFNjaGVtYS5raW5kfSBmb3IgJHtvYmouY29uc3RydWN0b3IubmFtZX1gKTtcbiAgICB9XG59XG4vLy8gU2VyaWFsaXplIGdpdmVuIG9iamVjdCB1c2luZyBzY2hlbWEgb2YgdGhlIGZvcm06XG4vLy8geyBjbGFzc19uYW1lIC0+IFsgW2ZpZWxkX25hbWUsIGZpZWxkX3R5cGVdLCAuLiBdLCAuLiB9XG5mdW5jdGlvbiBzZXJpYWxpemUoc2NoZW1hLCBvYmosIFdyaXRlciA9IEJpbmFyeVdyaXRlcikge1xuICAgIGNvbnN0IHdyaXRlciA9IG5ldyBXcml0ZXIoKTtcbiAgICBzZXJpYWxpemVTdHJ1Y3Qoc2NoZW1hLCBvYmosIHdyaXRlcik7XG4gICAgcmV0dXJuIHdyaXRlci50b0FycmF5KCk7XG59XG5leHBvcnRzLnNlcmlhbGl6ZSA9IHNlcmlhbGl6ZTtcbmZ1bmN0aW9uIGRlc2VyaWFsaXplRmllbGQoc2NoZW1hLCBmaWVsZE5hbWUsIGZpZWxkVHlwZSwgcmVhZGVyKSB7XG4gICAgdHJ5IHtcbiAgICAgICAgaWYgKHR5cGVvZiBmaWVsZFR5cGUgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgICAgIHJldHVybiByZWFkZXJbYHJlYWQke2NhcGl0YWxpemVGaXJzdExldHRlcihmaWVsZFR5cGUpfWBdKCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGZpZWxkVHlwZSBpbnN0YW5jZW9mIEFycmF5KSB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIGZpZWxkVHlwZVswXSA9PT0gXCJudW1iZXJcIikge1xuICAgICAgICAgICAgICAgIHJldHVybiByZWFkZXIucmVhZEZpeGVkQXJyYXkoZmllbGRUeXBlWzBdKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKHR5cGVvZiBmaWVsZFR5cGVbMV0gPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBhcnIgPSBbXTtcbiAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGZpZWxkVHlwZVsxXTsgaSsrKSB7XG4gICAgICAgICAgICAgICAgICAgIGFyci5wdXNoKGRlc2VyaWFsaXplRmllbGQoc2NoZW1hLCBudWxsLCBmaWVsZFR5cGVbMF0sIHJlYWRlcikpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gYXJyO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlYWRlci5yZWFkQXJyYXkoKCkgPT4gZGVzZXJpYWxpemVGaWVsZChzY2hlbWEsIGZpZWxkTmFtZSwgZmllbGRUeXBlWzBdLCByZWFkZXIpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoZmllbGRUeXBlLmtpbmQgPT09IFwib3B0aW9uXCIpIHtcbiAgICAgICAgICAgIGNvbnN0IG9wdGlvbiA9IHJlYWRlci5yZWFkVTgoKTtcbiAgICAgICAgICAgIGlmIChvcHRpb24pIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZGVzZXJpYWxpemVGaWVsZChzY2hlbWEsIGZpZWxkTmFtZSwgZmllbGRUeXBlLnR5cGUsIHJlYWRlcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgICAgICB9XG4gICAgICAgIGlmIChmaWVsZFR5cGUua2luZCA9PT0gXCJtYXBcIikge1xuICAgICAgICAgICAgbGV0IG1hcCA9IG5ldyBNYXAoKTtcbiAgICAgICAgICAgIGNvbnN0IGxlbmd0aCA9IHJlYWRlci5yZWFkVTMyKCk7XG4gICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qga2V5ID0gZGVzZXJpYWxpemVGaWVsZChzY2hlbWEsIGZpZWxkTmFtZSwgZmllbGRUeXBlLmtleSwgcmVhZGVyKTtcbiAgICAgICAgICAgICAgICBjb25zdCB2YWwgPSBkZXNlcmlhbGl6ZUZpZWxkKHNjaGVtYSwgZmllbGROYW1lLCBmaWVsZFR5cGUudmFsdWUsIHJlYWRlcik7XG4gICAgICAgICAgICAgICAgbWFwLnNldChrZXksIHZhbCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gbWFwO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBkZXNlcmlhbGl6ZVN0cnVjdChzY2hlbWEsIGZpZWxkVHlwZSwgcmVhZGVyKTtcbiAgICB9XG4gICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEJvcnNoRXJyb3IpIHtcbiAgICAgICAgICAgIGVycm9yLmFkZFRvRmllbGRQYXRoKGZpZWxkTmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxufVxuZnVuY3Rpb24gZGVzZXJpYWxpemVTdHJ1Y3Qoc2NoZW1hLCBjbGFzc1R5cGUsIHJlYWRlcikge1xuICAgIGlmICh0eXBlb2YgY2xhc3NUeXBlLmJvcnNoRGVzZXJpYWxpemUgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICByZXR1cm4gY2xhc3NUeXBlLmJvcnNoRGVzZXJpYWxpemUocmVhZGVyKTtcbiAgICB9XG4gICAgY29uc3Qgc3RydWN0U2NoZW1hID0gc2NoZW1hLmdldChjbGFzc1R5cGUpO1xuICAgIGlmICghc3RydWN0U2NoZW1hKSB7XG4gICAgICAgIHRocm93IG5ldyBCb3JzaEVycm9yKGBDbGFzcyAke2NsYXNzVHlwZS5uYW1lfSBpcyBtaXNzaW5nIGluIHNjaGVtYWApO1xuICAgIH1cbiAgICBpZiAoc3RydWN0U2NoZW1hLmtpbmQgPT09IFwic3RydWN0XCIpIHtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0ge307XG4gICAgICAgIGZvciAoY29uc3QgW2ZpZWxkTmFtZSwgZmllbGRUeXBlXSBvZiBzY2hlbWEuZ2V0KGNsYXNzVHlwZSkuZmllbGRzKSB7XG4gICAgICAgICAgICByZXN1bHRbZmllbGROYW1lXSA9IGRlc2VyaWFsaXplRmllbGQoc2NoZW1hLCBmaWVsZE5hbWUsIGZpZWxkVHlwZSwgcmVhZGVyKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3IGNsYXNzVHlwZShyZXN1bHQpO1xuICAgIH1cbiAgICBpZiAoc3RydWN0U2NoZW1hLmtpbmQgPT09IFwiZW51bVwiKSB7XG4gICAgICAgIGNvbnN0IGlkeCA9IHJlYWRlci5yZWFkVTgoKTtcbiAgICAgICAgaWYgKGlkeCA+PSBzdHJ1Y3RTY2hlbWEudmFsdWVzLmxlbmd0aCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEJvcnNoRXJyb3IoYEVudW0gaW5kZXg6ICR7aWR4fSBpcyBvdXQgb2YgcmFuZ2VgKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBbZmllbGROYW1lLCBmaWVsZFR5cGVdID0gc3RydWN0U2NoZW1hLnZhbHVlc1tpZHhdO1xuICAgICAgICBjb25zdCBmaWVsZFZhbHVlID0gZGVzZXJpYWxpemVGaWVsZChzY2hlbWEsIGZpZWxkTmFtZSwgZmllbGRUeXBlLCByZWFkZXIpO1xuICAgICAgICByZXR1cm4gbmV3IGNsYXNzVHlwZSh7IFtmaWVsZE5hbWVdOiBmaWVsZFZhbHVlIH0pO1xuICAgIH1cbiAgICB0aHJvdyBuZXcgQm9yc2hFcnJvcihgVW5leHBlY3RlZCBzY2hlbWEga2luZDogJHtzdHJ1Y3RTY2hlbWEua2luZH0gZm9yICR7Y2xhc3NUeXBlLmNvbnN0cnVjdG9yLm5hbWV9YCk7XG59XG4vLy8gRGVzZXJpYWxpemVzIG9iamVjdCBmcm9tIGJ5dGVzIHVzaW5nIHNjaGVtYS5cbmZ1bmN0aW9uIGRlc2VyaWFsaXplKHNjaGVtYSwgY2xhc3NUeXBlLCBidWZmZXIsIFJlYWRlciA9IEJpbmFyeVJlYWRlcikge1xuICAgIGNvbnN0IHJlYWRlciA9IG5ldyBSZWFkZXIoYnVmZmVyKTtcbiAgICBjb25zdCByZXN1bHQgPSBkZXNlcmlhbGl6ZVN0cnVjdChzY2hlbWEsIGNsYXNzVHlwZSwgcmVhZGVyKTtcbiAgICBpZiAocmVhZGVyLm9mZnNldCA8IGJ1ZmZlci5sZW5ndGgpIHtcbiAgICAgICAgdGhyb3cgbmV3IEJvcnNoRXJyb3IoYFVuZXhwZWN0ZWQgJHtidWZmZXIubGVuZ3RoIC0gcmVhZGVyLm9mZnNldH0gYnl0ZXMgYWZ0ZXIgZGVzZXJpYWxpemVkIGRhdGFgKTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cbmV4cG9ydHMuZGVzZXJpYWxpemUgPSBkZXNlcmlhbGl6ZTtcbi8vLyBEZXNlcmlhbGl6ZXMgb2JqZWN0IGZyb20gYnl0ZXMgdXNpbmcgc2NoZW1hLCB3aXRob3V0IGNoZWNraW5nIHRoZSBsZW5ndGggcmVhZFxuZnVuY3Rpb24gZGVzZXJpYWxpemVVbmNoZWNrZWQoc2NoZW1hLCBjbGFzc1R5cGUsIGJ1ZmZlciwgUmVhZGVyID0gQmluYXJ5UmVhZGVyKSB7XG4gICAgY29uc3QgcmVhZGVyID0gbmV3IFJlYWRlcihidWZmZXIpO1xuICAgIHJldHVybiBkZXNlcmlhbGl6ZVN0cnVjdChzY2hlbWEsIGNsYXNzVHlwZSwgcmVhZGVyKTtcbn1cbmV4cG9ydHMuZGVzZXJpYWxpemVVbmNoZWNrZWQgPSBkZXNlcmlhbGl6ZVVuY2hlY2tlZDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/borsh/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/borsh/node_modules/base-x/src/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/borsh/node_modules/base-x/src/index.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\n// @ts-ignore\nvar _Buffer = (__webpack_require__(/*! safe-buffer */ \"(rsc)/./node_modules/safe-buffer/index.js\").Buffer)\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  var BASE_MAP = new Uint8Array(256)\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i)\n    var xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  var BASE = ALPHABET.length\n  var LEADER = ALPHABET.charAt(0)\n  var FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  var iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    if (Array.isArray(source) || source instanceof Uint8Array) { source = _Buffer.from(source) }\n    if (!_Buffer.isBuffer(source)) { throw new TypeError('Expected Buffer') }\n    if (source.length === 0) { return '' }\n        // Skip & count leading zeroes.\n    var zeroes = 0\n    var length = 0\n    var pbegin = 0\n    var pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n        // Allocate enough space in big-endian base58 representation.\n    var size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    var b58 = new Uint8Array(size)\n        // Process the bytes.\n    while (pbegin !== pend) {\n      var carry = source[pbegin]\n            // Apply \"b58 = b58 * 256 + ch\".\n      var i = 0\n      for (var it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n        // Skip leading zeroes in base58 result.\n    var it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n        // Translate the result into a string.\n    var str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return _Buffer.alloc(0) }\n    var psz = 0\n        // Skip and count leading '1's.\n    var zeroes = 0\n    var length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n        // Allocate enough space in big-endian base256 representation.\n    var size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    var b256 = new Uint8Array(size)\n        // Process the characters.\n    while (psz < source.length) {\n            // Find code of next character\n      var charCode = source.charCodeAt(psz)\n            // Base map can not be indexed using char code\n      if (charCode > 255) { return }\n            // Decode character\n      var carry = BASE_MAP[charCode]\n            // Invalid character\n      if (carry === 255) { return }\n      var i = 0\n      for (var it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n        // Skip leading zeroes in b256.\n    var it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    var vch = _Buffer.allocUnsafe(zeroes + (size - it4))\n    vch.fill(0x00, 0, zeroes)\n    var j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    var buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  }\n}\nmodule.exports = base\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/borsh/node_modules/base-x/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/borsh/node_modules/bs58/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/borsh/node_modules/bs58/index.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var basex = __webpack_require__(/*! base-x */ \"(rsc)/./node_modules/borsh/node_modules/base-x/src/index.js\")\nvar ALPHABET = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'\n\nmodule.exports = basex(ALPHABET)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYm9yc2gvbm9kZV9tb2R1bGVzL2JzNTgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsWUFBWSxtQkFBTyxDQUFDLDJFQUFRO0FBQzVCOztBQUVBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9ib3JzaC9ub2RlX21vZHVsZXMvYnM1OC9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgYmFzZXggPSByZXF1aXJlKCdiYXNlLXgnKVxudmFyIEFMUEhBQkVUID0gJzEyMzQ1Njc4OUFCQ0RFRkdISktMTU5QUVJTVFVWV1hZWmFiY2RlZmdoaWprbW5vcHFyc3R1dnd4eXonXG5cbm1vZHVsZS5leHBvcnRzID0gYmFzZXgoQUxQSEFCRVQpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/borsh/node_modules/bs58/index.js\n");

/***/ })

};
;