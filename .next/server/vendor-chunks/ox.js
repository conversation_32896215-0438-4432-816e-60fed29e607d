"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ox";
exports.ids = ["vendor-chunks/ox"];
exports.modules = {

/***/ "(ssr)/./node_modules/ox/_esm/core/BlockOverrides.js":
/*!*****************************************************!*\
  !*** ./node_modules/ox/_esm/core/BlockOverrides.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromRpc: () => (/* binding */ fromRpc),\n/* harmony export */   toRpc: () => (/* binding */ toRpc)\n/* harmony export */ });\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n/* harmony import */ var _Withdrawal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Withdrawal.js */ \"(ssr)/./node_modules/ox/_esm/core/Withdrawal.js\");\n\n\n/**\n * Converts an {@link ox#BlockOverrides.Rpc} to an {@link ox#BlockOverrides.BlockOverrides}.\n *\n * @example\n * ```ts twoslash\n * import { BlockOverrides } from 'ox'\n *\n * const blockOverrides = BlockOverrides.fromRpc({\n *   baseFeePerGas: '0x1',\n *   blobBaseFee: '0x2',\n *   feeRecipient: '0x0000000000000000000000000000000000000000',\n *   gasLimit: '0x4',\n *   number: '0x5',\n *   prevRandao: '0x6',\n *   time: '0x1234567890',\n *   withdrawals: [\n *     {\n *       address: '0x0000000000000000000000000000000000000000',\n *       amount: '0x1',\n *       index: '0x0',\n *       validatorIndex: '0x1',\n *     },\n *   ],\n * })\n * ```\n *\n * @param rpcBlockOverrides - The RPC block overrides to convert.\n * @returns An instantiated {@link ox#BlockOverrides.BlockOverrides}.\n */\nfunction fromRpc(rpcBlockOverrides) {\n    return {\n        ...(rpcBlockOverrides.baseFeePerGas && {\n            baseFeePerGas: BigInt(rpcBlockOverrides.baseFeePerGas),\n        }),\n        ...(rpcBlockOverrides.blobBaseFee && {\n            blobBaseFee: BigInt(rpcBlockOverrides.blobBaseFee),\n        }),\n        ...(rpcBlockOverrides.feeRecipient && {\n            feeRecipient: rpcBlockOverrides.feeRecipient,\n        }),\n        ...(rpcBlockOverrides.gasLimit && {\n            gasLimit: BigInt(rpcBlockOverrides.gasLimit),\n        }),\n        ...(rpcBlockOverrides.number && {\n            number: BigInt(rpcBlockOverrides.number),\n        }),\n        ...(rpcBlockOverrides.prevRandao && {\n            prevRandao: BigInt(rpcBlockOverrides.prevRandao),\n        }),\n        ...(rpcBlockOverrides.time && {\n            time: BigInt(rpcBlockOverrides.time),\n        }),\n        ...(rpcBlockOverrides.withdrawals && {\n            withdrawals: rpcBlockOverrides.withdrawals.map(_Withdrawal_js__WEBPACK_IMPORTED_MODULE_0__.fromRpc),\n        }),\n    };\n}\n/**\n * Converts an {@link ox#BlockOverrides.BlockOverrides} to an {@link ox#BlockOverrides.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { BlockOverrides } from 'ox'\n *\n * const blockOverrides = BlockOverrides.toRpc({\n *   baseFeePerGas: 1n,\n *   blobBaseFee: 2n,\n *   feeRecipient: '0x0000000000000000000000000000000000000000',\n *   gasLimit: 4n,\n *   number: 5n,\n *   prevRandao: 6n,\n *   time: 78187493520n,\n *   withdrawals: [\n *     {\n *       address: '0x0000000000000000000000000000000000000000',\n *       amount: 1n,\n *       index: 0,\n *       validatorIndex: 1,\n *     },\n *   ],\n * })\n * ```\n *\n * @param blockOverrides - The block overrides to convert.\n * @returns An instantiated {@link ox#BlockOverrides.Rpc}.\n */\nfunction toRpc(blockOverrides) {\n    return {\n        ...(typeof blockOverrides.baseFeePerGas === 'bigint' && {\n            baseFeePerGas: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.baseFeePerGas),\n        }),\n        ...(typeof blockOverrides.blobBaseFee === 'bigint' && {\n            blobBaseFee: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.blobBaseFee),\n        }),\n        ...(typeof blockOverrides.feeRecipient === 'string' && {\n            feeRecipient: blockOverrides.feeRecipient,\n        }),\n        ...(typeof blockOverrides.gasLimit === 'bigint' && {\n            gasLimit: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.gasLimit),\n        }),\n        ...(typeof blockOverrides.number === 'bigint' && {\n            number: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.number),\n        }),\n        ...(typeof blockOverrides.prevRandao === 'bigint' && {\n            prevRandao: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.prevRandao),\n        }),\n        ...(typeof blockOverrides.time === 'bigint' && {\n            time: _Hex_js__WEBPACK_IMPORTED_MODULE_1__.fromNumber(blockOverrides.time),\n        }),\n        ...(blockOverrides.withdrawals && {\n            withdrawals: blockOverrides.withdrawals.map(_Withdrawal_js__WEBPACK_IMPORTED_MODULE_0__.toRpc),\n        }),\n    };\n}\n//# sourceMappingURL=BlockOverrides.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/BlockOverrides.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Bytes.js":
/*!********************************************!*\
  !*** ./node_modules/ox/_esm/core/Bytes.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidBytesBooleanError: () => (/* binding */ InvalidBytesBooleanError),\n/* harmony export */   InvalidBytesTypeError: () => (/* binding */ InvalidBytesTypeError),\n/* harmony export */   SizeExceedsPaddingSizeError: () => (/* binding */ SizeExceedsPaddingSizeError),\n/* harmony export */   SizeOverflowError: () => (/* binding */ SizeOverflowError),\n/* harmony export */   SliceOffsetOutOfBoundsError: () => (/* binding */ SliceOffsetOutOfBoundsError),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   fromArray: () => (/* binding */ fromArray),\n/* harmony export */   fromBoolean: () => (/* binding */ fromBoolean),\n/* harmony export */   fromHex: () => (/* binding */ fromHex),\n/* harmony export */   fromNumber: () => (/* binding */ fromNumber),\n/* harmony export */   fromString: () => (/* binding */ fromString),\n/* harmony export */   isEqual: () => (/* binding */ isEqual),\n/* harmony export */   padLeft: () => (/* binding */ padLeft),\n/* harmony export */   padRight: () => (/* binding */ padRight),\n/* harmony export */   random: () => (/* binding */ random),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   slice: () => (/* binding */ slice),\n/* harmony export */   toBigInt: () => (/* binding */ toBigInt),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toHex: () => (/* binding */ toHex),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toString: () => (/* binding */ toString),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   trimRight: () => (/* binding */ trimRight),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/* harmony import */ var _noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @noble/curves/abstract/utils */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _Errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_esm/core/Errors.js\");\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n/* harmony import */ var _Json_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Json.js */ \"(ssr)/./node_modules/ox/_esm/core/Json.js\");\n/* harmony import */ var _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/bytes.js\");\n/* harmony import */ var _internal_hex_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internal/hex.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/hex.js\");\n\n\n\n\n\n\nconst decoder = /*#__PURE__*/ new TextDecoder();\nconst encoder = /*#__PURE__*/ new TextEncoder();\n/**\n * Asserts if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.assert('abc')\n * // @error: Bytes.InvalidBytesTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid Bytes value.\n * // @error: Bytes values must be of type `Uint8Array`.\n * ```\n *\n * @param value - Value to assert.\n */\nfunction assert(value) {\n    if (value instanceof Uint8Array)\n        return;\n    if (!value)\n        throw new InvalidBytesTypeError(value);\n    if (typeof value !== 'object')\n        throw new InvalidBytesTypeError(value);\n    if (!('BYTES_PER_ELEMENT' in value))\n        throw new InvalidBytesTypeError(value);\n    if (value.BYTES_PER_ELEMENT !== 1 || value.constructor.name !== 'Uint8Array')\n        throw new InvalidBytesTypeError(value);\n}\n/**\n * Concatenates two or more {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.concat(\n *   Bytes.from([1]),\n *   Bytes.from([69]),\n *   Bytes.from([420, 69]),\n * )\n * // @log: Uint8Array [ 1, 69, 420, 69 ]\n * ```\n *\n * @param values - Values to concatenate.\n * @returns Concatenated {@link ox#Bytes.Bytes}.\n */\nfunction concat(...values) {\n    let length = 0;\n    for (const arr of values) {\n        length += arr.length;\n    }\n    const result = new Uint8Array(length);\n    for (let i = 0, index = 0; i < values.length; i++) {\n        const arr = values[i];\n        result.set(arr, index);\n        index += arr.length;\n    }\n    return result;\n}\n/**\n * Instantiates a {@link ox#Bytes.Bytes} value from a `Uint8Array`, a hex string, or an array of unsigned 8-bit integers.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Bytes.fromBoolean`\n *\n * - `Bytes.fromString`\n *\n * - `Bytes.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.from([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n *\n * const data = Bytes.from('0xdeadbeef')\n * // @log: Uint8Array([222, 173, 190, 239])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nfunction from(value) {\n    if (value instanceof Uint8Array)\n        return value;\n    if (typeof value === 'string')\n        return fromHex(value);\n    return fromArray(value);\n}\n/**\n * Converts an array of unsigned 8-bit integers into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromArray([255, 124, 5, 4])\n * // @log: Uint8Array([255, 124, 5, 4])\n * ```\n *\n * @param value - Value to convert.\n * @returns A {@link ox#Bytes.Bytes} instance.\n */\nfunction fromArray(value) {\n    return value instanceof Uint8Array ? value : new Uint8Array(value);\n}\n/**\n * Encodes a boolean value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true)\n * // @log: Uint8Array([1])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromBoolean(true, { size: 32 })\n * // @log: Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n * ```\n *\n * @param value - Boolean value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromBoolean(value, options = {}) {\n    const { size } = options;\n    const bytes = new Uint8Array(1);\n    bytes[0] = Number(value);\n    if (typeof size === 'number') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n        return padLeft(bytes, size);\n    }\n    return bytes;\n}\n/**\n * Encodes a {@link ox#Hex.Hex} value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Hex.Hex} value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromHex(value, options = {}) {\n    const { size } = options;\n    let hex = value;\n    if (size) {\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_1__.assertSize(value, size);\n        hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.padRight(value, size);\n    }\n    let hexString = hex.slice(2);\n    if (hexString.length % 2)\n        hexString = `0${hexString}`;\n    const length = hexString.length / 2;\n    const bytes = new Uint8Array(length);\n    for (let index = 0, j = 0; index < length; index++) {\n        const nibbleLeft = _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.charCodeToBase16(hexString.charCodeAt(j++));\n        const nibbleRight = _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.charCodeToBase16(hexString.charCodeAt(j++));\n        if (nibbleLeft === undefined || nibbleRight === undefined) {\n            throw new _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError(`Invalid byte sequence (\"${hexString[j - 2]}${hexString[j - 1]}\" in \"${hexString}\").`);\n        }\n        bytes[index] = nibbleLeft * 16 + nibbleRight;\n    }\n    return bytes;\n}\n/**\n * Encodes a number value into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420)\n * // @log: Uint8Array([1, 164])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromNumber(420, { size: 4 })\n * // @log: Uint8Array([0, 0, 1, 164])\n * ```\n *\n * @param value - Number value to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromNumber(value, options) {\n    const hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromNumber(value, options);\n    return fromHex(hex);\n}\n/**\n * Encodes a string into {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.fromString('Hello world!', { size: 32 })\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n * ```\n *\n * @param value - String to encode.\n * @param options - Encoding options.\n * @returns Encoded {@link ox#Bytes.Bytes}.\n */\nfunction fromString(value, options = {}) {\n    const { size } = options;\n    const bytes = encoder.encode(value);\n    if (typeof size === 'number') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n        return padRight(bytes, size);\n    }\n    return bytes;\n}\n/**\n * Checks if two {@link ox#Bytes.Bytes} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([1]))\n * // @log: true\n *\n * Bytes.isEqual(Bytes.from([1]), Bytes.from([2]))\n * // @log: false\n * ```\n *\n * @param bytesA - First {@link ox#Bytes.Bytes} value.\n * @param bytesB - Second {@link ox#Bytes.Bytes} value.\n * @returns `true` if the two values are equal, otherwise `false`.\n */\nfunction isEqual(bytesA, bytesB) {\n    return (0,_noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_4__.equalBytes)(bytesA, bytesB);\n}\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.from([1]), 4)\n * // @log: Uint8Array([0, 0, 0, 1])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nfunction padLeft(value, size) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'left', size });\n}\n/**\n * Pads a {@link ox#Bytes.Bytes} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padRight(Bytes.from([1]), 4)\n * // @log: Uint8Array([1, 0, 0, 0])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value to pad.\n * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.\n * @returns Padded {@link ox#Bytes.Bytes} value.\n */\nfunction padRight(value, size) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'right', size });\n}\n/**\n * Generates random {@link ox#Bytes.Bytes} of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const bytes = Bytes.random(32)\n * // @log: Uint8Array([... x32])\n * ```\n *\n * @param length - Length of the random {@link ox#Bytes.Bytes} to generate.\n * @returns Random {@link ox#Bytes.Bytes} of the specified length.\n */\nfunction random(length) {\n    return crypto.getRandomValues(new Uint8Array(length));\n}\n/**\n * Retrieves the size of a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.size(Bytes.from([1, 2, 3, 4]))\n * // @log: 4\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Size of the {@link ox#Bytes.Bytes} value.\n */\nfunction size(value) {\n    return value.length;\n}\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(\n *   Bytes.from([1, 2, 3, 4, 5, 6, 7, 8, 9]),\n *   1,\n *   4,\n * )\n * // @log: Uint8Array([2, 3, 4])\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value.\n * @param start - Start offset.\n * @param end - End offset.\n * @param options - Slice options.\n * @returns Sliced {@link ox#Bytes.Bytes} value.\n */\nfunction slice(value, start, end, options = {}) {\n    const { strict } = options;\n    _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertStartOffset(value, start);\n    const value_ = value.slice(start, end);\n    if (strict)\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertEndOffset(value_, start, end);\n    return value_;\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a bigint.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBigInt(Bytes.from([1, 164]))\n * // @log: 420n\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded bigint.\n */\nfunction toBigInt(bytes, options = {}) {\n    const { size } = options;\n    if (typeof size !== 'undefined')\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n    const hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromBytes(bytes, options);\n    return _Hex_js__WEBPACK_IMPORTED_MODULE_2__.toBigInt(hex, options);\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a boolean.\n *\n * @example\n * ```ts\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([1]))\n * // @log: true\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Decoding options.\n * @returns Decoded boolean.\n */\nfunction toBoolean(bytes, options = {}) {\n    const { size } = options;\n    let bytes_ = bytes;\n    if (typeof size !== 'undefined') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes_, size);\n        bytes_ = trimLeft(bytes_);\n    }\n    if (bytes_.length > 1 || bytes_[0] > 1)\n        throw new InvalidBytesBooleanError(bytes_);\n    return Boolean(bytes_[0]);\n}\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toHex(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded {@link ox#Hex.Hex} value.\n */\nfunction toHex(value, options = {}) {\n    return _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromBytes(value, options);\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a number.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toNumber(Bytes.from([1, 164]))\n * // @log: 420\n * ```\n */\nfunction toNumber(bytes, options = {}) {\n    const { size } = options;\n    if (typeof size !== 'undefined')\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes, size);\n    const hex = _Hex_js__WEBPACK_IMPORTED_MODULE_2__.fromBytes(bytes, options);\n    return _Hex_js__WEBPACK_IMPORTED_MODULE_2__.toNumber(hex, options);\n}\n/**\n * Decodes a {@link ox#Bytes.Bytes} into a string.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * const data = Bytes.toString(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: 'Hello world'\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} to decode.\n * @param options - Options.\n * @returns Decoded string.\n */\nfunction toString(bytes, options = {}) {\n    const { size } = options;\n    let bytes_ = bytes;\n    if (typeof size !== 'undefined') {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(bytes_, size);\n        bytes_ = trimRight(bytes_);\n    }\n    return decoder.decode(bytes_);\n}\n/**\n * Trims leading zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimLeft(Bytes.from([0, 0, 0, 0, 1, 2, 3]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nfunction trimLeft(value) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'left' });\n}\n/**\n * Trims trailing zeros from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.trimRight(Bytes.from([1, 2, 3, 0, 0, 0, 0]))\n * // @log: Uint8Array([1, 2, 3])\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} value.\n * @returns Trimmed {@link ox#Bytes.Bytes} value.\n */\nfunction trimRight(value) {\n    return _internal_bytes_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'right' });\n}\n/**\n * Checks if the given value is {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.validate('0x')\n * // @log: false\n *\n * Bytes.validate(Bytes.from([1, 2, 3]))\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns `true` if the value is {@link ox#Bytes.Bytes}, otherwise `false`.\n */\nfunction validate(value) {\n    try {\n        assert(value);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\n/**\n * Thrown when the bytes value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.toBoolean(Bytes.from([5]))\n * // @error: Bytes.InvalidBytesBooleanError: Bytes value `[5]` is not a valid boolean.\n * // @error: The bytes array must contain a single byte of either a `0` or `1` value.\n * ```\n */\nclass InvalidBytesBooleanError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor(bytes) {\n        super(`Bytes value \\`${bytes}\\` is not a valid boolean.`, {\n            metaMessages: [\n                'The bytes array must contain a single byte of either a `0` or `1` value.',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.InvalidBytesBooleanError'\n        });\n    }\n}\n/**\n * Thrown when a value cannot be converted to bytes.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Bytes } from 'ox'\n *\n * Bytes.from('foo')\n * // @error: Bytes.InvalidBytesTypeError: Value `foo` of type `string` is an invalid Bytes value.\n * ```\n */\nclass InvalidBytesTypeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor(value) {\n        super(`Value \\`${typeof value === 'object' ? _Json_js__WEBPACK_IMPORTED_MODULE_5__.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid Bytes value.`, {\n            metaMessages: ['Bytes values must be of type `Bytes`.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.InvalidBytesTypeError'\n        });\n    }\n}\n/**\n * Thrown when a size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromString('Hello World!', { size: 8 })\n * // @error: Bytes.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nclass SizeOverflowError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SizeOverflowError'\n        });\n    }\n}\n/**\n * Thrown when a slice offset is out-of-bounds.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.slice(Bytes.from([1, 2, 3]), 4)\n * // @error: Bytes.SliceOffsetOutOfBoundsError: Slice starting at offset `4` is out-of-bounds (size: `3`).\n * ```\n */\nclass SliceOffsetOutOfBoundsError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SliceOffsetOutOfBoundsError'\n        });\n    }\n}\n/**\n * Thrown when a the padding size exceeds the maximum allowed size.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.padLeft(Bytes.fromString('Hello World!'), 8)\n * // @error: [Bytes.SizeExceedsPaddingSizeError: Bytes size (`12`) exceeds padding size (`8`).\n * ```\n */\nclass SizeExceedsPaddingSizeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_3__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Bytes.SizeExceedsPaddingSizeError'\n        });\n    }\n}\n//# sourceMappingURL=Bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Errors.js":
/*!*********************************************!*\
  !*** ./node_modules/ox/_esm/core/Errors.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseError: () => (/* binding */ BaseError)\n/* harmony export */ });\n/* harmony import */ var _internal_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/errors.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/errors.js\");\n\n/**\n * Base error class inherited by all errors thrown by ox.\n *\n * @example\n * ```ts\n * import { Errors } from 'ox'\n * throw new Errors.BaseError('An error occurred')\n * ```\n */\nclass BaseError extends Error {\n    constructor(shortMessage, options = {}) {\n        const details = (() => {\n            if (options.cause instanceof BaseError) {\n                if (options.cause.details)\n                    return options.cause.details;\n                if (options.cause.shortMessage)\n                    return options.cause.shortMessage;\n            }\n            if (options.cause &&\n                'details' in options.cause &&\n                typeof options.cause.details === 'string')\n                return options.cause.details;\n            if (options.cause?.message)\n                return options.cause.message;\n            return options.details;\n        })();\n        const docsPath = (() => {\n            if (options.cause instanceof BaseError)\n                return options.cause.docsPath || options.docsPath;\n            return options.docsPath;\n        })();\n        const docsBaseUrl = 'https://oxlib.sh';\n        const docs = `${docsBaseUrl}${docsPath ?? ''}`;\n        const message = [\n            shortMessage || 'An error occurred.',\n            ...(options.metaMessages ? ['', ...options.metaMessages] : []),\n            ...(details || docsPath\n                ? [\n                    '',\n                    details ? `Details: ${details}` : undefined,\n                    docsPath ? `See: ${docs}` : undefined,\n                ]\n                : []),\n        ]\n            .filter((x) => typeof x === 'string')\n            .join('\\n');\n        super(message, options.cause ? { cause: options.cause } : undefined);\n        Object.defineProperty(this, \"details\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"docsPath\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"shortMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"cause\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'BaseError'\n        });\n        Object.defineProperty(this, \"version\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: `ox@${(0,_internal_errors_js__WEBPACK_IMPORTED_MODULE_0__.getVersion)()}`\n        });\n        this.cause = options.cause;\n        this.details = details;\n        this.docs = docs;\n        this.docsPath = docsPath;\n        this.shortMessage = shortMessage;\n    }\n    walk(fn) {\n        return walk(this, fn);\n    }\n}\n/** @internal */\nfunction walk(err, fn) {\n    if (fn?.(err))\n        return err;\n    if (err && typeof err === 'object' && 'cause' in err && err.cause)\n        return walk(err.cause, fn);\n    return fn ? null : err;\n}\n//# sourceMappingURL=Errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Hex.js":
/*!******************************************!*\
  !*** ./node_modules/ox/_esm/core/Hex.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegerOutOfRangeError: () => (/* binding */ IntegerOutOfRangeError),\n/* harmony export */   InvalidHexBooleanError: () => (/* binding */ InvalidHexBooleanError),\n/* harmony export */   InvalidHexTypeError: () => (/* binding */ InvalidHexTypeError),\n/* harmony export */   InvalidHexValueError: () => (/* binding */ InvalidHexValueError),\n/* harmony export */   InvalidLengthError: () => (/* binding */ InvalidLengthError),\n/* harmony export */   SizeExceedsPaddingSizeError: () => (/* binding */ SizeExceedsPaddingSizeError),\n/* harmony export */   SizeOverflowError: () => (/* binding */ SizeOverflowError),\n/* harmony export */   SliceOffsetOutOfBoundsError: () => (/* binding */ SliceOffsetOutOfBoundsError),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   fromBoolean: () => (/* binding */ fromBoolean),\n/* harmony export */   fromBytes: () => (/* binding */ fromBytes),\n/* harmony export */   fromNumber: () => (/* binding */ fromNumber),\n/* harmony export */   fromString: () => (/* binding */ fromString),\n/* harmony export */   isEqual: () => (/* binding */ isEqual),\n/* harmony export */   padLeft: () => (/* binding */ padLeft),\n/* harmony export */   padRight: () => (/* binding */ padRight),\n/* harmony export */   random: () => (/* binding */ random),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   slice: () => (/* binding */ slice),\n/* harmony export */   toBigInt: () => (/* binding */ toBigInt),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toBytes: () => (/* binding */ toBytes),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toString: () => (/* binding */ toString),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   trimRight: () => (/* binding */ trimRight),\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/* harmony import */ var _noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/curves/abstract/utils */ \"(ssr)/./node_modules/@noble/curves/esm/abstract/utils.js\");\n/* harmony import */ var _Bytes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/Bytes.js\");\n/* harmony import */ var _Errors_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Errors.js */ \"(ssr)/./node_modules/ox/_esm/core/Errors.js\");\n/* harmony import */ var _Json_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Json.js */ \"(ssr)/./node_modules/ox/_esm/core/Json.js\");\n/* harmony import */ var _internal_bytes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./internal/bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/bytes.js\");\n/* harmony import */ var _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/hex.js */ \"(ssr)/./node_modules/ox/_esm/core/internal/hex.js\");\n\n\n\n\n\n\nconst encoder = /*#__PURE__*/ new TextEncoder();\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) => i.toString(16).padStart(2, '0'));\n/**\n * Asserts if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('abc')\n * // @error: InvalidHexValueTypeError:\n * // @error: Value `\"abc\"` of type `string` is an invalid hex type.\n * // @error: Hex types must be represented as `\"0x\\${string}\"`.\n * ```\n *\n * @param value - The value to assert.\n * @param options - Options.\n */\nfunction assert(value, options = {}) {\n    const { strict = false } = options;\n    if (!value)\n        throw new InvalidHexTypeError(value);\n    if (typeof value !== 'string')\n        throw new InvalidHexTypeError(value);\n    if (strict) {\n        if (!/^0x[0-9a-fA-F]*$/.test(value))\n            throw new InvalidHexValueError(value);\n    }\n    if (!value.startsWith('0x'))\n        throw new InvalidHexValueError(value);\n}\n/**\n * Concatenates two or more {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.concat('0x123', '0x456')\n * // @log: '0x123456'\n * ```\n *\n * @param values - The {@link ox#Hex.Hex} values to concatenate.\n * @returns The concatenated {@link ox#Hex.Hex} value.\n */\nfunction concat(...values) {\n    return `0x${values.reduce((acc, x) => acc + x.replace('0x', ''), '')}`;\n}\n/**\n * Instantiates a {@link ox#Hex.Hex} value from a hex string or {@link ox#Bytes.Bytes} value.\n *\n * :::tip\n *\n * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:\n *\n * - `Hex.fromBoolean`\n *\n * - `Hex.fromString`\n *\n * - `Hex.fromNumber`\n *\n * :::\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.from('0x48656c6c6f20576f726c6421')\n * // @log: '0x48656c6c6f20576f726c6421'\n *\n * Hex.from(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction from(value) {\n    if (value instanceof Uint8Array)\n        return fromBytes(value);\n    if (Array.isArray(value))\n        return fromBytes(new Uint8Array(value));\n    return value;\n}\n/**\n * Encodes a boolean into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromBoolean(true)\n * // @log: '0x1'\n *\n * Hex.fromBoolean(false)\n * // @log: '0x0'\n *\n * Hex.fromBoolean(true, { size: 32 })\n * // @log: '0x0000000000000000000000000000000000000000000000000000000000000001'\n * ```\n *\n * @param value - The boolean value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromBoolean(value, options = {}) {\n    const hex = `0x${Number(value)}`;\n    if (typeof options.size === 'number') {\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n        return padLeft(hex, options.size);\n    }\n    return hex;\n}\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.fromBytes(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // @log: '0x48656c6c6f20576f726c6421'\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromBytes(value, options = {}) {\n    let string = '';\n    for (let i = 0; i < value.length; i++)\n        string += hexes[value[i]];\n    const hex = `0x${string}`;\n    if (typeof options.size === 'number') {\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n        return padRight(hex, options.size);\n    }\n    return hex;\n}\n/**\n * Encodes a number or bigint into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420)\n * // @log: '0x1a4'\n *\n * Hex.fromNumber(420, { size: 32 })\n * // @log: '0x00000000000000000000000000000000000000000000000000000000000001a4'\n * ```\n *\n * @param value - The number or bigint value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromNumber(value, options = {}) {\n    const { signed, size } = options;\n    const value_ = BigInt(value);\n    let maxValue;\n    if (size) {\n        if (signed)\n            maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n;\n        else\n            maxValue = 2n ** (BigInt(size) * 8n) - 1n;\n    }\n    else if (typeof value === 'number') {\n        maxValue = BigInt(Number.MAX_SAFE_INTEGER);\n    }\n    const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0;\n    if ((maxValue && value_ > maxValue) || value_ < minValue) {\n        const suffix = typeof value === 'bigint' ? 'n' : '';\n        throw new IntegerOutOfRangeError({\n            max: maxValue ? `${maxValue}${suffix}` : undefined,\n            min: `${minValue}${suffix}`,\n            signed,\n            size,\n            value: `${value}${suffix}`,\n        });\n    }\n    const stringValue = (signed && value_ < 0 ? (1n << BigInt(size * 8)) + BigInt(value_) : value_).toString(16);\n    const hex = `0x${stringValue}`;\n    if (size)\n        return padLeft(hex, size);\n    return hex;\n}\n/**\n * Encodes a string into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n * Hex.fromString('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * Hex.fromString('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n * ```\n *\n * @param value - The string value to encode.\n * @param options - Options.\n * @returns The encoded {@link ox#Hex.Hex} value.\n */\nfunction fromString(value, options = {}) {\n    return fromBytes(encoder.encode(value), options);\n}\n/**\n * Checks if two {@link ox#Hex.Hex} values are equal.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.isEqual('0xdeadbeef', '0xdeadbeef')\n * // @log: true\n *\n * Hex.isEqual('0xda', '0xba')\n * // @log: false\n * ```\n *\n * @param hexA - The first {@link ox#Hex.Hex} value.\n * @param hexB - The second {@link ox#Hex.Hex} value.\n * @returns `true` if the two {@link ox#Hex.Hex} values are equal, `false` otherwise.\n */\nfunction isEqual(hexA, hexB) {\n    return (0,_noble_curves_abstract_utils__WEBPACK_IMPORTED_MODULE_1__.equalBytes)(_Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hexA), _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hexB));\n}\n/**\n * Pads a {@link ox#Hex.Hex} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1234', 4)\n * // @log: '0x00001234'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nfunction padLeft(value, size) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'left', size });\n}\n/**\n * Pads a {@link ox#Hex.Hex} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).\n *\n * @example\n * ```ts\n * import { Hex } from 'ox'\n *\n * Hex.padRight('0x1234', 4)\n * // @log: '0x12340000'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to pad.\n * @param size - The size (in bytes) of the output hex value.\n * @returns The padded {@link ox#Hex.Hex} value.\n */\nfunction padRight(value, size) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.pad(value, { dir: 'right', size });\n}\n/**\n * Generates a random {@link ox#Hex.Hex} value of the specified length.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const hex = Hex.random(32)\n * // @log: '0x...'\n * ```\n *\n * @returns Random {@link ox#Hex.Hex} value.\n */\nfunction random(length) {\n    return fromBytes(_Bytes_js__WEBPACK_IMPORTED_MODULE_2__.random(length));\n}\n/**\n * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 1, 4)\n * // @log: '0x234567'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to slice.\n * @param start - The start offset (in bytes).\n * @param end - The end offset (in bytes).\n * @param options - Options.\n * @returns The sliced {@link ox#Hex.Hex} value.\n */\nfunction slice(value, start, end, options = {}) {\n    const { strict } = options;\n    _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertStartOffset(value, start);\n    const value_ = `0x${value\n        .replace('0x', '')\n        .slice((start ?? 0) * 2, (end ?? value.length) * 2)}`;\n    if (strict)\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertEndOffset(value_, start, end);\n    return value_;\n}\n/**\n * Retrieves the size of a {@link ox#Hex.Hex} value (in bytes).\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.size('0xdeadbeef')\n * // @log: 4\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to get the size of.\n * @returns The size of the {@link ox#Hex.Hex} value (in bytes).\n */\nfunction size(value) {\n    return Math.ceil((value.length - 2) / 2);\n}\n/**\n * Trims leading zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimLeft('0x00000000deadbeef')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nfunction trimLeft(value) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'left' });\n}\n/**\n * Trims trailing zeros from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.trimRight('0xdeadbeef00000000')\n * // @log: '0xdeadbeef'\n * ```\n *\n * @param value - The {@link ox#Hex.Hex} value to trim.\n * @returns The trimmed {@link ox#Hex.Hex} value.\n */\nfunction trimRight(value) {\n    return _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.trim(value, { dir: 'right' });\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a BigInt.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBigInt('0x1a4')\n * // @log: 420n\n *\n * Hex.toBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420n\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded BigInt.\n */\nfunction toBigInt(hex, options = {}) {\n    const { signed } = options;\n    if (options.size)\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n    const value = BigInt(hex);\n    if (!signed)\n        return value;\n    const size = (hex.length - 2) / 2;\n    const max_unsigned = (1n << (BigInt(size) * 8n)) - 1n;\n    const max_signed = max_unsigned >> 1n;\n    if (value <= max_signed)\n        return value;\n    return value - max_unsigned - 1n;\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0x01')\n * // @log: true\n *\n * Hex.toBoolean('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // @log: true\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded boolean.\n */\nfunction toBoolean(hex, options = {}) {\n    if (options.size)\n        _internal_hex_js__WEBPACK_IMPORTED_MODULE_0__.assertSize(hex, options.size);\n    const hex_ = trimLeft(hex);\n    if (hex_ === '0x')\n        return false;\n    if (hex_ === '0x1')\n        return true;\n    throw new InvalidHexBooleanError(hex);\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * const data = Hex.toBytes('0x48656c6c6f20776f726c6421')\n * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded {@link ox#Bytes.Bytes}.\n */\nfunction toBytes(hex, options = {}) {\n    return _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hex, options);\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a number.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toNumber('0x1a4')\n * // @log: 420\n *\n * Hex.toNumber('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // @log: 420\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded number.\n */\nfunction toNumber(hex, options = {}) {\n    const { signed, size } = options;\n    if (!signed && !size)\n        return Number(hex);\n    return Number(toBigInt(hex, options));\n}\n/**\n * Decodes a {@link ox#Hex.Hex} value into a string.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toString('0x48656c6c6f20576f726c6421')\n * // @log: 'Hello world!'\n *\n * Hex.toString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // @log: 'Hello world'\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to decode.\n * @param options - Options.\n * @returns The decoded string.\n */\nfunction toString(hex, options = {}) {\n    const { size } = options;\n    let bytes = _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.fromHex(hex);\n    if (size) {\n        _internal_bytes_js__WEBPACK_IMPORTED_MODULE_3__.assertSize(bytes, size);\n        bytes = _Bytes_js__WEBPACK_IMPORTED_MODULE_2__.trimRight(bytes);\n    }\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Checks if the given value is {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Hex } from 'ox'\n *\n * Hex.validate('0xdeadbeef')\n * // @log: true\n *\n * Hex.validate(Bytes.from([1, 2, 3]))\n * // @log: false\n * ```\n *\n * @param value - The value to check.\n * @param options - Options.\n * @returns `true` if the value is a {@link ox#Hex.Hex}, `false` otherwise.\n */\nfunction validate(value, options = {}) {\n    const { strict = false } = options;\n    try {\n        assert(value, { strict });\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\n/**\n * Thrown when the provided integer is out of range, and cannot be represented as a hex value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromNumber(420182738912731283712937129)\n * // @error: Hex.IntegerOutOfRangeError: Number \\`4.2018273891273126e+26\\` is not in safe unsigned integer range (`0` to `9007199254740991`)\n * ```\n */\nclass IntegerOutOfRangeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ max, min, signed, size, value, }) {\n        super(`Number \\`${value}\\` is not in safe${size ? ` ${size * 8}-bit` : ''}${signed ? ' signed' : ' unsigned'} integer range ${max ? `(\\`${min}\\` to \\`${max}\\`)` : `(above \\`${min}\\`)`}`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.IntegerOutOfRangeError'\n        });\n    }\n}\n/**\n * Thrown when the provided hex value cannot be represented as a boolean.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.toBoolean('0xa')\n * // @error: Hex.InvalidHexBooleanError: Hex value `\"0xa\"` is not a valid boolean.\n * // @error: The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).\n * ```\n */\nclass InvalidHexBooleanError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(hex) {\n        super(`Hex value \\`\"${hex}\"\\` is not a valid boolean.`, {\n            metaMessages: [\n                'The hex value must be `\"0x0\"` (false) or `\"0x1\"` (true).',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexBooleanError'\n        });\n    }\n}\n/**\n * Thrown when the provided value is not a valid hex type.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert(1)\n * // @error: Hex.InvalidHexTypeError: Value `1` of type `number` is an invalid hex type.\n * ```\n */\nclass InvalidHexTypeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(value) {\n        super(`Value \\`${typeof value === 'object' ? _Json_js__WEBPACK_IMPORTED_MODULE_5__.stringify(value) : value}\\` of type \\`${typeof value}\\` is an invalid hex type.`, {\n            metaMessages: ['Hex types must be represented as `\"0x${string}\"`.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexTypeError'\n        });\n    }\n}\n/**\n * Thrown when the provided hex value is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.assert('0x0123456789abcdefg')\n * // @error: Hex.InvalidHexValueError: Value `0x0123456789abcdefg` is an invalid hex value.\n * // @error: Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).\n * ```\n */\nclass InvalidHexValueError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(value) {\n        super(`Value \\`${value}\\` is an invalid hex value.`, {\n            metaMessages: [\n                'Hex values must start with `\"0x\"` and contain only hexadecimal characters (0-9, a-f, A-F).',\n            ],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidHexValueError'\n        });\n    }\n}\n/**\n * Thrown when the provided hex value is an odd length.\n *\n * @example\n * ```ts twoslash\n * import { Bytes } from 'ox'\n *\n * Bytes.fromHex('0xabcde')\n * // @error: Hex.InvalidLengthError: Hex value `\"0xabcde\"` is an odd length (5 nibbles).\n * ```\n */\nclass InvalidLengthError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor(value) {\n        super(`Hex value \\`\"${value}\"\\` is an odd length (${value.length - 2} nibbles).`, {\n            metaMessages: ['It must be an even length.'],\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.InvalidLengthError'\n        });\n    }\n}\n/**\n * Thrown when the size of the value exceeds the expected max size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.fromString('Hello World!', { size: 8 })\n * // @error: Hex.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.\n * ```\n */\nclass SizeOverflowError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ givenSize, maxSize }) {\n        super(`Size cannot exceed \\`${maxSize}\\` bytes. Given size: \\`${givenSize}\\` bytes.`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SizeOverflowError'\n        });\n    }\n}\n/**\n * Thrown when the slice offset exceeds the bounds of the value.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.slice('0x0123456789', 6)\n * // @error: Hex.SliceOffsetOutOfBoundsError: Slice starting at offset `6` is out-of-bounds (size: `5`).\n * ```\n */\nclass SliceOffsetOutOfBoundsError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ offset, position, size, }) {\n        super(`Slice ${position === 'start' ? 'starting' : 'ending'} at offset \\`${offset}\\` is out-of-bounds (size: \\`${size}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SliceOffsetOutOfBoundsError'\n        });\n    }\n}\n/**\n * Thrown when the size of the value exceeds the pad size.\n *\n * @example\n * ```ts twoslash\n * import { Hex } from 'ox'\n *\n * Hex.padLeft('0x1a4e12a45a21323123aaa87a897a897a898a6567a578a867a98778a667a85a875a87a6a787a65a675a6a9', 32)\n * // @error: Hex.SizeExceedsPaddingSizeError: Hex size (`43`) exceeds padding size (`32`).\n * ```\n */\nclass SizeExceedsPaddingSizeError extends _Errors_js__WEBPACK_IMPORTED_MODULE_4__.BaseError {\n    constructor({ size, targetSize, type, }) {\n        super(`${type.charAt(0).toUpperCase()}${type\n            .slice(1)\n            .toLowerCase()} size (\\`${size}\\`) exceeds padding size (\\`${targetSize}\\`).`);\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 'Hex.SizeExceedsPaddingSizeError'\n        });\n    }\n}\n//# sourceMappingURL=Hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Json.js":
/*!*******************************************!*\
  !*** ./node_modules/ox/_esm/core/Json.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\nconst bigIntSuffix = '#__bigint';\n/**\n * Parses a JSON string, with support for `bigint`.\n *\n * @example\n * ```ts twoslash\n * import { Json } from 'ox'\n *\n * const json = Json.parse('{\"foo\":\"bar\",\"baz\":\"69420694206942069420694206942069420694206942069420#__bigint\"}')\n * // @log: {\n * // @log:   foo: 'bar',\n * // @log:   baz: 69420694206942069420694206942069420694206942069420n\n * // @log: }\n * ```\n *\n * @param string - The value to parse.\n * @param reviver - A function that transforms the results.\n * @returns The parsed value.\n */\nfunction parse(string, reviver) {\n    return JSON.parse(string, (key, value_) => {\n        const value = value_;\n        if (typeof value === 'string' && value.endsWith(bigIntSuffix))\n            return BigInt(value.slice(0, -bigIntSuffix.length));\n        return typeof reviver === 'function' ? reviver(key, value) : value;\n    });\n}\n/**\n * Stringifies a value to its JSON representation, with support for `bigint`.\n *\n * @example\n * ```ts twoslash\n * import { Json } from 'ox'\n *\n * const json = Json.stringify({\n *   foo: 'bar',\n *   baz: 69420694206942069420694206942069420694206942069420n,\n * })\n * // @log: '{\"foo\":\"bar\",\"baz\":\"69420694206942069420694206942069420694206942069420#__bigint\"}'\n * ```\n *\n * @param value - The value to stringify.\n * @param replacer - A function that transforms the results. It is passed the key and value of the property, and must return the value to be used in the JSON string. If this function returns `undefined`, the property is not included in the resulting JSON string.\n * @param space - A string or number that determines the indentation of the JSON string. If it is a number, it indicates the number of spaces to use as indentation; if it is a string (e.g. `'\\t'`), it uses the string as the indentation character.\n * @returns The JSON string.\n */\nfunction stringify(value, replacer, space) {\n    return JSON.stringify(value, (key, value) => {\n        if (typeof replacer === 'function')\n            return replacer(key, value);\n        if (typeof value === 'bigint')\n            return value.toString() + bigIntSuffix;\n        return value;\n    }, space);\n}\n//# sourceMappingURL=Json.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/Withdrawal.js":
/*!*************************************************!*\
  !*** ./node_modules/ox/_esm/core/Withdrawal.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromRpc: () => (/* binding */ fromRpc),\n/* harmony export */   toRpc: () => (/* binding */ toRpc)\n/* harmony export */ });\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n\n/**\n * Converts a {@link ox#Withdrawal.Rpc} to an {@link ox#Withdrawal.Withdrawal}.\n *\n * @example\n * ```ts twoslash\n * import { Withdrawal } from 'ox'\n *\n * const withdrawal = Withdrawal.fromRpc({\n *   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n *   amount: '0x620323',\n *   index: '0x0',\n *   validatorIndex: '0x1',\n * })\n * // @log: {\n * // @log:   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n * // @log:   amount: 6423331n,\n * // @log:   index: 0,\n * // @log:   validatorIndex: 1\n * // @log: }\n * ```\n *\n * @param withdrawal - The RPC withdrawal to convert.\n * @returns An instantiated {@link ox#Withdrawal.Withdrawal}.\n */\nfunction fromRpc(withdrawal) {\n    return {\n        ...withdrawal,\n        amount: BigInt(withdrawal.amount),\n        index: Number(withdrawal.index),\n        validatorIndex: Number(withdrawal.validatorIndex),\n    };\n}\n/**\n * Converts a {@link ox#Withdrawal.Withdrawal} to an {@link ox#Withdrawal.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { Withdrawal } from 'ox'\n *\n * const withdrawal = Withdrawal.toRpc({\n *   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n *   amount: 6423331n,\n *   index: 0,\n *   validatorIndex: 1,\n * })\n * // @log: {\n * // @log:   address: '0x00000000219ab540356cBB839Cbe05303d7705Fa',\n * // @log:   amount: '0x620323',\n * // @log:   index: '0x0',\n * // @log:   validatorIndex: '0x1',\n * // @log: }\n * ```\n *\n * @param withdrawal - The Withdrawal to convert.\n * @returns An RPC Withdrawal.\n */\nfunction toRpc(withdrawal) {\n    return {\n        address: withdrawal.address,\n        amount: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.fromNumber(withdrawal.amount),\n        index: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.fromNumber(withdrawal.index),\n        validatorIndex: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.fromNumber(withdrawal.validatorIndex),\n    };\n}\n//# sourceMappingURL=Withdrawal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/Withdrawal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/internal/bytes.js":
/*!*****************************************************!*\
  !*** ./node_modules/ox/_esm/core/internal/bytes.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertEndOffset: () => (/* binding */ assertEndOffset),\n/* harmony export */   assertSize: () => (/* binding */ assertSize),\n/* harmony export */   assertStartOffset: () => (/* binding */ assertStartOffset),\n/* harmony export */   charCodeMap: () => (/* binding */ charCodeMap),\n/* harmony export */   charCodeToBase16: () => (/* binding */ charCodeToBase16),\n/* harmony export */   pad: () => (/* binding */ pad),\n/* harmony export */   trim: () => (/* binding */ trim)\n/* harmony export */ });\n/* harmony import */ var _Bytes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Bytes.js */ \"(ssr)/./node_modules/ox/_esm/core/Bytes.js\");\n\n/** @internal */\nfunction assertSize(bytes, size_) {\n    if (_Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(bytes) > size_)\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SizeOverflowError({\n            givenSize: _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(bytes),\n            maxSize: size_,\n        });\n}\n/** @internal */\nfunction assertStartOffset(value, start) {\n    if (typeof start === 'number' && start > 0 && start > _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value) - 1)\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: start,\n            position: 'start',\n            size: _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n}\n/** @internal */\nfunction assertEndOffset(value, start, end) {\n    if (typeof start === 'number' &&\n        typeof end === 'number' &&\n        _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value) !== end - start) {\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: end,\n            position: 'end',\n            size: _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n    }\n}\n/** @internal */\nconst charCodeMap = {\n    zero: 48,\n    nine: 57,\n    A: 65,\n    F: 70,\n    a: 97,\n    f: 102,\n};\n/** @internal */\nfunction charCodeToBase16(char) {\n    if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n        return char - charCodeMap.zero;\n    if (char >= charCodeMap.A && char <= charCodeMap.F)\n        return char - (charCodeMap.A - 10);\n    if (char >= charCodeMap.a && char <= charCodeMap.f)\n        return char - (charCodeMap.a - 10);\n    return undefined;\n}\n/** @internal */\nfunction pad(bytes, options = {}) {\n    const { dir, size = 32 } = options;\n    if (size === 0)\n        return bytes;\n    if (bytes.length > size)\n        throw new _Bytes_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: bytes.length,\n            targetSize: size,\n            type: 'Bytes',\n        });\n    const paddedBytes = new Uint8Array(size);\n    for (let i = 0; i < size; i++) {\n        const padEnd = dir === 'right';\n        paddedBytes[padEnd ? i : size - i - 1] =\n            bytes[padEnd ? i : bytes.length - i - 1];\n    }\n    return paddedBytes;\n}\n/** @internal */\nfunction trim(value, options = {}) {\n    const { dir = 'left' } = options;\n    let data = value;\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    return data;\n}\n//# sourceMappingURL=bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/internal/bytes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/internal/errors.js":
/*!******************************************************!*\
  !*** ./node_modules/ox/_esm/core/internal/errors.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUrl: () => (/* binding */ getUrl),\n/* harmony export */   getVersion: () => (/* binding */ getVersion),\n/* harmony export */   prettyPrint: () => (/* binding */ prettyPrint)\n/* harmony export */ });\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../version.js */ \"(ssr)/./node_modules/ox/_esm/core/version.js\");\n\n/** @internal */\nfunction getUrl(url) {\n    return url;\n}\n/** @internal */\nfunction getVersion() {\n    return _version_js__WEBPACK_IMPORTED_MODULE_0__.version;\n}\n/** @internal */\nfunction prettyPrint(args) {\n    if (!args)\n        return '';\n    const entries = Object.entries(args)\n        .map(([key, value]) => {\n        if (value === undefined || value === false)\n            return null;\n        return [key, value];\n    })\n        .filter(Boolean);\n    const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0);\n    return entries\n        .map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`)\n        .join('\\n');\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2VzbS9jb3JlL2ludGVybmFsL2Vycm9ycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdDO0FBQ3hDO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQLFdBQVcsZ0RBQU87QUFDbEI7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLEdBQUcsSUFBSSwyQkFBMkIsRUFBRSxNQUFNO0FBQzlFO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvb3gvX2VzbS9jb3JlL2ludGVybmFsL2Vycm9ycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnLi4vdmVyc2lvbi5qcyc7XG4vKiogQGludGVybmFsICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0VXJsKHVybCkge1xuICAgIHJldHVybiB1cmw7XG59XG4vKiogQGludGVybmFsICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0VmVyc2lvbigpIHtcbiAgICByZXR1cm4gdmVyc2lvbjtcbn1cbi8qKiBAaW50ZXJuYWwgKi9cbmV4cG9ydCBmdW5jdGlvbiBwcmV0dHlQcmludChhcmdzKSB7XG4gICAgaWYgKCFhcmdzKVxuICAgICAgICByZXR1cm4gJyc7XG4gICAgY29uc3QgZW50cmllcyA9IE9iamVjdC5lbnRyaWVzKGFyZ3MpXG4gICAgICAgIC5tYXAoKFtrZXksIHZhbHVlXSkgPT4ge1xuICAgICAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCB8fCB2YWx1ZSA9PT0gZmFsc2UpXG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgcmV0dXJuIFtrZXksIHZhbHVlXTtcbiAgICB9KVxuICAgICAgICAuZmlsdGVyKEJvb2xlYW4pO1xuICAgIGNvbnN0IG1heExlbmd0aCA9IGVudHJpZXMucmVkdWNlKChhY2MsIFtrZXldKSA9PiBNYXRoLm1heChhY2MsIGtleS5sZW5ndGgpLCAwKTtcbiAgICByZXR1cm4gZW50cmllc1xuICAgICAgICAubWFwKChba2V5LCB2YWx1ZV0pID0+IGAgICR7YCR7a2V5fTpgLnBhZEVuZChtYXhMZW5ndGggKyAxKX0gICR7dmFsdWV9YClcbiAgICAgICAgLmpvaW4oJ1xcbicpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXJyb3JzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/internal/errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/internal/hex.js":
/*!***************************************************!*\
  !*** ./node_modules/ox/_esm/core/internal/hex.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertEndOffset: () => (/* binding */ assertEndOffset),\n/* harmony export */   assertSize: () => (/* binding */ assertSize),\n/* harmony export */   assertStartOffset: () => (/* binding */ assertStartOffset),\n/* harmony export */   pad: () => (/* binding */ pad),\n/* harmony export */   trim: () => (/* binding */ trim)\n/* harmony export */ });\n/* harmony import */ var _Hex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Hex.js */ \"(ssr)/./node_modules/ox/_esm/core/Hex.js\");\n\n/** @internal */\nfunction assertSize(hex, size_) {\n    if (_Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(hex) > size_)\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SizeOverflowError({\n            givenSize: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(hex),\n            maxSize: size_,\n        });\n}\n/** @internal */\nfunction assertStartOffset(value, start) {\n    if (typeof start === 'number' && start > 0 && start > _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value) - 1)\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: start,\n            position: 'start',\n            size: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n}\n/** @internal */\nfunction assertEndOffset(value, start, end) {\n    if (typeof start === 'number' &&\n        typeof end === 'number' &&\n        _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value) !== end - start) {\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SliceOffsetOutOfBoundsError({\n            offset: end,\n            position: 'end',\n            size: _Hex_js__WEBPACK_IMPORTED_MODULE_0__.size(value),\n        });\n    }\n}\n/** @internal */\nfunction pad(hex_, options = {}) {\n    const { dir, size = 32 } = options;\n    if (size === 0)\n        return hex_;\n    const hex = hex_.replace('0x', '');\n    if (hex.length > size * 2)\n        throw new _Hex_js__WEBPACK_IMPORTED_MODULE_0__.SizeExceedsPaddingSizeError({\n            size: Math.ceil(hex.length / 2),\n            targetSize: size,\n            type: 'Hex',\n        });\n    return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](size * 2, '0')}`;\n}\n/** @internal */\nfunction trim(value, options = {}) {\n    const { dir = 'left' } = options;\n    let data = value.replace('0x', '');\n    let sliceLength = 0;\n    for (let i = 0; i < data.length - 1; i++) {\n        if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n            sliceLength++;\n        else\n            break;\n    }\n    data =\n        dir === 'left'\n            ? data.slice(sliceLength)\n            : data.slice(0, data.length - sliceLength);\n    if (data === '0')\n        return '0x';\n    if (dir === 'right' && data.length % 2 === 1)\n        return `0x${data}0`;\n    return `0x${data}`;\n}\n//# sourceMappingURL=hex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/internal/hex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ox/_esm/core/version.js":
/*!**********************************************!*\
  !*** ./node_modules/ox/_esm/core/version.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/** @internal */\nconst version = '0.1.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3gvX2VzbS9jb3JlL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvb3gvX2VzbS9jb3JlL3ZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqIEBpbnRlcm5hbCAqL1xuZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMC4xLjEnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ox/_esm/core/version.js\n");

/***/ })

};
;