"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rpc-websockets";
exports.ids = ["vendor-chunks/rpc-websockets"];
exports.modules = {

/***/ "(rsc)/./node_modules/rpc-websockets/dist/index.mjs":
/*!****************************************************!*\
  !*** ./node_modules/rpc-websockets/dist/index.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ Client),\n/* harmony export */   CommonClient: () => (/* binding */ CommonClient),\n/* harmony export */   DefaultDataPack: () => (/* binding */ DefaultDataPack),\n/* harmony export */   Server: () => (/* binding */ Server),\n/* harmony export */   WebSocket: () => (/* binding */ WebSocket),\n/* harmony export */   createError: () => (/* binding */ createError)\n/* harmony export */ });\n/* harmony import */ var ws__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ws */ \"(rsc)/./node_modules/ws/wrapper.mjs\");\n/* harmony import */ var eventemitter3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! eventemitter3 */ \"(rsc)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs\");\n/* harmony import */ var node_url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:url */ \"node:url\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/v1.js\");\n\n\n\n\n\n// src/lib/client/websocket.ts\nfunction WebSocket(address, options) {\n  return new ws__WEBPACK_IMPORTED_MODULE_0__[\"default\"](address, options);\n}\n\n// src/lib/utils.ts\nvar DefaultDataPack = class {\n  encode(value) {\n    return JSON.stringify(value);\n  }\n  decode(value) {\n    return JSON.parse(value);\n  }\n};\n\n// src/lib/client.ts\nvar CommonClient = class extends eventemitter3__WEBPACK_IMPORTED_MODULE_1__.EventEmitter {\n  address;\n  rpc_id;\n  queue;\n  options;\n  autoconnect;\n  ready;\n  reconnect;\n  reconnect_timer_id;\n  reconnect_interval;\n  max_reconnects;\n  rest_options;\n  current_reconnects;\n  generate_request_id;\n  socket;\n  webSocketFactory;\n  dataPack;\n  /**\n  * Instantiate a Client class.\n  * @constructor\n  * @param {webSocketFactory} webSocketFactory - factory method for WebSocket\n  * @param {String} address - url to a websocket server\n  * @param {Object} options - ws options object with reconnect parameters\n  * @param {Function} generate_request_id - custom generation request Id\n  * @param {DataPack} dataPack - data pack contains encoder and decoder\n  * @return {CommonClient}\n  */\n  constructor(webSocketFactory, address = \"ws://localhost:8080\", {\n    autoconnect = true,\n    reconnect = true,\n    reconnect_interval = 1e3,\n    max_reconnects = 5,\n    ...rest_options\n  } = {}, generate_request_id, dataPack) {\n    super();\n    this.webSocketFactory = webSocketFactory;\n    this.queue = {};\n    this.rpc_id = 0;\n    this.address = address;\n    this.autoconnect = autoconnect;\n    this.ready = false;\n    this.reconnect = reconnect;\n    this.reconnect_timer_id = void 0;\n    this.reconnect_interval = reconnect_interval;\n    this.max_reconnects = max_reconnects;\n    this.rest_options = rest_options;\n    this.current_reconnects = 0;\n    this.generate_request_id = generate_request_id || (() => typeof this.rpc_id === \"number\" ? ++this.rpc_id : Number(this.rpc_id) + 1);\n    if (!dataPack) this.dataPack = new DefaultDataPack();\n    else this.dataPack = dataPack;\n    if (this.autoconnect)\n      this._connect(this.address, {\n        autoconnect: this.autoconnect,\n        reconnect: this.reconnect,\n        reconnect_interval: this.reconnect_interval,\n        max_reconnects: this.max_reconnects,\n        ...this.rest_options\n      });\n  }\n  /**\n  * Connects to a defined server if not connected already.\n  * @method\n  * @return {Undefined}\n  */\n  connect() {\n    if (this.socket) return;\n    this._connect(this.address, {\n      autoconnect: this.autoconnect,\n      reconnect: this.reconnect,\n      reconnect_interval: this.reconnect_interval,\n      max_reconnects: this.max_reconnects,\n      ...this.rest_options\n    });\n  }\n  /**\n  * Calls a registered RPC method on server.\n  * @method\n  * @param {String} method - RPC method name\n  * @param {Object|Array} params - optional method parameters\n  * @param {Number} timeout - RPC reply timeout value\n  * @param {Object} ws_opts - options passed to ws\n  * @return {Promise}\n  */\n  call(method, params, timeout, ws_opts) {\n    if (!ws_opts && \"object\" === typeof timeout) {\n      ws_opts = timeout;\n      timeout = null;\n    }\n    return new Promise((resolve, reject) => {\n      if (!this.ready) return reject(new Error(\"socket not ready\"));\n      const rpc_id = this.generate_request_id(method, params);\n      const message = {\n        jsonrpc: \"2.0\",\n        method,\n        params: params || void 0,\n        id: rpc_id\n      };\n      this.socket.send(this.dataPack.encode(message), ws_opts, (error) => {\n        if (error) return reject(error);\n        this.queue[rpc_id] = { promise: [resolve, reject] };\n        if (timeout) {\n          this.queue[rpc_id].timeout = setTimeout(() => {\n            delete this.queue[rpc_id];\n            reject(new Error(\"reply timeout\"));\n          }, timeout);\n        }\n      });\n    });\n  }\n  /**\n  * Logins with the other side of the connection.\n  * @method\n  * @param {Object} params - Login credentials object\n  * @return {Promise}\n  */\n  async login(params) {\n    const resp = await this.call(\"rpc.login\", params);\n    if (!resp) throw new Error(\"authentication failed\");\n    return resp;\n  }\n  /**\n  * Fetches a list of client's methods registered on server.\n  * @method\n  * @return {Array}\n  */\n  async listMethods() {\n    return await this.call(\"__listMethods\");\n  }\n  /**\n  * Sends a JSON-RPC 2.0 notification to server.\n  * @method\n  * @param {String} method - RPC method name\n  * @param {Object} params - optional method parameters\n  * @return {Promise}\n  */\n  notify(method, params) {\n    return new Promise((resolve, reject) => {\n      if (!this.ready) return reject(new Error(\"socket not ready\"));\n      const message = {\n        jsonrpc: \"2.0\",\n        method,\n        params\n      };\n      this.socket.send(this.dataPack.encode(message), (error) => {\n        if (error) return reject(error);\n        resolve();\n      });\n    });\n  }\n  /**\n  * Subscribes for a defined event.\n  * @method\n  * @param {String|Array} event - event name\n  * @return {Undefined}\n  * @throws {Error}\n  */\n  async subscribe(event) {\n    if (typeof event === \"string\") event = [event];\n    const result = await this.call(\"rpc.on\", event);\n    if (typeof event === \"string\" && result[event] !== \"ok\")\n      throw new Error(\n        \"Failed subscribing to an event '\" + event + \"' with: \" + result[event]\n      );\n    return result;\n  }\n  /**\n  * Unsubscribes from a defined event.\n  * @method\n  * @param {String|Array} event - event name\n  * @return {Undefined}\n  * @throws {Error}\n  */\n  async unsubscribe(event) {\n    if (typeof event === \"string\") event = [event];\n    const result = await this.call(\"rpc.off\", event);\n    if (typeof event === \"string\" && result[event] !== \"ok\")\n      throw new Error(\"Failed unsubscribing from an event with: \" + result);\n    return result;\n  }\n  /**\n  * Closes a WebSocket connection gracefully.\n  * @method\n  * @param {Number} code - socket close code\n  * @param {String} data - optional data to be sent before closing\n  * @return {Undefined}\n  */\n  close(code, data) {\n    this.socket.close(code || 1e3, data);\n  }\n  /**\n  * Enable / disable automatic reconnection.\n  * @method\n  * @param {Boolean} reconnect - enable / disable reconnection\n  * @return {Undefined}\n  */\n  setAutoReconnect(reconnect) {\n    this.reconnect = reconnect;\n  }\n  /**\n  * Set the interval between reconnection attempts.\n  * @method\n  * @param {Number} interval - reconnection interval in milliseconds\n  * @return {Undefined}\n  */\n  setReconnectInterval(interval) {\n    this.reconnect_interval = interval;\n  }\n  /**\n  * Set the maximum number of reconnection attempts.\n  * @method\n  * @param {Number} max_reconnects - maximum reconnection attempts\n  * @return {Undefined}\n  */\n  setMaxReconnects(max_reconnects) {\n    this.max_reconnects = max_reconnects;\n  }\n  /**\n  * Connection/Message handler.\n  * @method\n  * @private\n  * @param {String} address - WebSocket API address\n  * @param {Object} options - ws options object\n  * @return {Undefined}\n  */\n  _connect(address, options) {\n    clearTimeout(this.reconnect_timer_id);\n    this.socket = this.webSocketFactory(address, options);\n    this.socket.addEventListener(\"open\", () => {\n      this.ready = true;\n      this.emit(\"open\");\n      this.current_reconnects = 0;\n    });\n    this.socket.addEventListener(\"message\", ({ data: message }) => {\n      if (message instanceof ArrayBuffer)\n        message = Buffer.from(message).toString();\n      try {\n        message = this.dataPack.decode(message);\n      } catch (error) {\n        return;\n      }\n      if (message.notification && this.listeners(message.notification).length) {\n        if (!Object.keys(message.params).length)\n          return this.emit(message.notification);\n        const args = [message.notification];\n        if (message.params.constructor === Object) args.push(message.params);\n        else\n          for (let i = 0; i < message.params.length; i++)\n            args.push(message.params[i]);\n        return Promise.resolve().then(() => {\n          this.emit.apply(this, args);\n        });\n      }\n      if (!this.queue[message.id]) {\n        if (message.method) {\n          return Promise.resolve().then(() => {\n            this.emit(message.method, message?.params);\n          });\n        }\n        return;\n      }\n      if (\"error\" in message === \"result\" in message)\n        this.queue[message.id].promise[1](\n          new Error(\n            'Server response malformed. Response must include either \"result\" or \"error\", but not both.'\n          )\n        );\n      if (this.queue[message.id].timeout)\n        clearTimeout(this.queue[message.id].timeout);\n      if (message.error) this.queue[message.id].promise[1](message.error);\n      else this.queue[message.id].promise[0](message.result);\n      delete this.queue[message.id];\n    });\n    this.socket.addEventListener(\"error\", (error) => this.emit(\"error\", error));\n    this.socket.addEventListener(\"close\", ({ code, reason }) => {\n      if (this.ready)\n        setTimeout(() => this.emit(\"close\", code, reason), 0);\n      this.ready = false;\n      this.socket = void 0;\n      if (code === 1e3) return;\n      this.current_reconnects++;\n      if (this.reconnect && (this.max_reconnects > this.current_reconnects || this.max_reconnects === 0))\n        this.reconnect_timer_id = setTimeout(\n          () => this._connect(address, options),\n          this.reconnect_interval\n        );\n    });\n  }\n};\nvar Server = class extends eventemitter3__WEBPACK_IMPORTED_MODULE_1__.EventEmitter {\n  namespaces;\n  dataPack;\n  wss;\n  /**\n  * Instantiate a Server class.\n  * @constructor\n  * @param {Object} options - ws constructor's parameters with rpc\n  * @param {DataPack} dataPack - data pack contains encoder and decoder\n  * @return {Server} - returns a new Server instance\n  */\n  constructor(options, dataPack) {\n    super();\n    this.namespaces = {};\n    if (!dataPack) this.dataPack = new DefaultDataPack();\n    else this.dataPack = dataPack;\n    this.wss = new ws__WEBPACK_IMPORTED_MODULE_0__.WebSocketServer(options);\n    this.wss.on(\"listening\", () => this.emit(\"listening\"));\n    this.wss.on(\"connection\", (socket, request) => {\n      const u = node_url__WEBPACK_IMPORTED_MODULE_2__.parse(request.url, true);\n      const ns = u.pathname;\n      if (u.query.socket_id) socket._id = u.query.socket_id;\n      else socket._id = (0,uuid__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n      socket[\"_authenticated\"] = false;\n      socket.on(\"error\", (error) => this.emit(\"socket-error\", socket, error));\n      socket.on(\"close\", () => {\n        this.namespaces[ns].clients.delete(socket._id);\n        for (const event of Object.keys(this.namespaces[ns].events)) {\n          const index = this.namespaces[ns].events[event].sockets.indexOf(\n            socket._id\n          );\n          if (index >= 0)\n            this.namespaces[ns].events[event].sockets.splice(index, 1);\n        }\n        this.emit(\"disconnection\", socket);\n      });\n      if (!this.namespaces[ns]) this._generateNamespace(ns);\n      this.namespaces[ns].clients.set(socket._id, socket);\n      this.emit(\"connection\", socket, request);\n      return this._handleRPC(socket, ns);\n    });\n    this.wss.on(\"error\", (error) => this.emit(\"error\", error));\n  }\n  /**\n  * Registers an RPC method.\n  * @method\n  * @param {String} name - method name\n  * @param {Function} fn - a callee function\n  * @param {String} ns - namespace identifier\n  * @throws {TypeError}\n  * @return {Object} - returns an IMethod object\n  */\n  register(name, fn, ns = \"/\") {\n    if (!this.namespaces[ns]) this._generateNamespace(ns);\n    this.namespaces[ns].rpc_methods[name] = {\n      fn,\n      protected: false\n    };\n    return {\n      protected: () => this._makeProtectedMethod(name, ns),\n      public: () => this._makePublicMethod(name, ns)\n    };\n  }\n  /**\n  * Sets an auth method.\n  * @method\n  * @param {Function} fn - an arbitrary auth method\n  * @param {String} ns - namespace identifier\n  * @throws {TypeError}\n  * @return {Undefined}\n  */\n  setAuth(fn, ns = \"/\") {\n    this.register(\"rpc.login\", fn, ns);\n  }\n  /**\n  * Marks an RPC method as protected.\n  * @method\n  * @param {String} name - method name\n  * @param {String} ns - namespace identifier\n  * @return {Undefined}\n  */\n  _makeProtectedMethod(name, ns = \"/\") {\n    this.namespaces[ns].rpc_methods[name].protected = true;\n  }\n  /**\n  * Marks an RPC method as public.\n  * @method\n  * @param {String} name - method name\n  * @param {String} ns - namespace identifier\n  * @return {Undefined}\n  */\n  _makePublicMethod(name, ns = \"/\") {\n    this.namespaces[ns].rpc_methods[name].protected = false;\n  }\n  /**\n  * Marks an event as protected.\n  * @method\n  * @param {String} name - event name\n  * @param {String} ns - namespace identifier\n  * @return {Undefined}\n  */\n  _makeProtectedEvent(name, ns = \"/\") {\n    this.namespaces[ns].events[name].protected = true;\n  }\n  /**\n  * Marks an event as public.\n  * @method\n  * @param {String} name - event name\n  * @param {String} ns - namespace identifier\n  * @return {Undefined}\n  */\n  _makePublicEvent(name, ns = \"/\") {\n    this.namespaces[ns].events[name].protected = false;\n  }\n  /**\n  * Removes a namespace and closes all connections\n  * @method\n  * @param {String} ns - namespace identifier\n  * @throws {TypeError}\n  * @return {Undefined}\n  */\n  closeNamespace(ns) {\n    const namespace = this.namespaces[ns];\n    if (namespace) {\n      delete namespace.rpc_methods;\n      delete namespace.events;\n      for (const socket of namespace.clients.values()) socket.close();\n      delete this.namespaces[ns];\n    }\n  }\n  /**\n  * Creates a new event that can be emitted to clients.\n  * @method\n  * @param {String} name - event name\n  * @param {String} ns - namespace identifier\n  * @throws {TypeError}\n  * @return {Object} - returns an IEvent object\n  */\n  event(name, ns = \"/\") {\n    if (!this.namespaces[ns]) this._generateNamespace(ns);\n    else {\n      const index = this.namespaces[ns].events[name];\n      if (index !== void 0)\n        throw new Error(`Already registered event ${ns}${name}`);\n    }\n    this.namespaces[ns].events[name] = {\n      sockets: [],\n      protected: false\n    };\n    this.on(name, (...params) => {\n      if (params.length === 1 && params[0] instanceof Object)\n        params = params[0];\n      for (const socket_id of this.namespaces[ns].events[name].sockets) {\n        const socket = this.namespaces[ns].clients.get(socket_id);\n        if (!socket) continue;\n        socket.send(\n          this.dataPack.encode({\n            notification: name,\n            params\n          })\n        );\n      }\n    });\n    return {\n      protected: () => this._makeProtectedEvent(name, ns),\n      public: () => this._makePublicEvent(name, ns)\n    };\n  }\n  /**\n  * Returns a requested namespace object\n  * @method\n  * @param {String} name - namespace identifier\n  * @throws {TypeError}\n  * @return {Object} - namespace object\n  */\n  of(name) {\n    if (!this.namespaces[name]) this._generateNamespace(name);\n    const self = this;\n    return {\n      // self.register convenience method\n      register(fn_name, fn) {\n        if (arguments.length !== 2)\n          throw new Error(\"must provide exactly two arguments\");\n        if (typeof fn_name !== \"string\")\n          throw new Error(\"name must be a string\");\n        if (typeof fn !== \"function\")\n          throw new Error(\"handler must be a function\");\n        return self.register(fn_name, fn, name);\n      },\n      // self.event convenience method\n      event(ev_name) {\n        if (arguments.length !== 1)\n          throw new Error(\"must provide exactly one argument\");\n        if (typeof ev_name !== \"string\")\n          throw new Error(\"name must be a string\");\n        return self.event(ev_name, name);\n      },\n      // self.eventList convenience method\n      get eventList() {\n        return Object.keys(self.namespaces[name].events);\n      },\n      /**\n      * Emits a specified event to this namespace.\n      * @inner\n      * @method\n      * @param {String} event - event name\n      * @param {Array} params - event parameters\n      * @return {Undefined}\n      */\n      emit(event, ...params) {\n        const socket_ids = [...self.namespaces[name].clients.keys()];\n        for (let i = 0, id; id = socket_ids[i]; ++i) {\n          self.namespaces[name].clients.get(id).send(\n            self.dataPack.encode({\n              notification: event,\n              params: params || []\n            })\n          );\n        }\n      },\n      /**\n      * Returns a name of this namespace.\n      * @inner\n      * @method\n      * @kind constant\n      * @return {String}\n      */\n      get name() {\n        return name;\n      },\n      /**\n      * Returns a hash of websocket objects connected to this namespace.\n      * @inner\n      * @method\n      * @return {Object}\n      */\n      connected() {\n        const socket_ids = [...self.namespaces[name].clients.keys()];\n        return socket_ids.reduce(\n          (acc, curr) => ({\n            ...acc,\n            [curr]: self.namespaces[name].clients.get(curr)\n          }),\n          {}\n        );\n      },\n      /**\n      * Returns a list of client unique identifiers connected to this namespace.\n      * @inner\n      * @method\n      * @return {Array}\n      */\n      clients() {\n        return self.namespaces[name];\n      }\n    };\n  }\n  /**\n  * Lists all created events in a given namespace. Defaults to \"/\".\n  * @method\n  * @param {String} ns - namespaces identifier\n  * @readonly\n  * @return {Array} - returns a list of created events\n  */\n  eventList(ns = \"/\") {\n    if (!this.namespaces[ns]) return [];\n    return Object.keys(this.namespaces[ns].events);\n  }\n  /**\n  * Creates a JSON-RPC 2.0 compliant error\n  * @method\n  * @param {Number} code - indicates the error type that occurred\n  * @param {String} message - provides a short description of the error\n  * @param {String|Object} data - details containing additional information about the error\n  * @return {Object}\n  */\n  createError(code, message, data) {\n    return {\n      code,\n      message,\n      data: data || null\n    };\n  }\n  /**\n  * Closes the server and terminates all clients.\n  * @method\n  * @return {Promise}\n  */\n  close() {\n    return new Promise((resolve, reject) => {\n      try {\n        this.wss.close();\n        this.emit(\"close\");\n        resolve();\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n  /**\n  * Handles all WebSocket JSON RPC 2.0 requests.\n  * @private\n  * @param {Object} socket - ws socket instance\n  * @param {String} ns - namespaces identifier\n  * @return {Undefined}\n  */\n  _handleRPC(socket, ns = \"/\") {\n    socket.on(\"message\", async (data) => {\n      const msg_options = {};\n      if (data instanceof ArrayBuffer) {\n        msg_options.binary = true;\n        data = Buffer.from(data).toString();\n      }\n      if (socket.readyState !== 1) return;\n      let parsedData;\n      try {\n        parsedData = this.dataPack.decode(data);\n      } catch (error) {\n        return socket.send(\n          this.dataPack.encode({\n            jsonrpc: \"2.0\",\n            error: createError(-32700, error.toString()),\n            id: null\n          }),\n          msg_options\n        );\n      }\n      if (Array.isArray(parsedData)) {\n        if (!parsedData.length)\n          return socket.send(\n            this.dataPack.encode({\n              jsonrpc: \"2.0\",\n              error: createError(-32600, \"Invalid array\"),\n              id: null\n            }),\n            msg_options\n          );\n        const responses = [];\n        for (const message of parsedData) {\n          const response2 = await this._runMethod(message, socket._id, ns);\n          if (!response2) continue;\n          responses.push(response2);\n        }\n        if (!responses.length) return;\n        return socket.send(this.dataPack.encode(responses), msg_options);\n      }\n      const response = await this._runMethod(parsedData, socket._id, ns);\n      if (!response) return;\n      return socket.send(this.dataPack.encode(response), msg_options);\n    });\n  }\n  /**\n  * Runs a defined RPC method.\n  * @private\n  * @param {Object} message - a message received\n  * @param {Object} socket_id - user's socket id\n  * @param {String} ns - namespaces identifier\n  * @return {Object|undefined}\n  */\n  async _runMethod(message, socket_id, ns = \"/\") {\n    if (typeof message !== \"object\" || message === null)\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32600),\n        id: null\n      };\n    if (message.jsonrpc !== \"2.0\")\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32600, \"Invalid JSON RPC version\"),\n        id: message.id || null\n      };\n    if (!message.method)\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32602, \"Method not specified\"),\n        id: message.id || null\n      };\n    if (typeof message.method !== \"string\")\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32600, \"Invalid method name\"),\n        id: message.id || null\n      };\n    if (message.params && typeof message.params === \"string\")\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32600),\n        id: message.id || null\n      };\n    if (message.method === \"rpc.on\") {\n      if (!message.params)\n        return {\n          jsonrpc: \"2.0\",\n          error: createError(-32e3),\n          id: message.id || null\n        };\n      const results = {};\n      const event_names = Object.keys(this.namespaces[ns].events);\n      for (const name of message.params) {\n        const index = event_names.indexOf(name);\n        const namespace = this.namespaces[ns];\n        if (index === -1) {\n          results[name] = \"provided event invalid\";\n          continue;\n        }\n        if (namespace.events[event_names[index]].protected === true && namespace.clients.get(socket_id)[\"_authenticated\"] === false) {\n          return {\n            jsonrpc: \"2.0\",\n            error: createError(-32606),\n            id: message.id || null\n          };\n        }\n        const socket_index = namespace.events[event_names[index]].sockets.indexOf(socket_id);\n        if (socket_index >= 0) {\n          results[name] = \"socket has already been subscribed to event\";\n          continue;\n        }\n        namespace.events[event_names[index]].sockets.push(socket_id);\n        results[name] = \"ok\";\n      }\n      return {\n        jsonrpc: \"2.0\",\n        result: results,\n        id: message.id || null\n      };\n    } else if (message.method === \"rpc.off\") {\n      if (!message.params)\n        return {\n          jsonrpc: \"2.0\",\n          error: createError(-32e3),\n          id: message.id || null\n        };\n      const results = {};\n      for (const name of message.params) {\n        if (!this.namespaces[ns].events[name]) {\n          results[name] = \"provided event invalid\";\n          continue;\n        }\n        const index = this.namespaces[ns].events[name].sockets.indexOf(socket_id);\n        if (index === -1) {\n          results[name] = \"not subscribed\";\n          continue;\n        }\n        this.namespaces[ns].events[name].sockets.splice(index, 1);\n        results[name] = \"ok\";\n      }\n      return {\n        jsonrpc: \"2.0\",\n        result: results,\n        id: message.id || null\n      };\n    } else if (message.method === \"rpc.login\") {\n      if (!message.params)\n        return {\n          jsonrpc: \"2.0\",\n          error: createError(-32604),\n          id: message.id || null\n        };\n    }\n    if (!this.namespaces[ns].rpc_methods[message.method]) {\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32601),\n        id: message.id || null\n      };\n    }\n    let response = null;\n    if (this.namespaces[ns].rpc_methods[message.method].protected === true && this.namespaces[ns].clients.get(socket_id)[\"_authenticated\"] === false) {\n      return {\n        jsonrpc: \"2.0\",\n        error: createError(-32605),\n        id: message.id || null\n      };\n    }\n    try {\n      response = await this.namespaces[ns].rpc_methods[message.method].fn(\n        message.params,\n        socket_id\n      );\n    } catch (error) {\n      if (!message.id) return;\n      if (error instanceof Error)\n        return {\n          jsonrpc: \"2.0\",\n          error: {\n            code: -32e3,\n            message: error.name,\n            data: error.message\n          },\n          id: message.id\n        };\n      return {\n        jsonrpc: \"2.0\",\n        error,\n        id: message.id\n      };\n    }\n    if (!message.id) return;\n    if (message.method === \"rpc.login\" && response === true) {\n      const s = this.namespaces[ns].clients.get(socket_id);\n      s[\"_authenticated\"] = true;\n      this.namespaces[ns].clients.set(socket_id, s);\n    }\n    return {\n      jsonrpc: \"2.0\",\n      result: response,\n      id: message.id\n    };\n  }\n  /**\n  * Generate a new namespace store.\n  * Also preregister some special namespace methods.\n  * @private\n  * @param {String} name - namespaces identifier\n  * @return {undefined}\n  */\n  _generateNamespace(name) {\n    this.namespaces[name] = {\n      rpc_methods: {\n        __listMethods: {\n          fn: () => Object.keys(this.namespaces[name].rpc_methods),\n          protected: false\n        }\n      },\n      clients: /* @__PURE__ */ new Map(),\n      events: {}\n    };\n  }\n};\nvar RPC_ERRORS = /* @__PURE__ */ new Map([\n  [-32e3, \"Event not provided\"],\n  [-32600, \"Invalid Request\"],\n  [-32601, \"Method not found\"],\n  [-32602, \"Invalid params\"],\n  [-32603, \"Internal error\"],\n  [-32604, \"Params not found\"],\n  [-32605, \"Method forbidden\"],\n  [-32606, \"Event forbidden\"],\n  [-32700, \"Parse error\"]\n]);\nfunction createError(code, details) {\n  const error = {\n    code,\n    message: RPC_ERRORS.get(code) || \"Internal Server Error\"\n  };\n  if (details) error[\"data\"] = details;\n  return error;\n}\n\n// src/index.ts\nvar Client = class extends CommonClient {\n  constructor(address = \"ws://localhost:8080\", {\n    autoconnect = true,\n    reconnect = true,\n    reconnect_interval = 1e3,\n    max_reconnects = 5,\n    ...rest_options\n  } = {}, generate_request_id) {\n    super(\n      WebSocket,\n      address,\n      {\n        autoconnect,\n        reconnect,\n        reconnect_interval,\n        max_reconnects,\n        ...rest_options\n      },\n      generate_request_id\n    );\n  }\n};\n\n\n//# sourceMappingURL=out.js.map\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rpc-websockets/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/rpc-websockets/node_modules/eventemitter3/index.js ***!
  \*************************************************************************/
/***/ ((module) => {

eval("\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif (true) {\n  module.exports = EventEmitter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventEmitter: () => (/* reexport default export from named module */ _index_js__WEBPACK_IMPORTED_MODULE_0__),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(rsc)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_index_js__WEBPACK_IMPORTED_MODULE_0__);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcnBjLXdlYnNvY2tldHMvbm9kZV9tb2R1bGVzL2V2ZW50ZW1pdHRlcjMvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQzs7QUFFZDtBQUN2QixpRUFBZSxzQ0FBWSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvcnBjLXdlYnNvY2tldHMvbm9kZV9tb2R1bGVzL2V2ZW50ZW1pdHRlcjMvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBFdmVudEVtaXR0ZXIgZnJvbSAnLi9pbmRleC5qcydcblxuZXhwb3J0IHsgRXZlbnRFbWl0dGVyIH1cbmV4cG9ydCBkZWZhdWx0IEV2ZW50RW1pdHRlclxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rpc-websockets/node_modules/eventemitter3/index.mjs\n");

/***/ })

};
;