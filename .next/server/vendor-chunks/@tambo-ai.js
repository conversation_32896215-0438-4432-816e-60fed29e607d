"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tambo-ai";
exports.ids = ["vendor-chunks/@tambo-ai"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/hooks/react-query-hooks.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/hooks/react-query-hooks.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTamboMutation: () => (/* binding */ useTamboMutation),\n/* harmony export */   useTamboQuery: () => (/* binding */ useTamboQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _providers_tambo_client_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../providers/tambo-client-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js\");\n// tamboHooks.ts\n\n\n/**\n * Wrapper around useQuery that uses the internal tambo query client.\n *\n * Use this instead of useQuery from @tanstack/react-query\n * @param options - The options for the query, same as useQuery from @tanstack/react-query\n * @returns The query result\n */\nfunction useTamboQuery(options) {\n    const queryClient = (0,_providers_tambo_client_provider__WEBPACK_IMPORTED_MODULE_0__.useTamboQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.useQuery)(options, queryClient);\n}\n/**\n * Wrapper around useMutation that uses the internal tambo query client.\n *\n * Use this instead of useMutation from @tanstack/react-query\n * @param options - The options for the mutation, same as useMutation from @tanstack/react-query\n * @returns The mutation result\n */\nfunction useTamboMutation(options) {\n    const queryClient = (0,_providers_tambo_client_provider__WEBPACK_IMPORTED_MODULE_0__.useTamboQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useMutation)(options, queryClient);\n}\n//# sourceMappingURL=react-query-hooks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/hooks/react-query-hooks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-component-state.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/hooks/use-component-state.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTamboComponentState: () => (/* binding */ useTamboComponentState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_debounce__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-debounce */ \"(ssr)/./node_modules/use-debounce/dist/index.module.js\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../providers */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-thread-provider.js\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../providers */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js\");\n/* harmony import */ var _use_current_message__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-current-message */ \"(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-current-message.js\");\n\n\n\n\n// eslint-disable-next-line jsdoc/require-jsdoc\nfunction useTamboComponentState(keyName, initialValue, debounceTime = 500) {\n    const { messageId } = (0,_use_current_message__WEBPACK_IMPORTED_MODULE_1__.useTamboMessageContext)();\n    const { updateThreadMessage, thread } = (0,_providers__WEBPACK_IMPORTED_MODULE_2__.useTamboThread)();\n    const client = (0,_providers__WEBPACK_IMPORTED_MODULE_3__.useTamboClient)();\n    const message = (0,_use_current_message__WEBPACK_IMPORTED_MODULE_1__.useTamboCurrentMessage)();\n    const threadId = thread.id;\n    // Initial value management\n    const [cachedInitialValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => initialValue);\n    // UI state management\n    const [localState, setLocalState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(cachedInitialValue);\n    // Synchronization state\n    const [isPending, setIsPending] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Track the last user-initiated value instead of a simple boolean flag\n    const [lastUserValue, setLastUserValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [haveInitialized, setHaveInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Determine if we need to initialize state\n    const shouldInitialize = !haveInitialized &&\n        message &&\n        cachedInitialValue !== undefined &&\n        (!message.componentState || !(keyName in message.componentState));\n    // Sync local state with message state on initial load and when message changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (message?.componentState && keyName in message.componentState) {\n            const messageState = message.componentState[keyName];\n            // Only update local state if we haven't had any user changes yet\n            if (lastUserValue === null) {\n                setLocalState(messageState);\n            }\n        }\n        // Otherwise fall back to initial value if we have one and no user changes\n        else if (cachedInitialValue !== undefined &&\n            !localState &&\n            lastUserValue === null) {\n            setLocalState(cachedInitialValue);\n        }\n    }, [\n        keyName,\n        message?.componentState,\n        cachedInitialValue,\n        lastUserValue,\n        localState,\n    ]);\n    // Create debounced save function for efficient server synchronization\n    const debouncedServerWrite = (0,use_debounce__WEBPACK_IMPORTED_MODULE_4__.useDebouncedCallback)(async (newValue) => {\n        setIsPending(true);\n        try {\n            const componentStateUpdate = {\n                state: { [keyName]: newValue },\n            };\n            await client.beta.threads.messages.updateComponentState(threadId, messageId, componentStateUpdate);\n        }\n        catch (err) {\n            console.error(`Failed to save component state for key \"${keyName}\":`, err);\n        }\n        finally {\n            setIsPending(false);\n        }\n    }, debounceTime);\n    // Initialize state on first render if needed\n    const initializeState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async () => {\n        if (!message) {\n            console.warn(`Cannot initialize state for missing message ${messageId} with key \"${keyName}\"`);\n            return;\n        }\n        try {\n            const messageUpdate = {\n                ...message,\n                componentState: {\n                    ...message.componentState,\n                    [keyName]: cachedInitialValue,\n                },\n            };\n            const componentStateUpdate = {\n                state: { [keyName]: cachedInitialValue },\n            };\n            await Promise.all([\n                updateThreadMessage(messageId, messageUpdate, false),\n                client.beta.threads.messages.updateComponentState(threadId, messageId, componentStateUpdate),\n            ]);\n        }\n        catch (err) {\n            console.warn(`Failed to initialize component state for key \"${keyName}\":`, err);\n        }\n    }, [\n        cachedInitialValue,\n        client.beta.threads.messages,\n        keyName,\n        message,\n        messageId,\n        threadId,\n        updateThreadMessage,\n    ]);\n    // Send initial state when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (shouldInitialize) {\n            initializeState();\n            setHaveInitialized(true);\n        }\n    }, [initializeState, shouldInitialize]);\n    // setValue function for updating state\n    // Updates local state immediately and schedules debounced server sync\n    const setValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newValue) => {\n        // Track this as a user-initiated update\n        setLastUserValue(newValue);\n        setLocalState(newValue);\n        // Only trigger server updates if we have a message\n        if (message) {\n            debouncedServerWrite(newValue);\n            const messageUpdate = {\n                ...message,\n                componentState: {\n                    ...message.componentState,\n                    [keyName]: newValue,\n                },\n            };\n            updateThreadMessage(messageId, messageUpdate, false);\n        }\n        else {\n            console.warn(`Cannot update server for missing message ${messageId} with key \"${keyName}\"`);\n        }\n    }, [message, debouncedServerWrite, keyName, updateThreadMessage, messageId]);\n    // Ensure pending changes are flushed on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        return () => {\n            debouncedServerWrite.flush();\n        };\n    }, [debouncedServerWrite]);\n    // Return the local state for immediate UI rendering\n    return [localState, setValue, { isPending }];\n}\n//# sourceMappingURL=use-component-state.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-component-state.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-current-message.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/hooks/use-current-message.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamboMessageProvider: () => (/* binding */ TamboMessageProvider),\n/* harmony export */   useTamboCurrentMessage: () => (/* binding */ useTamboCurrentMessage),\n/* harmony export */   useTamboMessageContext: () => (/* binding */ useTamboMessageContext),\n/* harmony export */   wrapWithTamboMessageProvider: () => (/* binding */ wrapWithTamboMessageProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../providers */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-thread-provider.js\");\n\n\nconst TamboMessageContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    messageId: \"\",\n});\n/**\n * Wraps all components, so that they can find what thread and message they are in\n * @param props - props for the TamboMessageProvider\n * @param props.children - The children to wrap\n * @param props.messageId - The messageId of the message\n * @returns The wrapped component\n */\nconst TamboMessageProvider = ({ children, messageId }) => {\n    // Use a unique key={...} to force a re-render when the messageId changes - this\n    // make sure that if the rendered component is swapped into a tree (like if\n    // you always show the last rendered component) then the state/etc is correct\n    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TamboMessageContext.Provider, { value: { messageId }, key: messageId }, children));\n};\n/**\n * Wraps a component with a ComponentMessageProvider - this allows the provider\n * to be used outside of a TSX file\n * @param children - The children to wrap\n * @param threadId - The threadId of the thread\n * @param messageId - The messageId of the message\n * @returns The wrapped component\n */\nfunction wrapWithTamboMessageProvider(children, threadId, messageId) {\n    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TamboMessageProvider, { threadId: threadId, messageId: messageId }, children));\n}\n/**\n * Hook used inside a component wrapped with ComponentMessageProvider, to get\n * the threadId and messageId\n * @returns The threadId and messageId\n */\nconst useTamboMessageContext = () => {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TamboMessageContext);\n    if (!context) {\n        throw new Error(\"useTamboMessageContext must be used within a TamboMessageProvider\");\n    }\n    return context;\n};\n/**\n * Hook used inside a component wrapped with ComponentMessageProvider, to get\n * the current message. The current thread will be fetched from the server, if\n * it is not already in the cache.\n * @returns The current message that is used to render the component\n */\nconst useTamboCurrentMessage = () => {\n    const { messageId, threadId } = useTamboMessageContext();\n    const { thread } = (0,_providers__WEBPACK_IMPORTED_MODULE_1__.useTamboThread)();\n    if (thread.id && threadId && thread.id !== threadId) {\n        console.warn(`Thread ID mismatch ${thread.id} !== ${threadId}`);\n    }\n    const message = thread.messages.find((m) => m.id === messageId);\n    return message;\n};\n//# sourceMappingURL=use-current-message.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-current-message.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-suggestions.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/hooks/use-suggestions.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTamboSuggestions: () => (/* binding */ useTamboSuggestions)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _model_generate_component_response__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../model/generate-component-response */ \"(ssr)/./node_modules/@tambo-ai/react/esm/model/generate-component-response.js\");\n/* harmony import */ var _model_validate_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../model/validate-input */ \"(ssr)/./node_modules/@tambo-ai/react/esm/model/validate-input.js\");\n/* harmony import */ var _providers_tambo_client_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../providers/tambo-client-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js\");\n/* harmony import */ var _providers_tambo_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../providers/tambo-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-provider.js\");\n/* harmony import */ var _providers_tambo_registry_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../providers/tambo-registry-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-registry-provider.js\");\n/* harmony import */ var _providers_tambo_thread_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../providers/tambo-thread-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-thread-provider.js\");\n/* harmony import */ var _util_query_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../util/query-utils */ \"(ssr)/./node_modules/@tambo-ai/react/esm/util/query-utils.js\");\n/* harmony import */ var _util_registry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/registry */ \"(ssr)/./node_modules/@tambo-ai/react/esm/util/registry.js\");\n/* harmony import */ var _react_query_hooks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./react-query-hooks */ \"(ssr)/./node_modules/@tambo-ai/react/esm/hooks/react-query-hooks.js\");\n/* harmony import */ var _use_thread_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./use-thread-input */ \"(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-thread-input.js\");\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Hook for managing Tambo AI suggestions in a thread\n * @param options - Configuration options for suggestion generation\n * @returns Object containing suggestions state and control functions\n */\nfunction useTamboSuggestions(options = {}) {\n    const { maxSuggestions = 3 } = options;\n    const { thread, generationStage } = (0,_providers_tambo_thread_provider__WEBPACK_IMPORTED_MODULE_1__.useTamboThread)();\n    const { sendThreadMessage } = (0,_providers_tambo_provider__WEBPACK_IMPORTED_MODULE_2__.useTambo)();\n    const tamboClient = (0,_providers_tambo_client_provider__WEBPACK_IMPORTED_MODULE_3__.useTamboClient)();\n    const { componentList, toolRegistry, componentToolAssociations } = (0,_providers_tambo_registry_provider__WEBPACK_IMPORTED_MODULE_4__.useTamboRegistry)();\n    const [selectedSuggestionId, setSelectedSuggestionId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { setValue: setInputValue } = (0,_use_thread_input__WEBPACK_IMPORTED_MODULE_5__.useTamboThreadInput)();\n    const latestMessage = thread.messages[thread.messages.length - 1];\n    const isLatestFromTambo = latestMessage?.role === \"assistant\";\n    const latestMessageId = latestMessage?.id;\n    // Reset selected suggestion when the message changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        setSelectedSuggestionId(null);\n    }, [latestMessageId]);\n    const shouldGenerateSuggestions = latestMessageId && isLatestFromTambo && (0,_model_generate_component_response__WEBPACK_IMPORTED_MODULE_6__.isIdleStage)(generationStage);\n    // Use React Query to fetch suggestions when a new hydra message is received\n    const suggestionsResult = (0,_react_query_hooks__WEBPACK_IMPORTED_MODULE_7__.useTamboQuery)({\n        // Make sure the query key changes when the message changes, so that we are\n        // always generating suggestions when there is a new message\n        queryKey: [\n            \"suggestions\",\n            shouldGenerateSuggestions ? latestMessageId : null,\n        ],\n        queryFn: async () => {\n            if (!shouldGenerateSuggestions) {\n                return [];\n            }\n            // Get registered components from the registry\n            const components = (0,_util_registry__WEBPACK_IMPORTED_MODULE_8__.getAvailableComponents)(componentList, toolRegistry, componentToolAssociations);\n            return await tamboClient.beta.threads.suggestions.generate(thread.id, latestMessageId, {\n                maxSuggestions,\n                availableComponents: components,\n            });\n        },\n        // Only run the query if we have a valid message from hydra\n        enabled: Boolean(latestMessageId && isLatestFromTambo),\n        // Don't refetch on window focus or reconnect\n        refetchOnWindowFocus: false,\n        refetchOnReconnect: false,\n        // Don't retry on failure\n        retry: false,\n    });\n    // Accept suggestion mutation\n    const acceptMutationState = (0,_react_query_hooks__WEBPACK_IMPORTED_MODULE_7__.useTamboMutation)({\n        mutationFn: async ({ suggestion, shouldSubmit = false }) => {\n            const validation = (0,_model_validate_input__WEBPACK_IMPORTED_MODULE_9__.validateInput)(suggestion.detailedSuggestion);\n            if (!validation.isValid) {\n                if (validation.error) {\n                    throw validation.error;\n                }\n                throw new Error(_use_thread_input__WEBPACK_IMPORTED_MODULE_5__.INPUT_ERROR_MESSAGES.VALIDATION);\n            }\n            if (shouldSubmit) {\n                await sendThreadMessage(validation.sanitizedInput, {\n                    threadId: thread.id,\n                });\n            }\n            else {\n                setInputValue(validation.sanitizedInput);\n            }\n            setSelectedSuggestionId(suggestion.id);\n        },\n    });\n    // Generate suggestions mutation\n    const generateMutationState = (0,_react_query_hooks__WEBPACK_IMPORTED_MODULE_7__.useTamboMutation)({\n        mutationFn: async (abortController) => {\n            if (!shouldGenerateSuggestions) {\n                return undefined;\n            }\n            // Get registered components from the registry\n            const components = (0,_util_registry__WEBPACK_IMPORTED_MODULE_8__.getAvailableComponents)(componentList, toolRegistry, componentToolAssociations);\n            return await tamboClient.beta.threads.suggestions.generate(thread.id, latestMessageId, {\n                maxSuggestions,\n                availableComponents: components,\n            }, { signal: abortController.signal });\n        },\n        // Don't retry on failure\n        retry: false,\n    });\n    // Use the query data if available, otherwise use the mutation data\n    // Only return suggestions if the latest message is from hydra\n    const suggestions = isLatestFromTambo\n        ? (suggestionsResult.data ?? generateMutationState.data ?? [])\n        : [];\n    return {\n        suggestions,\n        accept: acceptMutationState.mutateAsync,\n        selectedSuggestionId,\n        acceptResult: acceptMutationState,\n        generateResult: generateMutationState,\n        suggestionsResult,\n        ...(0,_util_query_utils__WEBPACK_IMPORTED_MODULE_10__.combineMutationResults)(acceptMutationState, generateMutationState),\n    };\n}\n//# sourceMappingURL=use-suggestions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-suggestions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-tambo-threads.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/hooks/use-tambo-threads.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTamboThreadList: () => (/* binding */ useTamboThreadList)\n/* harmony export */ });\n/* harmony import */ var _providers_tambo_client_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../providers/tambo-client-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js\");\n/* harmony import */ var _react_query_hooks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./react-query-hooks */ \"(ssr)/./node_modules/@tambo-ai/react/esm/hooks/react-query-hooks.js\");\n\n\n/**\n * Get all the threads for the specified project.\n *\n * If contextKey is empty, then all threads for the project will be returned.\n * If contextKey is not empty, then only the threads for the specified context\n * key will be returned.\n * @param config - The config for the useTamboThreadList hook\n * @param config.projectId - The projectId to get the threads for\n * @param config.contextKey - The context key to get the threads for\n * @returns The threads for the specified project and optional context key\n */\nfunction useTamboThreadList({ projectId, contextKey } = {}, options = {}) {\n    const client = (0,_providers_tambo_client_provider__WEBPACK_IMPORTED_MODULE_0__.useTamboClient)();\n    const { data: queriedProjectId, ...projectIdState } = (0,_react_query_hooks__WEBPACK_IMPORTED_MODULE_1__.useTamboQuery)({\n        ...options,\n        queryKey: [\"projectId\"],\n        queryFn: async () => {\n            return (await client.beta.projects.getCurrent()).id;\n        },\n    });\n    const currentProjectId = projectId ?? queriedProjectId;\n    const threadState = (0,_react_query_hooks__WEBPACK_IMPORTED_MODULE_1__.useTamboQuery)({\n        ...options,\n        enabled: !!currentProjectId,\n        queryKey: [\"threads\", currentProjectId, contextKey],\n        queryFn: async () => {\n            if (!currentProjectId) {\n                return null;\n            }\n            const threadIter = await client.beta.threads.list(currentProjectId, {\n                contextKey,\n            });\n            return threadIter;\n        },\n    });\n    return currentProjectId ? threadState : { data: null, ...projectIdState };\n}\n//# sourceMappingURL=use-tambo-threads.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3JlYWN0L2VzbS9ob29rcy91c2UtdGFtYm8tdGhyZWFkcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0U7QUFDaEI7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDhCQUE4Qix3QkFBd0IsSUFBSSxjQUFjO0FBQy9FLG1CQUFtQixnRkFBYztBQUNqQyxZQUFZLDRDQUE0QyxFQUFFLGlFQUFhO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBLHdCQUF3QixpRUFBYTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMLDhDQUE4QztBQUM5QztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9AdGFtYm8tYWkvcmVhY3QvZXNtL2hvb2tzL3VzZS10YW1iby10aHJlYWRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVRhbWJvQ2xpZW50IH0gZnJvbSBcIi4uL3Byb3ZpZGVycy90YW1iby1jbGllbnQtcHJvdmlkZXJcIjtcbmltcG9ydCB7IHVzZVRhbWJvUXVlcnkgfSBmcm9tIFwiLi9yZWFjdC1xdWVyeS1ob29rc1wiO1xuLyoqXG4gKiBHZXQgYWxsIHRoZSB0aHJlYWRzIGZvciB0aGUgc3BlY2lmaWVkIHByb2plY3QuXG4gKlxuICogSWYgY29udGV4dEtleSBpcyBlbXB0eSwgdGhlbiBhbGwgdGhyZWFkcyBmb3IgdGhlIHByb2plY3Qgd2lsbCBiZSByZXR1cm5lZC5cbiAqIElmIGNvbnRleHRLZXkgaXMgbm90IGVtcHR5LCB0aGVuIG9ubHkgdGhlIHRocmVhZHMgZm9yIHRoZSBzcGVjaWZpZWQgY29udGV4dFxuICoga2V5IHdpbGwgYmUgcmV0dXJuZWQuXG4gKiBAcGFyYW0gY29uZmlnIC0gVGhlIGNvbmZpZyBmb3IgdGhlIHVzZVRhbWJvVGhyZWFkTGlzdCBob29rXG4gKiBAcGFyYW0gY29uZmlnLnByb2plY3RJZCAtIFRoZSBwcm9qZWN0SWQgdG8gZ2V0IHRoZSB0aHJlYWRzIGZvclxuICogQHBhcmFtIGNvbmZpZy5jb250ZXh0S2V5IC0gVGhlIGNvbnRleHQga2V5IHRvIGdldCB0aGUgdGhyZWFkcyBmb3JcbiAqIEByZXR1cm5zIFRoZSB0aHJlYWRzIGZvciB0aGUgc3BlY2lmaWVkIHByb2plY3QgYW5kIG9wdGlvbmFsIGNvbnRleHQga2V5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VUYW1ib1RocmVhZExpc3QoeyBwcm9qZWN0SWQsIGNvbnRleHRLZXkgfSA9IHt9LCBvcHRpb25zID0ge30pIHtcbiAgICBjb25zdCBjbGllbnQgPSB1c2VUYW1ib0NsaWVudCgpO1xuICAgIGNvbnN0IHsgZGF0YTogcXVlcmllZFByb2plY3RJZCwgLi4ucHJvamVjdElkU3RhdGUgfSA9IHVzZVRhbWJvUXVlcnkoe1xuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICBxdWVyeUtleTogW1wicHJvamVjdElkXCJdLFxuICAgICAgICBxdWVyeUZuOiBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gKGF3YWl0IGNsaWVudC5iZXRhLnByb2plY3RzLmdldEN1cnJlbnQoKSkuaWQ7XG4gICAgICAgIH0sXG4gICAgfSk7XG4gICAgY29uc3QgY3VycmVudFByb2plY3RJZCA9IHByb2plY3RJZCA/PyBxdWVyaWVkUHJvamVjdElkO1xuICAgIGNvbnN0IHRocmVhZFN0YXRlID0gdXNlVGFtYm9RdWVyeSh7XG4gICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgIGVuYWJsZWQ6ICEhY3VycmVudFByb2plY3RJZCxcbiAgICAgICAgcXVlcnlLZXk6IFtcInRocmVhZHNcIiwgY3VycmVudFByb2plY3RJZCwgY29udGV4dEtleV0sXG4gICAgICAgIHF1ZXJ5Rm46IGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgIGlmICghY3VycmVudFByb2plY3RJZCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgdGhyZWFkSXRlciA9IGF3YWl0IGNsaWVudC5iZXRhLnRocmVhZHMubGlzdChjdXJyZW50UHJvamVjdElkLCB7XG4gICAgICAgICAgICAgICAgY29udGV4dEtleSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIHRocmVhZEl0ZXI7XG4gICAgICAgIH0sXG4gICAgfSk7XG4gICAgcmV0dXJuIGN1cnJlbnRQcm9qZWN0SWQgPyB0aHJlYWRTdGF0ZSA6IHsgZGF0YTogbnVsbCwgLi4ucHJvamVjdElkU3RhdGUgfTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVzZS10YW1iby10aHJlYWRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-tambo-threads.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-thread-input.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/hooks/use-thread-input.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INPUT_ERROR_MESSAGES: () => (/* binding */ INPUT_ERROR_MESSAGES),\n/* harmony export */   useTamboThreadInput: () => (/* binding */ useTamboThreadInput)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _model_thread_input_error__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../model/thread-input-error */ \"(ssr)/./node_modules/@tambo-ai/react/esm/model/thread-input-error.js\");\n/* harmony import */ var _model_validate_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../model/validate-input */ \"(ssr)/./node_modules/@tambo-ai/react/esm/model/validate-input.js\");\n/* harmony import */ var _providers_tambo_thread_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../providers/tambo-thread-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-thread-provider.js\");\n/* harmony import */ var _react_query_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./react-query-hooks */ \"(ssr)/./node_modules/@tambo-ai/react/esm/hooks/react-query-hooks.js\");\n\n\n\n\n\n/**\n * Error messages for various input-related error scenarios\n * These messages are used to provide user-friendly error feedback\n * @readonly\n */\nconst INPUT_ERROR_MESSAGES = {\n    /** Error when attempting to submit empty input */\n    EMPTY: \"Message cannot be empty\",\n    /** Error when network connection fails */\n    NETWORK: \"Network error. Please check your connection\",\n    /** Error when server fails to process the request */\n    SERVER: \"Server error. Please try again\",\n    /** Error when input format is invalid */\n    VALIDATION: \"Invalid message format\",\n};\n/**\n * Hook for managing thread message input state and submission\n * @returns Interface for managing thread input state and submission\n */\nfunction useTamboThreadInput(contextKey) {\n    const { thread, inputValue, setInputValue, sendThreadMessage } = (0,_providers_tambo_thread_provider__WEBPACK_IMPORTED_MODULE_1__.useTamboThread)();\n    const submit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ({ contextKey: submitContextKey, streamResponse, forceToolChoice, } = {}) => {\n        const validation = (0,_model_validate_input__WEBPACK_IMPORTED_MODULE_2__.validateInput)(inputValue);\n        if (!validation.isValid) {\n            throw new _model_thread_input_error__WEBPACK_IMPORTED_MODULE_3__.ThreadInputError(`Cannot submit message: ${validation.error ?? INPUT_ERROR_MESSAGES.VALIDATION}`, { cause: validation.error });\n        }\n        await sendThreadMessage(validation.sanitizedInput, {\n            threadId: thread.id,\n            contextKey: submitContextKey ?? contextKey ?? undefined,\n            streamResponse: streamResponse,\n            forceToolChoice: forceToolChoice,\n        });\n        setInputValue(\"\");\n    }, [inputValue, sendThreadMessage, thread.id, contextKey, setInputValue]);\n    const { mutateAsync: submitAsync, mutate: _unusedSubmit, ...mutationState } = (0,_react_query_hooks__WEBPACK_IMPORTED_MODULE_4__.useTamboMutation)({\n        mutationFn: submit,\n    });\n    return {\n        ...mutationState,\n        value: inputValue,\n        setValue: setInputValue,\n        submit: submitAsync,\n    };\n}\n//# sourceMappingURL=use-thread-input.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-thread-input.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/mcp/mcp-client.js":
/*!************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/mcp/mcp-client.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MCPClient: () => (/* binding */ MCPClient),\n/* harmony export */   MCPTransport: () => (/* binding */ MCPTransport)\n/* harmony export */ });\n/* harmony import */ var _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/index.js */ \"(ssr)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\");\n/* harmony import */ var _modelcontextprotocol_sdk_client_sse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/sse.js */ \"(ssr)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/sse.js\");\n/* harmony import */ var _modelcontextprotocol_sdk_client_streamableHttp_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/streamableHttp.js */ \"(ssr)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js\");\n\n\n\nvar MCPTransport;\n(function (MCPTransport) {\n    MCPTransport[\"SSE\"] = \"sse\";\n    MCPTransport[\"HTTP\"] = \"http\";\n})(MCPTransport || (MCPTransport = {}));\n/**\n * A client for interacting with MCP (Model Context Protocol) servers.\n * Provides a simple interface for listing and calling tools exposed by the server.\n * @example\n * ```typescript\n * const mcp = await MCPClient.create('https://api.example.com/mcp');\n * const tools = await mcp.listTools();\n * const result = await mcp.callTool('toolName', { arg1: 'value1' });\n * ```\n */\nclass MCPClient {\n    client;\n    transport;\n    /**\n     * Private constructor to enforce using the static create method.\n     * @param endpoint - The URL of the MCP server to connect to\n     * @param transport - The transport to use for the MCP client\n     * @param headers - Optional custom headers to include in requests\n     */\n    constructor(endpoint, transport, headers) {\n        if (transport === MCPTransport.SSE) {\n            this.transport = new _modelcontextprotocol_sdk_client_sse_js__WEBPACK_IMPORTED_MODULE_1__.SSEClientTransport(new URL(endpoint), {\n                requestInit: { headers },\n            });\n        }\n        else {\n            this.transport = new _modelcontextprotocol_sdk_client_streamableHttp_js__WEBPACK_IMPORTED_MODULE_2__.StreamableHTTPClientTransport(new URL(endpoint), {\n                requestInit: { headers },\n            });\n        }\n        this.client = new _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__.Client({\n            name: \"tambo-mcp-client\",\n            version: \"1.0.0\",\n        });\n    }\n    /**\n     * Creates and initializes a new MCPClient instance.\n     * This is the recommended way to create an MCPClient as it handles both\n     * instantiation and connection setup.\n     * @param endpoint - The URL of the MCP server to connect to\n     * @param headers - Optional custom headers to include in requests\n     * @returns A connected MCPClient instance ready for use\n     * @throws Will throw an error if connection fails\n     */\n    static async create(endpoint, transport = MCPTransport.HTTP, headers) {\n        const mcpClient = new MCPClient(endpoint, transport, headers);\n        await mcpClient.client.connect(mcpClient.transport);\n        return mcpClient;\n    }\n    /**\n     * Retrieves a complete list of all available tools from the MCP server.\n     * Handles pagination automatically by following cursors until all tools are fetched.\n     * @returns A complete list of all available tools and their descriptions\n     * @throws Will throw an error if any server request fails during pagination\n     */\n    async listTools() {\n        const allTools = [];\n        let hasMore = true;\n        let cursor = undefined;\n        while (hasMore) {\n            const response = await this.client.listTools({ cursor }, {});\n            allTools.push(...response.tools.map((tool) => {\n                if (tool.inputSchema.type !== \"object\") {\n                    throw new Error(`Input schema for tool ${tool.name} is not an object`);\n                }\n                return {\n                    name: tool.name,\n                    description: tool.description,\n                    inputSchema: tool.inputSchema,\n                };\n            }));\n            if (response.nextCursor) {\n                cursor = response.nextCursor;\n            }\n            else {\n                hasMore = false;\n            }\n        }\n        return allTools;\n    }\n    /**\n     * Calls a specific tool on the MCP server with the provided arguments.\n     * @param name - The name of the tool to call\n     * @param args - Arguments to pass to the tool, must match the tool's expected schema\n     * @returns The result from the tool execution\n     * @throws Will throw an error if the tool call fails or if arguments are invalid\n     */\n    async callTool(name, args) {\n        const result = await this.client.callTool({\n            name,\n            arguments: args,\n        });\n        return result;\n    }\n}\n//# sourceMappingURL=mcp-client.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/mcp/mcp-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/mcp/tambo-mcp-provider.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/mcp/tambo-mcp-provider.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamboMcpProvider: () => (/* binding */ TamboMcpProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _providers_tambo_registry_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../providers/tambo-registry-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-registry-provider.js\");\n/* harmony import */ var _mcp_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mcp-client */ \"(ssr)/./node_modules/@tambo-ai/react/esm/mcp/mcp-client.js\");\n\n\n\n/**\n * This provider is used to register tools from MCP servers.\n * @returns the wrapped children\n */\nconst TamboMcpProvider = ({ mcpServers, children }) => {\n    const { registerTool } = (0,_providers_tambo_registry_provider__WEBPACK_IMPORTED_MODULE_1__.useTamboRegistry)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!mcpServers) {\n            return;\n        }\n        async function registerMcpServers(mcpServers) {\n            // Maps tool names to the MCP client that registered them\n            const mcpServerMap = new Map();\n            const serverToolLists = mcpServers.map(async (mcpServer) => {\n                const server = typeof mcpServer === \"string\"\n                    ? { url: mcpServer, transport: _mcp_client__WEBPACK_IMPORTED_MODULE_2__.MCPTransport.SSE }\n                    : mcpServer;\n                const { url, transport = _mcp_client__WEBPACK_IMPORTED_MODULE_2__.MCPTransport.SSE } = server;\n                const mcpClient = await _mcp_client__WEBPACK_IMPORTED_MODULE_2__.MCPClient.create(url, transport);\n                const tools = await mcpClient.listTools();\n                tools.forEach((tool) => {\n                    mcpServerMap.set(tool.name, mcpClient);\n                });\n                return tools;\n            });\n            const toolResults = await Promise.allSettled(serverToolLists);\n            // Just log the failed tools, we can't do anything about them\n            const failedTools = toolResults.filter((result) => result.status === \"rejected\");\n            if (failedTools.length > 0) {\n                console.error(\"Failed to register tools from MCP servers:\", failedTools.map((result) => result.reason));\n            }\n            // Register the successful tools\n            const allTools = toolResults\n                .filter((result) => result.status === \"fulfilled\")\n                .map((result) => result.value)\n                .flat();\n            allTools.forEach((tool) => {\n                registerTool({\n                    description: tool.description ?? \"\",\n                    name: tool.name,\n                    tool: async (args) => {\n                        const mcpServer = mcpServerMap.get(tool.name);\n                        if (!mcpServer) {\n                            // should never happen\n                            throw new Error(`MCP server for tool ${tool.name} not found`);\n                        }\n                        const result = await mcpServer.callTool(tool.name, args);\n                        if (result.isError) {\n                            // TODO: is there a better way to handle this?\n                            throw new Error(`${result.content}`);\n                        }\n                        return result.content;\n                    },\n                    toolSchema: tool.inputSchema,\n                });\n            });\n        }\n        registerMcpServers(mcpServers);\n    }, [mcpServers, registerTool]);\n    return children;\n};\n//# sourceMappingURL=tambo-mcp-provider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/mcp/tambo-mcp-provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/model/generate-component-response.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/model/generate-component-response.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GenerationStage: () => (/* binding */ GenerationStage),\n/* harmony export */   isIdleStage: () => (/* binding */ isIdleStage)\n/* harmony export */ });\nvar GenerationStage;\n(function (GenerationStage) {\n    GenerationStage[\"IDLE\"] = \"IDLE\";\n    GenerationStage[\"CHOOSING_COMPONENT\"] = \"CHOOSING_COMPONENT\";\n    GenerationStage[\"FETCHING_CONTEXT\"] = \"FETCHING_CONTEXT\";\n    GenerationStage[\"HYDRATING_COMPONENT\"] = \"HYDRATING_COMPONENT\";\n    GenerationStage[\"STREAMING_RESPONSE\"] = \"STREAMING_RESPONSE\";\n    GenerationStage[\"COMPLETE\"] = \"COMPLETE\";\n    GenerationStage[\"ERROR\"] = \"ERROR\";\n})(GenerationStage || (GenerationStage = {}));\n/**\n * Checks if the generation stage is in a state where it can accept user input.\n * @param generationStage - The generation stage to check\n * @returns True if the generation stage is idle, false otherwise\n */\nfunction isIdleStage(generationStage) {\n    return [\n        GenerationStage.IDLE,\n        GenerationStage.COMPLETE,\n        GenerationStage.ERROR,\n    ].includes(generationStage);\n}\n//# sourceMappingURL=generate-component-response.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3JlYWN0L2VzbS9tb2RlbC9nZW5lcmF0ZS1jb21wb25lbnQtcmVzcG9uc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDBDQUEwQztBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3JlYWN0L2VzbS9tb2RlbC9nZW5lcmF0ZS1jb21wb25lbnQtcmVzcG9uc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBHZW5lcmF0aW9uU3RhZ2U7XG4oZnVuY3Rpb24gKEdlbmVyYXRpb25TdGFnZSkge1xuICAgIEdlbmVyYXRpb25TdGFnZVtcIklETEVcIl0gPSBcIklETEVcIjtcbiAgICBHZW5lcmF0aW9uU3RhZ2VbXCJDSE9PU0lOR19DT01QT05FTlRcIl0gPSBcIkNIT09TSU5HX0NPTVBPTkVOVFwiO1xuICAgIEdlbmVyYXRpb25TdGFnZVtcIkZFVENISU5HX0NPTlRFWFRcIl0gPSBcIkZFVENISU5HX0NPTlRFWFRcIjtcbiAgICBHZW5lcmF0aW9uU3RhZ2VbXCJIWURSQVRJTkdfQ09NUE9ORU5UXCJdID0gXCJIWURSQVRJTkdfQ09NUE9ORU5UXCI7XG4gICAgR2VuZXJhdGlvblN0YWdlW1wiU1RSRUFNSU5HX1JFU1BPTlNFXCJdID0gXCJTVFJFQU1JTkdfUkVTUE9OU0VcIjtcbiAgICBHZW5lcmF0aW9uU3RhZ2VbXCJDT01QTEVURVwiXSA9IFwiQ09NUExFVEVcIjtcbiAgICBHZW5lcmF0aW9uU3RhZ2VbXCJFUlJPUlwiXSA9IFwiRVJST1JcIjtcbn0pKEdlbmVyYXRpb25TdGFnZSB8fCAoR2VuZXJhdGlvblN0YWdlID0ge30pKTtcbi8qKlxuICogQ2hlY2tzIGlmIHRoZSBnZW5lcmF0aW9uIHN0YWdlIGlzIGluIGEgc3RhdGUgd2hlcmUgaXQgY2FuIGFjY2VwdCB1c2VyIGlucHV0LlxuICogQHBhcmFtIGdlbmVyYXRpb25TdGFnZSAtIFRoZSBnZW5lcmF0aW9uIHN0YWdlIHRvIGNoZWNrXG4gKiBAcmV0dXJucyBUcnVlIGlmIHRoZSBnZW5lcmF0aW9uIHN0YWdlIGlzIGlkbGUsIGZhbHNlIG90aGVyd2lzZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNJZGxlU3RhZ2UoZ2VuZXJhdGlvblN0YWdlKSB7XG4gICAgcmV0dXJuIFtcbiAgICAgICAgR2VuZXJhdGlvblN0YWdlLklETEUsXG4gICAgICAgIEdlbmVyYXRpb25TdGFnZS5DT01QTEVURSxcbiAgICAgICAgR2VuZXJhdGlvblN0YWdlLkVSUk9SLFxuICAgIF0uaW5jbHVkZXMoZ2VuZXJhdGlvblN0YWdlKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdlbmVyYXRlLWNvbXBvbmVudC1yZXNwb25zZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/model/generate-component-response.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/model/thread-input-error.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/model/thread-input-error.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThreadInputError: () => (/* binding */ ThreadInputError)\n/* harmony export */ });\n/* harmony import */ var _tambo_ai_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tambo-ai/typescript-sdk */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/error.mjs\");\n\nclass ThreadInputError extends _tambo_ai_typescript_sdk__WEBPACK_IMPORTED_MODULE_0__.TamboAIError {\n}\n//# sourceMappingURL=thread-input-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3JlYWN0L2VzbS9tb2RlbC90aHJlYWQtaW5wdXQtZXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0Q7QUFDakQsK0JBQStCLGtFQUFZO0FBQ2xEO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B0YW1iby1haS9yZWFjdC9lc20vbW9kZWwvdGhyZWFkLWlucHV0LWVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRhbWJvQUlFcnJvciB9IGZyb20gXCJAdGFtYm8tYWkvdHlwZXNjcmlwdC1zZGtcIjtcbmV4cG9ydCBjbGFzcyBUaHJlYWRJbnB1dEVycm9yIGV4dGVuZHMgVGFtYm9BSUVycm9yIHtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRocmVhZC1pbnB1dC1lcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/model/thread-input-error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/model/validate-input.js":
/*!******************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/model/validate-input.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateInput: () => (/* binding */ validateInput)\n/* harmony export */ });\n/**\n * Validates the input of a message. Makes sure the message is not empty and is not too long.\n * @param input - The input to validate\n * @returns The validation result\n */\nfunction validateInput(input) {\n    const trimmed = input.trim();\n    if (trimmed.length === 0) {\n        return {\n            isValid: false,\n            error: new Error(\"Message cannot be empty\"),\n            sanitizedInput: trimmed,\n        };\n    }\n    // TODO(perf): Make this configurable if needed\n    if (trimmed.length > 10000) {\n        return {\n            isValid: false,\n            error: new Error(\"Message is too long (max 10000 characters)\"),\n            sanitizedInput: trimmed,\n        };\n    }\n    return {\n        isValid: true,\n        sanitizedInput: trimmed,\n    };\n}\n//# sourceMappingURL=validate-input.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3JlYWN0L2VzbS9tb2RlbC92YWxpZGF0ZS1pbnB1dC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B0YW1iby1haS9yZWFjdC9lc20vbW9kZWwvdmFsaWRhdGUtaW5wdXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBWYWxpZGF0ZXMgdGhlIGlucHV0IG9mIGEgbWVzc2FnZS4gTWFrZXMgc3VyZSB0aGUgbWVzc2FnZSBpcyBub3QgZW1wdHkgYW5kIGlzIG5vdCB0b28gbG9uZy5cbiAqIEBwYXJhbSBpbnB1dCAtIFRoZSBpbnB1dCB0byB2YWxpZGF0ZVxuICogQHJldHVybnMgVGhlIHZhbGlkYXRpb24gcmVzdWx0XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZUlucHV0KGlucHV0KSB7XG4gICAgY29uc3QgdHJpbW1lZCA9IGlucHV0LnRyaW0oKTtcbiAgICBpZiAodHJpbW1lZC5sZW5ndGggPT09IDApIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICAgICAgZXJyb3I6IG5ldyBFcnJvcihcIk1lc3NhZ2UgY2Fubm90IGJlIGVtcHR5XCIpLFxuICAgICAgICAgICAgc2FuaXRpemVkSW5wdXQ6IHRyaW1tZWQsXG4gICAgICAgIH07XG4gICAgfVxuICAgIC8vIFRPRE8ocGVyZik6IE1ha2UgdGhpcyBjb25maWd1cmFibGUgaWYgbmVlZGVkXG4gICAgaWYgKHRyaW1tZWQubGVuZ3RoID4gMTAwMDApIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICAgICAgZXJyb3I6IG5ldyBFcnJvcihcIk1lc3NhZ2UgaXMgdG9vIGxvbmcgKG1heCAxMDAwMCBjaGFyYWN0ZXJzKVwiKSxcbiAgICAgICAgICAgIHNhbml0aXplZElucHV0OiB0cmltbWVkLFxuICAgICAgICB9O1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICBpc1ZhbGlkOiB0cnVlLFxuICAgICAgICBzYW5pdGl6ZWRJbnB1dDogdHJpbW1lZCxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmFsaWRhdGUtaW5wdXQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/model/validate-input.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamboClientProvider: () => (/* binding */ TamboClientProvider),\n/* harmony export */   useTamboClient: () => (/* binding */ useTamboClient),\n/* harmony export */   useTamboQueryClient: () => (/* binding */ useTamboQueryClient)\n/* harmony export */ });\n/* harmony import */ var _tambo_ai_typescript_sdk__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tambo-ai/typescript-sdk */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/index.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\nconst TamboClientContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n/**\n * The TamboClientProvider is a React provider that provides a TamboAI client\n * and a query client to the descendants of the provider.\n * @param props - The props for the TamboClientProvider\n * @param props.children - The children to wrap\n * @param props.tamboUrl - The URL of the Tambo API\n * @param props.apiKey - The API key for the Tambo API\n * @param props.environment - The environment to use for the Tambo API\n * @returns The TamboClientProvider component\n */\nconst TamboClientProvider = ({ children, tamboUrl, apiKey, environment }) => {\n    const tamboConfig = { apiKey };\n    if (tamboUrl) {\n        tamboConfig.baseURL = tamboUrl;\n    }\n    if (environment) {\n        tamboConfig.environment = environment;\n    }\n    const [client] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new _tambo_ai_typescript_sdk__WEBPACK_IMPORTED_MODULE_1__[\"default\"](tamboConfig));\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient());\n    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TamboClientContext.Provider, { value: { client, queryClient } }, children));\n};\n/**\n * The useTamboClient hook provides access to the TamboAI client\n * to the descendants of the TamboClientProvider.\n * @returns The TamboAI client\n */\nconst useTamboClient = () => {\n    const context = react__WEBPACK_IMPORTED_MODULE_0___default().useContext(TamboClientContext);\n    if (context === undefined) {\n        throw new Error(\"useTamboClient must be used within a TamboClientProvider\");\n    }\n    return context.client;\n};\n/**\n * The useTamboQueryClient hook provides access to the tambo-specific query client\n * to the descendants of the TamboClientProvider.\n * @returns The tambo-specific query client\n * @private\n */\nconst useTamboQueryClient = () => {\n    const context = react__WEBPACK_IMPORTED_MODULE_0___default().useContext(TamboClientContext);\n    if (context === undefined) {\n        throw new Error(\"useTamboQueryClient must be used within a TamboClientProvider\");\n    }\n    return context.queryClient;\n};\n//# sourceMappingURL=tambo-client-provider.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3JlYWN0L2VzbS9wcm92aWRlcnMvdGFtYm8tY2xpZW50LXByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBK0M7QUFDSztBQUNHO0FBQ3ZELDJCQUEyQixvREFBYTtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLCtCQUErQix5Q0FBeUM7QUFDL0UsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiwrQ0FBUSxXQUFXLGdFQUFPO0FBQy9DLDBCQUEwQiwrQ0FBUSxXQUFXLDhEQUFXO0FBQ3hELFlBQVksMERBQW1CLGdDQUFnQyxTQUFTLHVCQUF1QjtBQUMvRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLG9CQUFvQix1REFBZ0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1Asb0JBQW9CLHVEQUFnQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B0YW1iby1haS9yZWFjdC9lc20vcHJvdmlkZXJzL3RhbWJvLWNsaWVudC1wcm92aWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgVGFtYm9BSSBmcm9tIFwiQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrXCI7XG5pbXBvcnQgeyBRdWVyeUNsaWVudCB9IGZyb20gXCJAdGFuc3RhY2svcmVhY3QtcXVlcnlcIjtcbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuY29uc3QgVGFtYm9DbGllbnRDb250ZXh0ID0gY3JlYXRlQ29udGV4dCh1bmRlZmluZWQpO1xuLyoqXG4gKiBUaGUgVGFtYm9DbGllbnRQcm92aWRlciBpcyBhIFJlYWN0IHByb3ZpZGVyIHRoYXQgcHJvdmlkZXMgYSBUYW1ib0FJIGNsaWVudFxuICogYW5kIGEgcXVlcnkgY2xpZW50IHRvIHRoZSBkZXNjZW5kYW50cyBvZiB0aGUgcHJvdmlkZXIuXG4gKiBAcGFyYW0gcHJvcHMgLSBUaGUgcHJvcHMgZm9yIHRoZSBUYW1ib0NsaWVudFByb3ZpZGVyXG4gKiBAcGFyYW0gcHJvcHMuY2hpbGRyZW4gLSBUaGUgY2hpbGRyZW4gdG8gd3JhcFxuICogQHBhcmFtIHByb3BzLnRhbWJvVXJsIC0gVGhlIFVSTCBvZiB0aGUgVGFtYm8gQVBJXG4gKiBAcGFyYW0gcHJvcHMuYXBpS2V5IC0gVGhlIEFQSSBrZXkgZm9yIHRoZSBUYW1ibyBBUElcbiAqIEBwYXJhbSBwcm9wcy5lbnZpcm9ubWVudCAtIFRoZSBlbnZpcm9ubWVudCB0byB1c2UgZm9yIHRoZSBUYW1ibyBBUElcbiAqIEByZXR1cm5zIFRoZSBUYW1ib0NsaWVudFByb3ZpZGVyIGNvbXBvbmVudFxuICovXG5leHBvcnQgY29uc3QgVGFtYm9DbGllbnRQcm92aWRlciA9ICh7IGNoaWxkcmVuLCB0YW1ib1VybCwgYXBpS2V5LCBlbnZpcm9ubWVudCB9KSA9PiB7XG4gICAgY29uc3QgdGFtYm9Db25maWcgPSB7IGFwaUtleSB9O1xuICAgIGlmICh0YW1ib1VybCkge1xuICAgICAgICB0YW1ib0NvbmZpZy5iYXNlVVJMID0gdGFtYm9Vcmw7XG4gICAgfVxuICAgIGlmIChlbnZpcm9ubWVudCkge1xuICAgICAgICB0YW1ib0NvbmZpZy5lbnZpcm9ubWVudCA9IGVudmlyb25tZW50O1xuICAgIH1cbiAgICBjb25zdCBbY2xpZW50XSA9IHVzZVN0YXRlKCgpID0+IG5ldyBUYW1ib0FJKHRhbWJvQ29uZmlnKSk7XG4gICAgY29uc3QgW3F1ZXJ5Q2xpZW50XSA9IHVzZVN0YXRlKCgpID0+IG5ldyBRdWVyeUNsaWVudCgpKTtcbiAgICByZXR1cm4gKFJlYWN0LmNyZWF0ZUVsZW1lbnQoVGFtYm9DbGllbnRDb250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiB7IGNsaWVudCwgcXVlcnlDbGllbnQgfSB9LCBjaGlsZHJlbikpO1xufTtcbi8qKlxuICogVGhlIHVzZVRhbWJvQ2xpZW50IGhvb2sgcHJvdmlkZXMgYWNjZXNzIHRvIHRoZSBUYW1ib0FJIGNsaWVudFxuICogdG8gdGhlIGRlc2NlbmRhbnRzIG9mIHRoZSBUYW1ib0NsaWVudFByb3ZpZGVyLlxuICogQHJldHVybnMgVGhlIFRhbWJvQUkgY2xpZW50XG4gKi9cbmV4cG9ydCBjb25zdCB1c2VUYW1ib0NsaWVudCA9ICgpID0+IHtcbiAgICBjb25zdCBjb250ZXh0ID0gUmVhY3QudXNlQ29udGV4dChUYW1ib0NsaWVudENvbnRleHQpO1xuICAgIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwidXNlVGFtYm9DbGllbnQgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFRhbWJvQ2xpZW50UHJvdmlkZXJcIik7XG4gICAgfVxuICAgIHJldHVybiBjb250ZXh0LmNsaWVudDtcbn07XG4vKipcbiAqIFRoZSB1c2VUYW1ib1F1ZXJ5Q2xpZW50IGhvb2sgcHJvdmlkZXMgYWNjZXNzIHRvIHRoZSB0YW1iby1zcGVjaWZpYyBxdWVyeSBjbGllbnRcbiAqIHRvIHRoZSBkZXNjZW5kYW50cyBvZiB0aGUgVGFtYm9DbGllbnRQcm92aWRlci5cbiAqIEByZXR1cm5zIFRoZSB0YW1iby1zcGVjaWZpYyBxdWVyeSBjbGllbnRcbiAqIEBwcml2YXRlXG4gKi9cbmV4cG9ydCBjb25zdCB1c2VUYW1ib1F1ZXJ5Q2xpZW50ID0gKCkgPT4ge1xuICAgIGNvbnN0IGNvbnRleHQgPSBSZWFjdC51c2VDb250ZXh0KFRhbWJvQ2xpZW50Q29udGV4dCk7XG4gICAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJ1c2VUYW1ib1F1ZXJ5Q2xpZW50IG11c3QgYmUgdXNlZCB3aXRoaW4gYSBUYW1ib0NsaWVudFByb3ZpZGVyXCIpO1xuICAgIH1cbiAgICByZXR1cm4gY29udGV4dC5xdWVyeUNsaWVudDtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD10YW1iby1jbGllbnQtcHJvdmlkZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-component-provider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/providers/tambo-component-provider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamboComponentProvider: () => (/* binding */ TamboComponentProvider),\n/* harmony export */   useTamboComponent: () => (/* binding */ useTamboComponent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tambo_client_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tambo-client-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js\");\n/* harmony import */ var _tambo_registry_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tambo-registry-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-registry-provider.js\");\n/* __next_internal_client_entry_do_not_use__ TamboComponentProvider,useTamboComponent auto */ \n\n\nconst TamboComponentContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    /**\n     *\n     */ registerComponent: ()=>{},\n    /**\n     *\n     */ registerTool: ()=>{},\n    /**\n     *\n     */ registerTools: ()=>{},\n    /**\n     *\n     */ addToolAssociation: ()=>{}\n});\n/**\n * The TamboComponentProvider is a React provider that provides component\n * registration services to the descendants of the provider.\n * @param props - The props for the TamboComponentProvider\n * @param props.children - The children to wrap\n * @returns The TamboComponentProvider component\n */ const TamboComponentProvider = ({ children })=>{\n    const client = (0,_tambo_client_provider__WEBPACK_IMPORTED_MODULE_1__.useTamboClient)();\n    const { registerComponent, addToolAssociation, registerTool, registerTools } = (0,_tambo_registry_provider__WEBPACK_IMPORTED_MODULE_2__.useTamboRegistry)();\n    const value = {\n        client,\n        registerComponent,\n        registerTool,\n        registerTools,\n        addToolAssociation\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TamboComponentContext.Provider, {\n        value: value\n    }, children);\n};\n/**\n * The useTamboComponent hook provides access to the component registration\n * services to the descendants of the TamboComponentProvider.\n * @returns The component registration services\n */ const useTamboComponent = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TamboComponentContext);\n}; //# sourceMappingURL=tambo-component-provider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-component-provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-provider.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/providers/tambo-provider.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamboContext: () => (/* binding */ TamboContext),\n/* harmony export */   TamboProvider: () => (/* binding */ TamboProvider),\n/* harmony export */   useTambo: () => (/* binding */ useTambo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tambo_client_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tambo-client-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js\");\n/* harmony import */ var _tambo_component_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tambo-component-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-component-provider.js\");\n/* harmony import */ var _tambo_registry_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tambo-registry-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-registry-provider.js\");\n/* harmony import */ var _tambo_thread_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tambo-thread-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-thread-provider.js\");\n/* __next_internal_client_entry_do_not_use__ TamboProvider,TamboContext,useTambo auto */ \n\n\n\n\n/**\n * The TamboProvider gives full access to the whole Tambo API. This includes the\n * TamboAI client, the component registry, and the current thread context.\n * @param props - The props for the TamboProvider\n * @param props.children - The children to wrap\n * @param props.tamboUrl - The URL of the Tambo API\n * @param props.apiKey - The API key for the Tambo API\n * @param props.components - The components to register\n * @param props.environment - The environment to use for the Tambo API\n * @param props.tools - The tools to register\n * @returns The TamboProvider component\n */ const TamboProvider = ({ children, tamboUrl, apiKey, components, environment, tools })=>{\n    // Should only be used in browser\n    if (true) {\n        console.error(\"TamboProvider must be used within a browser\");\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tambo_client_provider__WEBPACK_IMPORTED_MODULE_1__.TamboClientProvider, {\n        tamboUrl: tamboUrl,\n        apiKey: apiKey,\n        environment: environment\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tambo_registry_provider__WEBPACK_IMPORTED_MODULE_2__.TamboRegistryProvider, {\n        components: components,\n        tools: tools\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tambo_thread_provider__WEBPACK_IMPORTED_MODULE_3__.TamboThreadProvider, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tambo_component_provider__WEBPACK_IMPORTED_MODULE_4__.TamboComponentProvider, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TamboCompositeProvider, null, children)))));\n};\nconst TamboContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\n/**\n * TamboCompositeProvider is a provider that combines the TamboClient,\n * TamboThread, and TamboComponent providers\n * @param props - The props for the TamboCompositeProvider\n * @param props.children - The children to wrap\n * @returns The wrapped component\n */ const TamboCompositeProvider = ({ children })=>{\n    const threads = (0,_tambo_thread_provider__WEBPACK_IMPORTED_MODULE_3__.useTamboThread)();\n    const client = (0,_tambo_client_provider__WEBPACK_IMPORTED_MODULE_1__.useTamboClient)();\n    const queryClient = (0,_tambo_client_provider__WEBPACK_IMPORTED_MODULE_1__.useTamboQueryClient)();\n    const componentRegistry = (0,_tambo_component_provider__WEBPACK_IMPORTED_MODULE_4__.useTamboComponent)();\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TamboContext.Provider, {\n        value: {\n            client,\n            queryClient,\n            ...componentRegistry,\n            ...threads\n        }\n    }, children);\n};\n/**\n * The useTambo hook provides access to the Tambo API. This is the primary entrypoint\n * for the Tambo React SDK.\n *\n * This includes the TamboAI client, the component registry, and the current thread context.\n * @returns The Tambo API\n */ const useTambo = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TamboContext);\n}; //# sourceMappingURL=tambo-provider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-registry-provider.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/providers/tambo-registry-provider.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TamboRegistryProvider: () => (/* binding */ TamboRegistryProvider),\n/* harmony export */   useTamboRegistry: () => (/* binding */ useTamboRegistry)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod-to-json-schema */ \"(ssr)/./node_modules/zod-to-json-schema/dist/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ TamboRegistryProvider,useTamboRegistry auto */ \n\n\nconst TamboRegistryContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    componentList: {},\n    toolRegistry: {},\n    componentToolAssociations: {},\n    /**\n     *\n     */ registerComponent: ()=>{},\n    /**\n     *\n     */ registerTool: ()=>{},\n    /**\n     *\n     */ registerTools: ()=>{},\n    /**\n     *\n     */ addToolAssociation: ()=>{}\n});\n/**\n * The TamboRegistryProvider is a React provider that provides a component\n * registry to the descendants of the provider.\n * @param props - The props for the TamboRegistryProvider\n * @param props.children - The children to wrap\n * @param props.components - The components to register\n * @param props.tools - The tools to register\n * @returns The TamboRegistryProvider component\n */ const TamboRegistryProvider = ({ children, components: userComponents, tools: userTools })=>{\n    const [componentList, setComponentList] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [toolRegistry, setToolRegistry] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [componentToolAssociations, setComponentToolAssociations] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const registerTool = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboRegistryProvider.useCallback[registerTool]\": (tool, warnOnOverwrite = true)=>{\n            setToolRegistry({\n                \"TamboRegistryProvider.useCallback[registerTool]\": (prev)=>{\n                    if (prev[tool.name] && warnOnOverwrite) {\n                        console.warn(`Overwriting tool ${tool.name}`);\n                    }\n                    return {\n                        ...prev,\n                        [tool.name]: tool\n                    };\n                }\n            }[\"TamboRegistryProvider.useCallback[registerTool]\"]);\n        }\n    }[\"TamboRegistryProvider.useCallback[registerTool]\"], []);\n    const registerTools = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboRegistryProvider.useCallback[registerTools]\": (tools, warnOnOverwrite = true)=>{\n            tools.forEach({\n                \"TamboRegistryProvider.useCallback[registerTools]\": (tool)=>registerTool(tool, warnOnOverwrite)\n            }[\"TamboRegistryProvider.useCallback[registerTools]\"]);\n        }\n    }[\"TamboRegistryProvider.useCallback[registerTools]\"], [\n        registerTool\n    ]);\n    const addToolAssociation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboRegistryProvider.useCallback[addToolAssociation]\": (componentName, tool)=>{\n            if (!componentList[componentName]) {\n                throw new Error(`Component ${componentName} not found in registry`);\n            }\n            setComponentToolAssociations({\n                \"TamboRegistryProvider.useCallback[addToolAssociation]\": (prev)=>({\n                        ...prev,\n                        [componentName]: [\n                            ...prev[componentName] || [],\n                            tool.name\n                        ]\n                    })\n            }[\"TamboRegistryProvider.useCallback[addToolAssociation]\"]);\n        }\n    }[\"TamboRegistryProvider.useCallback[addToolAssociation]\"], [\n        componentList\n    ]);\n    const registerComponent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboRegistryProvider.useCallback[registerComponent]\": (options, warnOnOverwrite = true)=>{\n            const { name, description, component, propsSchema, propsDefinition, loadingComponent, associatedTools } = options;\n            // Validate that at least one props definition is provided\n            if (!propsSchema && !propsDefinition) {\n                throw new Error(`Component ${name} must have either propsSchema (recommended) or propsDefinition defined`);\n            }\n            // Validate that only one props definition is provided\n            if (propsSchema && propsDefinition) {\n                throw new Error(`Component ${name} cannot have both propsSchema and propsDefinition defined. Use only one. We recommend using propsSchema.`);\n            }\n            // Convert propsSchema to JSON Schema if it exists\n            const props = getSerializedProps(propsDefinition, propsSchema, name);\n            setComponentList({\n                \"TamboRegistryProvider.useCallback[registerComponent]\": (prev)=>{\n                    if (prev[name] && warnOnOverwrite) {\n                        console.warn(`overwriting component ${name}`);\n                    }\n                    return {\n                        ...prev,\n                        [name]: {\n                            component,\n                            loadingComponent,\n                            name,\n                            description,\n                            props,\n                            contextTools: []\n                        }\n                    };\n                }\n            }[\"TamboRegistryProvider.useCallback[registerComponent]\"]);\n            if (associatedTools) {\n                registerTools(associatedTools);\n                setComponentToolAssociations({\n                    \"TamboRegistryProvider.useCallback[registerComponent]\": (prev)=>({\n                            ...prev,\n                            [name]: associatedTools.map({\n                                \"TamboRegistryProvider.useCallback[registerComponent]\": (tool)=>tool.name\n                            }[\"TamboRegistryProvider.useCallback[registerComponent]\"])\n                        })\n                }[\"TamboRegistryProvider.useCallback[registerComponent]\"]);\n            }\n        }\n    }[\"TamboRegistryProvider.useCallback[registerComponent]\"], [\n        registerTools\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"TamboRegistryProvider.useEffect\": ()=>{\n            if (userComponents) {\n                userComponents.forEach({\n                    \"TamboRegistryProvider.useEffect\": (component)=>{\n                        registerComponent(component, false);\n                    }\n                }[\"TamboRegistryProvider.useEffect\"]);\n            }\n        }\n    }[\"TamboRegistryProvider.useEffect\"], [\n        registerComponent,\n        userComponents\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"TamboRegistryProvider.useEffect\": ()=>{\n            if (userTools) {\n                registerTools(userTools, false);\n            }\n        }\n    }[\"TamboRegistryProvider.useEffect\"], [\n        registerTools,\n        userTools\n    ]);\n    const value = {\n        componentList,\n        toolRegistry,\n        componentToolAssociations,\n        registerComponent,\n        registerTool,\n        registerTools,\n        addToolAssociation\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TamboRegistryContext.Provider, {\n        value: value\n    }, children);\n};\n/**\n * The useTamboRegistry hook provides access to the component registry\n * to the descendants of the TamboRegistryProvider.\n * @returns The component registry\n */ const useTamboRegistry = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TamboRegistryContext);\n};\nfunction getSerializedProps(propsDefinition, propsSchema, name) {\n    if (propsDefinition) {\n        console.warn(`propsDefinition is deprecated. Use propsSchema instead.`);\n        return propsDefinition;\n    }\n    if (isZodSchema(propsSchema)) {\n        try {\n            return (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(propsSchema);\n        } catch (error) {\n            console.error(`Error converting ${name} props schema to JSON Schema:`, error);\n        }\n    }\n    // try to roughly detect JSONSchema, should always be an object with a properties key\n    if (isJSONSchema(propsSchema)) {\n        return propsSchema;\n    }\n    throw new Error(`Invalid props schema for ${name}`);\n}\n/**\n * Checks if the propsSchema is a JSON Schema. This is a rough check, and the\n * server will provide the definitive check.\n * @param propsSchema - The props schema to check\n * @returns True if the props schema is a JSON Schema, false otherwise\n */ function isJSONSchema(propsSchema) {\n    return propsSchema && typeof propsSchema === \"object\" && propsSchema.type === \"object\" && propsSchema.properties;\n}\n/**\n * Since we require a certain zod version, we need to check if the object is a ZodSchema\n * @param obj - The object to check\n * @returns True if the object is a ZodSchema, false otherwise\n */ function isZodSchema(obj) {\n    if (obj instanceof zod__WEBPACK_IMPORTED_MODULE_1__.ZodSchema) {\n        return true;\n    }\n    // try to detect if the object is a ZodSchema\n    return typeof obj === \"object\" && obj !== null && typeof obj.safeParse === \"function\" && typeof obj._def === \"object\";\n} //# sourceMappingURL=tambo-registry-provider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3JlYWN0L2VzbS9wcm92aWRlcnMvdGFtYm8tcmVnaXN0cnktcHJvdmlkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OzRGQVFlO0FBQ2lCO0FBQ2lCO0FBaUJqRCxNQUFNLG9CQUFvQixpQkFBRyxvREFBYSxDQUF1QjtJQUMvRCxhQUFhLEVBQUUsRUFBRTtJQUNqQixZQUFZLEVBQUUsRUFBRTtJQUNoQix5QkFBeUIsRUFBRSxFQUFFO0lBQzdCOztPQUVHLENBQ0gsaUJBQWlCLEVBQUUsR0FBRyxFQUFFLENBQUc7SUFDM0I7O09BRUcsQ0FDSCxZQUFZLEVBQUUsR0FBRyxFQUFJLENBQUM7SUFDdEI7O09BRUcsQ0FDSCxhQUFhLEVBQUUsR0FBRyxFQUFFLENBQUc7SUFDdkI7O09BRUcsQ0FDSCxrQkFBa0IsRUFBRSxHQUFHLEVBQUksQ0FBQztDQUM3QixDQUFDLENBQUM7QUFTSDs7Ozs7Ozs7R0FRRyxDQUNJLE1BQU0scUJBQXFCLEdBRTlCLENBQUMsRUFBRSxRQUFRLEVBQUUsVUFBVSxFQUFFLGNBQWMsRUFBRSxLQUFLLEVBQUUsU0FBUyxFQUFFLEVBQUUsRUFBRTtJQUNqRSxNQUFNLENBQUMsYUFBYSxFQUFFLGdCQUFnQixDQUFDLEdBQUcsK0NBQVEsQ0FBb0IsRUFBRSxDQUFDLENBQUM7SUFDMUUsTUFBTSxDQUFDLFlBQVksRUFBRSxlQUFlLENBQUMsR0FBRywrQ0FBUSxDQUM5QyxFQUFFLENBQ0gsQ0FBQztJQUNGLE1BQU0sQ0FBQyx5QkFBeUIsRUFBRSw0QkFBNEIsQ0FBQyxHQUFHLCtDQUFRLENBRXhFLEVBQUUsQ0FBQyxDQUFDO0lBRU4sTUFBTSxZQUFZLEdBQUcsa0RBQVc7MkRBQzlCLENBQUMsSUFBZSxFQUFFLGVBQWUsR0FBRyxJQUFJLEVBQUUsRUFBRTtZQUMxQyxlQUFlO21FQUFDLENBQUMsSUFBSSxFQUFFLEVBQUU7b0JBQ3ZCLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxlQUFlLEVBQUUsQ0FBQzt3QkFDdkMsT0FBTyxDQUFDLElBQUksQ0FBQyxvQkFBb0IsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7b0JBQ2hELENBQUM7b0JBQ0QsT0FBTzt3QkFDTCxHQUFHLElBQUk7d0JBQ1AsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSTtxQkFDbEIsQ0FBQztnQkFDSixDQUFDOztRQUNILENBQUM7MERBQ0QsRUFBRSxDQUNILENBQUM7SUFFRixNQUFNLGFBQWEsR0FBRyxrREFBVzs0REFDL0IsQ0FBQyxLQUFrQixFQUFFLGVBQWUsR0FBRyxJQUFJLEVBQUUsRUFBRTtZQUM3QyxLQUFLLENBQUMsT0FBTztvRUFBQyxDQUFDLElBQUksRUFBRSxDQUFHLENBQUQsV0FBYSxDQUFDLElBQUksRUFBRSxlQUFlLENBQUM7O1FBQzdELENBQUM7MkRBQ0Q7UUFBQyxZQUFZO0tBQUMsQ0FDZixDQUFDO0lBRUYsTUFBTSxrQkFBa0IsR0FBRyxrREFBVztpRUFDcEMsQ0FBQyxhQUFxQixFQUFFLElBQWUsRUFBRSxFQUFFO1lBQ3pDLElBQUksQ0FBQyxhQUFhLENBQUMsYUFBYSxDQUFDLEVBQUUsQ0FBQztnQkFDbEMsTUFBTSxJQUFJLEtBQUssQ0FBQyxhQUFhLGFBQWEsd0JBQXdCLENBQUMsQ0FBQztZQUN0RSxDQUFDO1lBQ0QsNEJBQTRCO3lFQUFDLENBQUMsSUFBSSxFQUFFLEVBQUUsQ0FBQzt3QkFDckMsR0FBRyxJQUFJO3dCQUNQLENBQUMsYUFBYSxDQUFDLEVBQUUsQ0FBQzsrQkFBSSxJQUFJLENBQUMsYUFBYSxDQUFDLElBQUksRUFBRSxDQUFDOzRCQUFFLElBQUksQ0FBQyxJQUFJO3lCQUFDO3NCQUM3RDs7UUFDSCxDQUFDO2dFQUNEO1FBQUMsYUFBYTtLQUFDLENBQ2hCLENBQUM7SUFFRixNQUFNLGlCQUFpQixHQUFHLGtEQUFXO2dFQUNuQyxDQUFDLE9BQXVCLEVBQUUsZUFBZSxHQUFHLElBQUksRUFBRSxFQUFFO1lBQ2xELE1BQU0sRUFDSixJQUFJLEVBQ0osV0FBVyxFQUNYLFNBQVMsRUFDVCxXQUFXLEVBQ1gsZUFBZSxFQUNmLGdCQUFnQixFQUNoQixlQUFlLEVBQ2hCLEdBQUcsT0FBTyxDQUFDO1lBRVosMERBQTBEO1lBQzFELElBQUksQ0FBQyxXQUFXLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztnQkFDckMsTUFBTSxJQUFJLEtBQUssQ0FDYixhQUFhLElBQUksd0VBQXdFLENBQzFGLENBQUM7WUFDSixDQUFDO1lBRUQsc0RBQXNEO1lBQ3RELElBQUksV0FBVyxJQUFJLGVBQWUsRUFBRSxDQUFDO2dCQUNuQyxNQUFNLElBQUksS0FBSyxDQUNiLGFBQWEsSUFBSSwwR0FBMEcsQ0FDNUgsQ0FBQztZQUNKLENBQUM7WUFFRCxrREFBa0Q7WUFDbEQsTUFBTSxLQUFLLEdBQUcsa0JBQWtCLENBQUMsZUFBZSxFQUFFLFdBQVcsRUFBRSxJQUFJLENBQUMsQ0FBQztZQUVyRSxnQkFBZ0I7d0VBQUMsQ0FBQyxJQUFJLEVBQUUsRUFBRTtvQkFDeEIsSUFBSSxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUksZUFBZSxFQUFFLENBQUM7d0JBQ2xDLE9BQU8sQ0FBQyxJQUFJLENBQUMseUJBQXlCLElBQUksRUFBRSxDQUFDLENBQUM7b0JBQ2hELENBQUM7b0JBQ0QsT0FBTzt3QkFDTCxHQUFHLElBQUk7d0JBQ1AsQ0FBQyxJQUFJLENBQUMsRUFBRTs0QkFDTixTQUFTOzRCQUNULGdCQUFnQjs0QkFDaEIsSUFBSTs0QkFDSixXQUFXOzRCQUNYLEtBQUs7NEJBQ0wsWUFBWSxFQUFFLEVBQUU7eUJBQ2pCO3FCQUNGLENBQUM7Z0JBQ0osQ0FBQzs7WUFDRCxJQUFJLGVBQWUsRUFBRSxDQUFDO2dCQUNwQixhQUFhLENBQUMsZUFBZSxDQUFDLENBQUM7Z0JBQy9CLDRCQUE0Qjs0RUFBQyxDQUFDLElBQUksRUFBRSxFQUFFLENBQUM7NEJBQ3JDLEdBQUcsSUFBSTs0QkFDUCxDQUFDLElBQUksQ0FBQyxFQUFFLGVBQWUsQ0FBQyxHQUFHO3dGQUFDLENBQUMsSUFBSSxFQUFFLENBQUcsQ0FBRCxHQUFLLENBQUMsSUFBSTs7MEJBQ2hEOztZQUNILENBQUM7UUFDSCxDQUFDOytEQUNEO1FBQUMsYUFBYTtLQUFDLENBQ2hCLENBQUM7SUFDRixnREFBUzsyQ0FBQyxHQUFHLEVBQUU7WUFDYixJQUFJLGNBQWMsRUFBRSxDQUFDO2dCQUNuQixjQUFjLENBQUMsT0FBTzt1REFBQyxDQUFDLFNBQVMsRUFBRSxFQUFFO3dCQUNuQyxpQkFBaUIsQ0FBQyxTQUFTLEVBQUUsS0FBSyxDQUFDLENBQUM7b0JBQ3RDLENBQUM7O1lBQ0gsQ0FBQztRQUNILENBQUM7MENBQUU7UUFBQyxpQkFBaUI7UUFBRSxjQUFjO0tBQUMsQ0FBQyxDQUFDO0lBRXhDLGdEQUFTOzJDQUFDLEdBQUcsRUFBRTtZQUNiLElBQUksU0FBUyxFQUFFLENBQUM7Z0JBQ2QsYUFBYSxDQUFDLFNBQVMsRUFBRSxLQUFLLENBQUMsQ0FBQztZQUNsQyxDQUFDO1FBQ0gsQ0FBQzswQ0FBRTtRQUFDLGFBQWE7UUFBRSxTQUFTO0tBQUMsQ0FBQyxDQUFDO0lBRS9CLE1BQU0sS0FBSyxHQUFHO1FBQ1osYUFBYTtRQUNiLFlBQVk7UUFDWix5QkFBeUI7UUFDekIsaUJBQWlCO1FBQ2pCLFlBQVk7UUFDWixhQUFhO1FBQ2Isa0JBQWtCO0tBQ25CLENBQUM7SUFFRixPQUFPLGNBQ0wsMkRBQUMsb0JBQW9CLENBQUMsUUFBUTtRQUFDLEtBQUssRUFBRSxLQUFLO0lBQUEsR0FDeEMsUUFBUSxDQUNxQixDQUNqQztBQUNILENBQUMsQ0FBQztBQUVGOzs7O0dBSUcsQ0FDSSxNQUFNLGdCQUFnQixHQUFHLEdBQUcsRUFBRTtJQUNuQyxPQUFPLGlEQUFVLENBQUMsb0JBQW9CLENBQUMsQ0FBQztBQUMxQyxDQUFDLENBQUM7QUFDRixTQUFTLGtCQUFrQixDQUN6QixlQUFvQixFQUNwQixXQUFnQixFQUNoQixJQUFZO0lBRVosSUFBSSxlQUFlLEVBQUUsQ0FBQztRQUNwQixPQUFPLENBQUMsSUFBSSxDQUFDLHlEQUF5RCxDQUFDLENBQUM7UUFDeEUsT0FBTyxlQUFlLENBQUM7SUFDekIsQ0FBQztJQUVELElBQUksV0FBVyxDQUFDLFdBQVcsQ0FBQyxFQUFFLENBQUM7UUFDN0IsSUFBSSxDQUFDO1lBQ0gsT0FBTyw4REFBZSxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBQ3RDLENBQUMsQ0FBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsT0FBTyxDQUFDLEtBQUssQ0FDWCxvQkFBb0IsSUFBSSwrQkFBK0IsRUFDdkQsS0FBSyxDQUNOLENBQUM7UUFDSixDQUFDO0lBQ0gsQ0FBQztJQUNELHFGQUFxRjtJQUNyRixJQUFJLFlBQVksQ0FBQyxXQUFXLENBQUMsRUFBRSxDQUFDO1FBQzlCLE9BQU8sV0FBVyxDQUFDO0lBQ3JCLENBQUM7SUFFRCxNQUFNLElBQUksS0FBSyxDQUFDLDRCQUE0QixJQUFJLEVBQUUsQ0FBQyxDQUFDO0FBQ3RELENBQUM7QUFFRDs7Ozs7R0FLRyxDQUNILFNBQVMsWUFBWSxDQUFDLFdBQWdCO0lBQ3BDLE9BQU8sV0FDTSxJQUNYLE9BQU8sV0FBVyxLQUFLLFFBQVEsSUFDL0IsV0FBVyxDQUFDLElBQUksS0FBSyxRQUFRLElBQzdCLFdBQVcsQ0FBQyxVQUFVLENBQ3ZCO0FBQ0gsQ0FBQztBQUVEOzs7O0dBSUcsQ0FDSCxTQUFTLFdBQVcsQ0FBQyxHQUFZO0lBQy9CLElBQUksR0FBRyxZQUFZLDBDQUFTLEVBQUUsQ0FBQztRQUM3QixPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFDRCw2Q0FBNkM7SUFDN0MsT0FBTyxPQUNFLEdBQUcsS0FBSyxRQUFRLElBQ3ZCLEdBQUcsS0FBSyxJQUFJLElBQ1osT0FBUSxHQUFXLENBQUMsU0FBUyxLQUFLLFVBQVUsSUFDNUMsT0FBUSxHQUFXLENBQUMsSUFBSSxLQUFLLFFBQVEsQ0FDdEM7QUFDSCxDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vc3JjL3Byb3ZpZGVycy90YW1iby1yZWdpc3RyeS1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgUmVhY3QsIHtcbiAgY3JlYXRlQ29udGV4dCxcbiAgUHJvcHNXaXRoQ2hpbGRyZW4sXG4gIHVzZUNhbGxiYWNrLFxuICB1c2VDb250ZXh0LFxuICB1c2VFZmZlY3QsXG4gIHVzZVN0YXRlLFxufSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IFpvZFNjaGVtYSB9IGZyb20gXCJ6b2RcIjtcbmltcG9ydCB6b2RUb0pzb25TY2hlbWEgZnJvbSBcInpvZC10by1qc29uLXNjaGVtYVwiO1xuaW1wb3J0IHtcbiAgQ29tcG9uZW50UmVnaXN0cnksXG4gIFRhbWJvQ29tcG9uZW50LFxuICBUYW1ib1Rvb2wsXG59IGZyb20gXCIuLi9tb2RlbC9jb21wb25lbnQtbWV0YWRhdGFcIjtcblxuZXhwb3J0IGludGVyZmFjZSBUYW1ib1JlZ2lzdHJ5Q29udGV4dCB7XG4gIGNvbXBvbmVudExpc3Q6IENvbXBvbmVudFJlZ2lzdHJ5O1xuICB0b29sUmVnaXN0cnk6IFJlY29yZDxzdHJpbmcsIFRhbWJvVG9vbD47XG4gIGNvbXBvbmVudFRvb2xBc3NvY2lhdGlvbnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZ1tdPjtcbiAgcmVnaXN0ZXJDb21wb25lbnQ6IChvcHRpb25zOiBUYW1ib0NvbXBvbmVudCkgPT4gdm9pZDtcbiAgcmVnaXN0ZXJUb29sOiAodG9vbDogVGFtYm9Ub29sKSA9PiB2b2lkO1xuICByZWdpc3RlclRvb2xzOiAodG9vbHM6IFRhbWJvVG9vbFtdKSA9PiB2b2lkO1xuICBhZGRUb29sQXNzb2NpYXRpb246IChjb21wb25lbnROYW1lOiBzdHJpbmcsIHRvb2w6IFRhbWJvVG9vbCkgPT4gdm9pZDtcbn1cblxuY29uc3QgVGFtYm9SZWdpc3RyeUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRhbWJvUmVnaXN0cnlDb250ZXh0Pih7XG4gIGNvbXBvbmVudExpc3Q6IHt9LFxuICB0b29sUmVnaXN0cnk6IHt9LFxuICBjb21wb25lbnRUb29sQXNzb2NpYXRpb25zOiB7fSxcbiAgLyoqXG4gICAqXG4gICAqL1xuICByZWdpc3RlckNvbXBvbmVudDogKCkgPT4ge30sXG4gIC8qKlxuICAgKlxuICAgKi9cbiAgcmVnaXN0ZXJUb29sOiAoKSA9PiB7fSxcbiAgLyoqXG4gICAqXG4gICAqL1xuICByZWdpc3RlclRvb2xzOiAoKSA9PiB7fSxcbiAgLyoqXG4gICAqXG4gICAqL1xuICBhZGRUb29sQXNzb2NpYXRpb246ICgpID0+IHt9LFxufSk7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVGFtYm9SZWdpc3RyeVByb3ZpZGVyUHJvcHMge1xuICAvKiogVGhlIGNvbXBvbmVudHMgdG8gcmVnaXN0ZXIgKi9cbiAgY29tcG9uZW50cz86IFRhbWJvQ29tcG9uZW50W107XG4gIC8qKiBUaGUgdG9vbHMgdG8gcmVnaXN0ZXIgKi9cbiAgdG9vbHM/OiBUYW1ib1Rvb2xbXTtcbn1cblxuLyoqXG4gKiBUaGUgVGFtYm9SZWdpc3RyeVByb3ZpZGVyIGlzIGEgUmVhY3QgcHJvdmlkZXIgdGhhdCBwcm92aWRlcyBhIGNvbXBvbmVudFxuICogcmVnaXN0cnkgdG8gdGhlIGRlc2NlbmRhbnRzIG9mIHRoZSBwcm92aWRlci5cbiAqIEBwYXJhbSBwcm9wcyAtIFRoZSBwcm9wcyBmb3IgdGhlIFRhbWJvUmVnaXN0cnlQcm92aWRlclxuICogQHBhcmFtIHByb3BzLmNoaWxkcmVuIC0gVGhlIGNoaWxkcmVuIHRvIHdyYXBcbiAqIEBwYXJhbSBwcm9wcy5jb21wb25lbnRzIC0gVGhlIGNvbXBvbmVudHMgdG8gcmVnaXN0ZXJcbiAqIEBwYXJhbSBwcm9wcy50b29scyAtIFRoZSB0b29scyB0byByZWdpc3RlclxuICogQHJldHVybnMgVGhlIFRhbWJvUmVnaXN0cnlQcm92aWRlciBjb21wb25lbnRcbiAqL1xuZXhwb3J0IGNvbnN0IFRhbWJvUmVnaXN0cnlQcm92aWRlcjogUmVhY3QuRkM8XG4gIFByb3BzV2l0aENoaWxkcmVuPFRhbWJvUmVnaXN0cnlQcm92aWRlclByb3BzPlxuPiA9ICh7IGNoaWxkcmVuLCBjb21wb25lbnRzOiB1c2VyQ29tcG9uZW50cywgdG9vbHM6IHVzZXJUb29scyB9KSA9PiB7XG4gIGNvbnN0IFtjb21wb25lbnRMaXN0LCBzZXRDb21wb25lbnRMaXN0XSA9IHVzZVN0YXRlPENvbXBvbmVudFJlZ2lzdHJ5Pih7fSk7XG4gIGNvbnN0IFt0b29sUmVnaXN0cnksIHNldFRvb2xSZWdpc3RyeV0gPSB1c2VTdGF0ZTxSZWNvcmQ8c3RyaW5nLCBUYW1ib1Rvb2w+PihcbiAgICB7fSxcbiAgKTtcbiAgY29uc3QgW2NvbXBvbmVudFRvb2xBc3NvY2lhdGlvbnMsIHNldENvbXBvbmVudFRvb2xBc3NvY2lhdGlvbnNdID0gdXNlU3RhdGU8XG4gICAgUmVjb3JkPHN0cmluZywgc3RyaW5nW10+XG4gID4oe30pO1xuXG4gIGNvbnN0IHJlZ2lzdGVyVG9vbCA9IHVzZUNhbGxiYWNrKFxuICAgICh0b29sOiBUYW1ib1Rvb2wsIHdhcm5Pbk92ZXJ3cml0ZSA9IHRydWUpID0+IHtcbiAgICAgIHNldFRvb2xSZWdpc3RyeSgocHJldikgPT4ge1xuICAgICAgICBpZiAocHJldlt0b29sLm5hbWVdICYmIHdhcm5Pbk92ZXJ3cml0ZSkge1xuICAgICAgICAgIGNvbnNvbGUud2FybihgT3ZlcndyaXRpbmcgdG9vbCAke3Rvb2wubmFtZX1gKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgW3Rvb2wubmFtZV06IHRvb2wsXG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICB9LFxuICAgIFtdLFxuICApO1xuXG4gIGNvbnN0IHJlZ2lzdGVyVG9vbHMgPSB1c2VDYWxsYmFjayhcbiAgICAodG9vbHM6IFRhbWJvVG9vbFtdLCB3YXJuT25PdmVyd3JpdGUgPSB0cnVlKSA9PiB7XG4gICAgICB0b29scy5mb3JFYWNoKCh0b29sKSA9PiByZWdpc3RlclRvb2wodG9vbCwgd2Fybk9uT3ZlcndyaXRlKSk7XG4gICAgfSxcbiAgICBbcmVnaXN0ZXJUb29sXSxcbiAgKTtcblxuICBjb25zdCBhZGRUb29sQXNzb2NpYXRpb24gPSB1c2VDYWxsYmFjayhcbiAgICAoY29tcG9uZW50TmFtZTogc3RyaW5nLCB0b29sOiBUYW1ib1Rvb2wpID0+IHtcbiAgICAgIGlmICghY29tcG9uZW50TGlzdFtjb21wb25lbnROYW1lXSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYENvbXBvbmVudCAke2NvbXBvbmVudE5hbWV9IG5vdCBmb3VuZCBpbiByZWdpc3RyeWApO1xuICAgICAgfVxuICAgICAgc2V0Q29tcG9uZW50VG9vbEFzc29jaWF0aW9ucygocHJldikgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgW2NvbXBvbmVudE5hbWVdOiBbLi4uKHByZXZbY29tcG9uZW50TmFtZV0gfHwgW10pLCB0b29sLm5hbWVdLFxuICAgICAgfSkpO1xuICAgIH0sXG4gICAgW2NvbXBvbmVudExpc3RdLFxuICApO1xuXG4gIGNvbnN0IHJlZ2lzdGVyQ29tcG9uZW50ID0gdXNlQ2FsbGJhY2soXG4gICAgKG9wdGlvbnM6IFRhbWJvQ29tcG9uZW50LCB3YXJuT25PdmVyd3JpdGUgPSB0cnVlKSA9PiB7XG4gICAgICBjb25zdCB7XG4gICAgICAgIG5hbWUsXG4gICAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgICBjb21wb25lbnQsXG4gICAgICAgIHByb3BzU2NoZW1hLFxuICAgICAgICBwcm9wc0RlZmluaXRpb24sXG4gICAgICAgIGxvYWRpbmdDb21wb25lbnQsXG4gICAgICAgIGFzc29jaWF0ZWRUb29scyxcbiAgICAgIH0gPSBvcHRpb25zO1xuXG4gICAgICAvLyBWYWxpZGF0ZSB0aGF0IGF0IGxlYXN0IG9uZSBwcm9wcyBkZWZpbml0aW9uIGlzIHByb3ZpZGVkXG4gICAgICBpZiAoIXByb3BzU2NoZW1hICYmICFwcm9wc0RlZmluaXRpb24pIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgIGBDb21wb25lbnQgJHtuYW1lfSBtdXN0IGhhdmUgZWl0aGVyIHByb3BzU2NoZW1hIChyZWNvbW1lbmRlZCkgb3IgcHJvcHNEZWZpbml0aW9uIGRlZmluZWRgLFxuICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICAvLyBWYWxpZGF0ZSB0aGF0IG9ubHkgb25lIHByb3BzIGRlZmluaXRpb24gaXMgcHJvdmlkZWRcbiAgICAgIGlmIChwcm9wc1NjaGVtYSAmJiBwcm9wc0RlZmluaXRpb24pIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgIGBDb21wb25lbnQgJHtuYW1lfSBjYW5ub3QgaGF2ZSBib3RoIHByb3BzU2NoZW1hIGFuZCBwcm9wc0RlZmluaXRpb24gZGVmaW5lZC4gVXNlIG9ubHkgb25lLiBXZSByZWNvbW1lbmQgdXNpbmcgcHJvcHNTY2hlbWEuYCxcbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ29udmVydCBwcm9wc1NjaGVtYSB0byBKU09OIFNjaGVtYSBpZiBpdCBleGlzdHNcbiAgICAgIGNvbnN0IHByb3BzID0gZ2V0U2VyaWFsaXplZFByb3BzKHByb3BzRGVmaW5pdGlvbiwgcHJvcHNTY2hlbWEsIG5hbWUpO1xuXG4gICAgICBzZXRDb21wb25lbnRMaXN0KChwcmV2KSA9PiB7XG4gICAgICAgIGlmIChwcmV2W25hbWVdICYmIHdhcm5Pbk92ZXJ3cml0ZSkge1xuICAgICAgICAgIGNvbnNvbGUud2Fybihgb3ZlcndyaXRpbmcgY29tcG9uZW50ICR7bmFtZX1gKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgW25hbWVdOiB7XG4gICAgICAgICAgICBjb21wb25lbnQsXG4gICAgICAgICAgICBsb2FkaW5nQ29tcG9uZW50LFxuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgcHJvcHMsXG4gICAgICAgICAgICBjb250ZXh0VG9vbHM6IFtdLFxuICAgICAgICAgIH0sXG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICAgIGlmIChhc3NvY2lhdGVkVG9vbHMpIHtcbiAgICAgICAgcmVnaXN0ZXJUb29scyhhc3NvY2lhdGVkVG9vbHMpO1xuICAgICAgICBzZXRDb21wb25lbnRUb29sQXNzb2NpYXRpb25zKChwcmV2KSA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgW25hbWVdOiBhc3NvY2lhdGVkVG9vbHMubWFwKCh0b29sKSA9PiB0b29sLm5hbWUpLFxuICAgICAgICB9KSk7XG4gICAgICB9XG4gICAgfSxcbiAgICBbcmVnaXN0ZXJUb29sc10sXG4gICk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHVzZXJDb21wb25lbnRzKSB7XG4gICAgICB1c2VyQ29tcG9uZW50cy5mb3JFYWNoKChjb21wb25lbnQpID0+IHtcbiAgICAgICAgcmVnaXN0ZXJDb21wb25lbnQoY29tcG9uZW50LCBmYWxzZSk7XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtyZWdpc3RlckNvbXBvbmVudCwgdXNlckNvbXBvbmVudHNdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyVG9vbHMpIHtcbiAgICAgIHJlZ2lzdGVyVG9vbHModXNlclRvb2xzLCBmYWxzZSk7XG4gICAgfVxuICB9LCBbcmVnaXN0ZXJUb29scywgdXNlclRvb2xzXSk7XG5cbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgY29tcG9uZW50TGlzdCxcbiAgICB0b29sUmVnaXN0cnksXG4gICAgY29tcG9uZW50VG9vbEFzc29jaWF0aW9ucyxcbiAgICByZWdpc3RlckNvbXBvbmVudCxcbiAgICByZWdpc3RlclRvb2wsXG4gICAgcmVnaXN0ZXJUb29scyxcbiAgICBhZGRUb29sQXNzb2NpYXRpb24sXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8VGFtYm9SZWdpc3RyeUNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1RhbWJvUmVnaXN0cnlDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufTtcblxuLyoqXG4gKiBUaGUgdXNlVGFtYm9SZWdpc3RyeSBob29rIHByb3ZpZGVzIGFjY2VzcyB0byB0aGUgY29tcG9uZW50IHJlZ2lzdHJ5XG4gKiB0byB0aGUgZGVzY2VuZGFudHMgb2YgdGhlIFRhbWJvUmVnaXN0cnlQcm92aWRlci5cbiAqIEByZXR1cm5zIFRoZSBjb21wb25lbnQgcmVnaXN0cnlcbiAqL1xuZXhwb3J0IGNvbnN0IHVzZVRhbWJvUmVnaXN0cnkgPSAoKSA9PiB7XG4gIHJldHVybiB1c2VDb250ZXh0KFRhbWJvUmVnaXN0cnlDb250ZXh0KTtcbn07XG5mdW5jdGlvbiBnZXRTZXJpYWxpemVkUHJvcHMoXG4gIHByb3BzRGVmaW5pdGlvbjogYW55LFxuICBwcm9wc1NjaGVtYTogYW55LFxuICBuYW1lOiBzdHJpbmcsXG4pIHtcbiAgaWYgKHByb3BzRGVmaW5pdGlvbikge1xuICAgIGNvbnNvbGUud2FybihgcHJvcHNEZWZpbml0aW9uIGlzIGRlcHJlY2F0ZWQuIFVzZSBwcm9wc1NjaGVtYSBpbnN0ZWFkLmApO1xuICAgIHJldHVybiBwcm9wc0RlZmluaXRpb247XG4gIH1cblxuICBpZiAoaXNab2RTY2hlbWEocHJvcHNTY2hlbWEpKSB7XG4gICAgdHJ5IHtcbiAgICAgIHJldHVybiB6b2RUb0pzb25TY2hlbWEocHJvcHNTY2hlbWEpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICBgRXJyb3IgY29udmVydGluZyAke25hbWV9IHByb3BzIHNjaGVtYSB0byBKU09OIFNjaGVtYTpgLFxuICAgICAgICBlcnJvcixcbiAgICAgICk7XG4gICAgfVxuICB9XG4gIC8vIHRyeSB0byByb3VnaGx5IGRldGVjdCBKU09OU2NoZW1hLCBzaG91bGQgYWx3YXlzIGJlIGFuIG9iamVjdCB3aXRoIGEgcHJvcGVydGllcyBrZXlcbiAgaWYgKGlzSlNPTlNjaGVtYShwcm9wc1NjaGVtYSkpIHtcbiAgICByZXR1cm4gcHJvcHNTY2hlbWE7XG4gIH1cblxuICB0aHJvdyBuZXcgRXJyb3IoYEludmFsaWQgcHJvcHMgc2NoZW1hIGZvciAke25hbWV9YCk7XG59XG5cbi8qKlxuICogQ2hlY2tzIGlmIHRoZSBwcm9wc1NjaGVtYSBpcyBhIEpTT04gU2NoZW1hLiBUaGlzIGlzIGEgcm91Z2ggY2hlY2ssIGFuZCB0aGVcbiAqIHNlcnZlciB3aWxsIHByb3ZpZGUgdGhlIGRlZmluaXRpdmUgY2hlY2suXG4gKiBAcGFyYW0gcHJvcHNTY2hlbWEgLSBUaGUgcHJvcHMgc2NoZW1hIHRvIGNoZWNrXG4gKiBAcmV0dXJucyBUcnVlIGlmIHRoZSBwcm9wcyBzY2hlbWEgaXMgYSBKU09OIFNjaGVtYSwgZmFsc2Ugb3RoZXJ3aXNlXG4gKi9cbmZ1bmN0aW9uIGlzSlNPTlNjaGVtYShwcm9wc1NjaGVtYTogYW55KSB7XG4gIHJldHVybiAoXG4gICAgcHJvcHNTY2hlbWEgJiZcbiAgICB0eXBlb2YgcHJvcHNTY2hlbWEgPT09IFwib2JqZWN0XCIgJiZcbiAgICBwcm9wc1NjaGVtYS50eXBlID09PSBcIm9iamVjdFwiICYmXG4gICAgcHJvcHNTY2hlbWEucHJvcGVydGllc1xuICApO1xufVxuXG4vKipcbiAqIFNpbmNlIHdlIHJlcXVpcmUgYSBjZXJ0YWluIHpvZCB2ZXJzaW9uLCB3ZSBuZWVkIHRvIGNoZWNrIGlmIHRoZSBvYmplY3QgaXMgYSBab2RTY2hlbWFcbiAqIEBwYXJhbSBvYmogLSBUaGUgb2JqZWN0IHRvIGNoZWNrXG4gKiBAcmV0dXJucyBUcnVlIGlmIHRoZSBvYmplY3QgaXMgYSBab2RTY2hlbWEsIGZhbHNlIG90aGVyd2lzZVxuICovXG5mdW5jdGlvbiBpc1pvZFNjaGVtYShvYmo6IHVua25vd24pOiBvYmogaXMgWm9kU2NoZW1hIHtcbiAgaWYgKG9iaiBpbnN0YW5jZW9mIFpvZFNjaGVtYSkge1xuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIC8vIHRyeSB0byBkZXRlY3QgaWYgdGhlIG9iamVjdCBpcyBhIFpvZFNjaGVtYVxuICByZXR1cm4gKFxuICAgIHR5cGVvZiBvYmogPT09IFwib2JqZWN0XCIgJiZcbiAgICBvYmogIT09IG51bGwgJiZcbiAgICB0eXBlb2YgKG9iaiBhcyBhbnkpLnNhZmVQYXJzZSA9PT0gXCJmdW5jdGlvblwiICYmXG4gICAgdHlwZW9mIChvYmogYXMgYW55KS5fZGVmID09PSBcIm9iamVjdFwiXG4gICk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-registry-provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-thread-provider.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/providers/tambo-thread-provider.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLACEHOLDER_THREAD: () => (/* binding */ PLACEHOLDER_THREAD),\n/* harmony export */   TamboThreadContext: () => (/* binding */ TamboThreadContext),\n/* harmony export */   TamboThreadProvider: () => (/* binding */ TamboThreadProvider),\n/* harmony export */   useTamboThread: () => (/* binding */ useTamboThread)\n/* harmony export */ });\n/* harmony import */ var _tambo_ai_typescript_sdk__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tambo-ai/typescript-sdk */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/lib/advance-stream.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../model/generate-component-response */ \"(ssr)/./node_modules/@tambo-ai/react/esm/model/generate-component-response.js\");\n/* harmony import */ var _util_generate_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../util/generate-component */ \"(ssr)/./node_modules/@tambo-ai/react/esm/util/generate-component.js\");\n/* harmony import */ var _util_registry__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/registry */ \"(ssr)/./node_modules/@tambo-ai/react/esm/util/registry.js\");\n/* harmony import */ var _util_tool_caller__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/tool-caller */ \"(ssr)/./node_modules/@tambo-ai/react/esm/util/tool-caller.js\");\n/* harmony import */ var _tambo_client_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tambo-client-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-client-provider.js\");\n/* harmony import */ var _tambo_registry_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tambo-registry-provider */ \"(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-registry-provider.js\");\n/* __next_internal_client_entry_do_not_use__ PLACEHOLDER_THREAD,TamboThreadContext,TamboThreadProvider,useTamboThread auto */ \n\n\n\n\n\n\n\n/**\n * This is a stub entry for when the thread is not yet created, the first time\n * the user sends a message\n *\n * Note that the consumer needs to be careful never to send `PLACEHOLDER_THREAD.id` to the server,\n * as this doesn't really exist on the server side.\n */ const PLACEHOLDER_THREAD = {\n    id: \"placeholder\",\n    messages: [],\n    createdAt: \"\",\n    projectId: \"\",\n    updatedAt: \"\",\n    metadata: {}\n};\nconst TamboThreadContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    thread: PLACEHOLDER_THREAD,\n    /**\n     *\n     */ switchCurrentThread: ()=>{\n        throw new Error(\"switchCurrentThread not implemented\");\n    },\n    /**\n     *\n     */ startNewThread: ()=>{\n        throw new Error(\"startNewThread not implemented\");\n    },\n    /**\n     *\n     */ addThreadMessage: ()=>{\n        throw new Error(\"updateThreadMessageHistory not implemented\");\n    },\n    inputValue: \"\",\n    /**\n     *\n     */ setInputValue: ()=>{\n        throw new Error(\"setInputValue not implemented\");\n    },\n    /**\n     *\n     */ updateThreadMessage: ()=>{\n        throw new Error(\"updateThreadMessage not implemented\");\n    },\n    /**\n     *\n     */ sendThreadMessage: ()=>{\n        throw new Error(\"advance not implemented\");\n    },\n    generationStage: _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.IDLE,\n    generationStatusMessage: \"\",\n    isIdle: true\n});\n/**\n * The TamboThreadProvider is a React provider that provides a thread context\n * to the descendants of the provider.\n * @param props - The props for the TamboThreadProvider\n * @param props.children - The children to wrap\n * @returns The TamboThreadProvider component\n */ const TamboThreadProvider = ({ children })=>{\n    const [threadMap, setThreadMap] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        [PLACEHOLDER_THREAD.id]: PLACEHOLDER_THREAD\n    });\n    const client = (0,_tambo_client_provider__WEBPACK_IMPORTED_MODULE_2__.useTamboClient)();\n    const { componentList, toolRegistry, componentToolAssociations } = (0,_tambo_registry_provider__WEBPACK_IMPORTED_MODULE_3__.useTamboRegistry)();\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [currentThreadId, setCurrentThreadId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(PLACEHOLDER_THREAD.id);\n    const currentThread = threadMap[currentThreadId];\n    // Use existing messages from the current thread to avoid re-generating any components\n    const currentMessageCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"TamboThreadProvider.useMemo[currentMessageCache]\": ()=>{\n            const messageCache = new Map();\n            if (currentThread) {\n                for (const message of currentThread.messages){\n                    messageCache.set(message.id, message);\n                }\n            }\n            return messageCache;\n        }\n    }[\"TamboThreadProvider.useMemo[currentMessageCache]\"], [\n        currentThread\n    ]);\n    const fetchThread = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboThreadProvider.useCallback[fetchThread]\": async (threadId)=>{\n            const thread = await client.beta.threads.retrieve(threadId);\n            const threadWithRenderedComponents = {\n                ...thread,\n                messages: thread.messages.map({\n                    \"TamboThreadProvider.useCallback[fetchThread]\": (message)=>{\n                        if (currentMessageCache.has(message.id)) {\n                            const renderedMessage = currentMessageCache.get(message.id);\n                            return {\n                                ...renderedMessage,\n                                ...message\n                            };\n                        }\n                        if (message.component?.componentName) {\n                            const messageWithComponent = (0,_util_generate_component__WEBPACK_IMPORTED_MODULE_4__.renderComponentIntoMessage)(message, componentList);\n                            return messageWithComponent;\n                        }\n                        return message;\n                    }\n                }[\"TamboThreadProvider.useCallback[fetchThread]\"])\n            };\n            setThreadMap({\n                \"TamboThreadProvider.useCallback[fetchThread]\": (prevMap)=>{\n                    const updatedThreadMap = {\n                        ...prevMap,\n                        [threadId]: threadWithRenderedComponents\n                    };\n                    return updatedThreadMap;\n                }\n            }[\"TamboThreadProvider.useCallback[fetchThread]\"]);\n        }\n    }[\"TamboThreadProvider.useCallback[fetchThread]\"], [\n        client.beta.threads,\n        componentList,\n        currentMessageCache\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"TamboThreadProvider.useEffect\": ()=>{\n            if (currentThreadId && currentThreadId !== PLACEHOLDER_THREAD.id && !threadMap[currentThreadId]) {\n                fetchThread(currentThreadId);\n            }\n        }\n    }[\"TamboThreadProvider.useEffect\"], [\n        currentThreadId,\n        fetchThread,\n        threadMap\n    ]);\n    const addThreadMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboThreadProvider.useCallback[addThreadMessage]\": async (message, sendToServer = true, createdAt = new Date().toISOString())=>{\n            if (!currentThread) {\n                console.warn(\"Cannot add messages if we do not have a current thread\");\n                return [];\n            }\n            const chatMessage = {\n                ...message,\n                additionalContext: message.role === \"user\" ? (0,_util_registry__WEBPACK_IMPORTED_MODULE_5__.getClientContext)() : undefined,\n                createdAt\n            };\n            const threadId = message.threadId;\n            const messageId = chatMessage.id;\n            // optimistically update the thread in the local state\n            setThreadMap({\n                \"TamboThreadProvider.useCallback[addThreadMessage]\": (prevMap)=>{\n                    if (!threadId) {\n                        return prevMap;\n                    }\n                    const prevMessages = prevMap[threadId]?.messages || [];\n                    const haveMessage = prevMessages.find({\n                        \"TamboThreadProvider.useCallback[addThreadMessage].haveMessage\": (msg)=>msg.id === messageId\n                    }[\"TamboThreadProvider.useCallback[addThreadMessage].haveMessage\"]);\n                    // Update in place if the message already exists\n                    const updatedMessages = haveMessage ? prevMessages.map({\n                        \"TamboThreadProvider.useCallback[addThreadMessage]\": (msg)=>{\n                            if (msg.id === messageId) {\n                                return chatMessage;\n                            }\n                            return msg;\n                        }\n                    }[\"TamboThreadProvider.useCallback[addThreadMessage]\"]) : [\n                        ...prevMessages,\n                        chatMessage\n                    ];\n                    const updatedThreadMap = {\n                        ...prevMap,\n                        [threadId]: {\n                            ...prevMap[threadId],\n                            messages: updatedMessages\n                        }\n                    };\n                    return updatedThreadMap;\n                }\n            }[\"TamboThreadProvider.useCallback[addThreadMessage]\"]);\n            if (sendToServer) {\n                // TODO: if this fails, we need to revert the local state update\n                await client.beta.threads.messages.create(message.threadId, {\n                    content: message.content,\n                    role: message.role\n                });\n            }\n            return threadMap[threadId]?.messages || [];\n        }\n    }[\"TamboThreadProvider.useCallback[addThreadMessage]\"], [\n        client.beta.threads.messages,\n        currentThread,\n        threadMap\n    ]);\n    const updateThreadMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboThreadProvider.useCallback[updateThreadMessage]\": async (id, message, sendToServer = true, createdAt = new Date().toISOString())=>{\n            const chatMessage = {\n                ...message,\n                createdAt\n            };\n            setThreadMap({\n                \"TamboThreadProvider.useCallback[updateThreadMessage]\": (prevMap)=>{\n                    if (!message.threadId) {\n                        return prevMap;\n                    }\n                    const prevMessages = prevMap[message.threadId]?.messages || [];\n                    const updatedMessages = prevMessages.map({\n                        \"TamboThreadProvider.useCallback[updateThreadMessage].updatedMessages\": (msg)=>{\n                            if (msg.id === id) {\n                                return chatMessage;\n                            }\n                            return msg;\n                        }\n                    }[\"TamboThreadProvider.useCallback[updateThreadMessage].updatedMessages\"]);\n                    return {\n                        ...prevMap,\n                        [message.threadId]: {\n                            ...prevMap[message.threadId],\n                            messages: updatedMessages\n                        }\n                    };\n                }\n            }[\"TamboThreadProvider.useCallback[updateThreadMessage]\"]);\n            if (sendToServer) {\n                // TODO: if this fails, we need to revert the local state update\n                await client.beta.threads.messages.create(message.threadId, {\n                    content: message.content,\n                    role: message.role\n                });\n            }\n        }\n    }[\"TamboThreadProvider.useCallback[updateThreadMessage]\"], [\n        client.beta.threads.messages\n    ]);\n    const startNewThread = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboThreadProvider.useCallback[startNewThread]\": ()=>{\n            setCurrentThreadId(PLACEHOLDER_THREAD.id);\n            setThreadMap({\n                \"TamboThreadProvider.useCallback[startNewThread]\": (prevMap)=>{\n                    return {\n                        ...prevMap,\n                        [PLACEHOLDER_THREAD.id]: PLACEHOLDER_THREAD\n                    };\n                }\n            }[\"TamboThreadProvider.useCallback[startNewThread]\"]);\n        }\n    }[\"TamboThreadProvider.useCallback[startNewThread]\"], []);\n    const switchCurrentThread = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboThreadProvider.useCallback[switchCurrentThread]\": async (threadId, fetch = true)=>{\n            if (threadId === PLACEHOLDER_THREAD.id) {\n                console.warn(\"Switching to placeholder thread, may be a bug.\");\n                return;\n            }\n            setCurrentThreadId(threadId);\n            setThreadMap({\n                \"TamboThreadProvider.useCallback[switchCurrentThread]\": (prevMap)=>{\n                    if (prevMap[threadId]) {\n                        return prevMap;\n                    }\n                    // If this is a new thread, add placeholder thread messages to the thread\n                    const updatedThreadMap = {\n                        ...prevMap,\n                        [threadId]: {\n                            ...prevMap[PLACEHOLDER_THREAD.id],\n                            id: threadId\n                        }\n                    };\n                    return updatedThreadMap;\n                }\n            }[\"TamboThreadProvider.useCallback[switchCurrentThread]\"]);\n            if (fetch) {\n                await fetchThread(threadId);\n            }\n        }\n    }[\"TamboThreadProvider.useCallback[switchCurrentThread]\"], [\n        fetchThread\n    ]);\n    const updateThreadStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboThreadProvider.useCallback[updateThreadStatus]\": (threadId, stage, statusMessage)=>{\n            setThreadMap({\n                \"TamboThreadProvider.useCallback[updateThreadStatus]\": (prevMap)=>{\n                    const updatedThreadMap = {\n                        ...prevMap,\n                        [threadId]: {\n                            ...prevMap[threadId],\n                            generationStage: stage,\n                            statusMessage: statusMessage\n                        }\n                    };\n                    return updatedThreadMap;\n                }\n            }[\"TamboThreadProvider.useCallback[updateThreadStatus]\"]);\n        }\n    }[\"TamboThreadProvider.useCallback[updateThreadStatus]\"], []);\n    const handleAdvanceStream = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboThreadProvider.useCallback[handleAdvanceStream]\": async (stream, params, threadId)=>{\n            let finalMessage;\n            let hasSetThreadId = false;\n            updateThreadStatus(threadId, _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.STREAMING_RESPONSE);\n            for await (const chunk of stream){\n                if (chunk.responseMessageDto.toolCallRequest) {\n                    updateThreadStatus(chunk.responseMessageDto.threadId, _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.FETCHING_CONTEXT);\n                    const toolCallResponse = await (0,_util_tool_caller__WEBPACK_IMPORTED_MODULE_6__.handleToolCall)(chunk.responseMessageDto, toolRegistry);\n                    const toolCallResponseString = typeof toolCallResponse.result === \"string\" ? toolCallResponse.result : JSON.stringify(toolCallResponse.result);\n                    const toolCallResponseParams = {\n                        ...params,\n                        messageToAppend: {\n                            content: [\n                                {\n                                    type: \"text\",\n                                    text: toolCallResponseString\n                                }\n                            ],\n                            role: \"tool\",\n                            actionType: \"tool_response\",\n                            component: chunk.responseMessageDto.component,\n                            tool_call_id: chunk.responseMessageDto.tool_call_id,\n                            error: toolCallResponse.error\n                        }\n                    };\n                    updateThreadMessage(chunk.responseMessageDto.id, {\n                        ...chunk.responseMessageDto,\n                        error: toolCallResponse.error\n                    }, false);\n                    updateThreadStatus(chunk.responseMessageDto.threadId, _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.STREAMING_RESPONSE);\n                    const toolCallResponseStream = await (0,_tambo_ai_typescript_sdk__WEBPACK_IMPORTED_MODULE_7__.advanceStream)(client, toolCallResponseParams, chunk.responseMessageDto.threadId);\n                    return await handleAdvanceStream(toolCallResponseStream, toolCallResponseParams, chunk.responseMessageDto.threadId);\n                } else {\n                    if (!hasSetThreadId && chunk.responseMessageDto.threadId && chunk.responseMessageDto.threadId !== currentThread?.id) {\n                        hasSetThreadId = true;\n                        await switchCurrentThread(chunk.responseMessageDto.threadId, false);\n                    }\n                    if (!finalMessage) {\n                        finalMessage = chunk.responseMessageDto.component?.componentName ? (0,_util_generate_component__WEBPACK_IMPORTED_MODULE_4__.renderComponentIntoMessage)(chunk.responseMessageDto, componentList) : chunk.responseMessageDto;\n                        await addThreadMessage(finalMessage, false);\n                    } else {\n                        // if we start getting a new message mid-stream, put the previous one on screen\n                        const isNewMessage = chunk.responseMessageDto.id !== finalMessage.id;\n                        finalMessage = chunk.responseMessageDto.component?.componentName ? (0,_util_generate_component__WEBPACK_IMPORTED_MODULE_4__.renderComponentIntoMessage)(chunk.responseMessageDto, componentList) : chunk.responseMessageDto;\n                        if (isNewMessage) {\n                            await addThreadMessage(finalMessage, false);\n                        } else {\n                            await updateThreadMessage(finalMessage.id, finalMessage, false);\n                        }\n                    }\n                }\n            }\n            updateThreadStatus(finalMessage?.threadId ?? threadId, _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.COMPLETE);\n            return finalMessage ?? {\n                threadId: \"\",\n                content: [\n                    {\n                        type: \"text\",\n                        text: `Error processing stream`\n                    }\n                ],\n                role: \"assistant\",\n                createdAt: new Date().toISOString(),\n                id: crypto.randomUUID(),\n                componentState: {}\n            };\n        }\n    }[\"TamboThreadProvider.useCallback[handleAdvanceStream]\"], [\n        addThreadMessage,\n        client,\n        componentList,\n        currentThread?.id,\n        switchCurrentThread,\n        toolRegistry,\n        updateThreadMessage,\n        updateThreadStatus\n    ]);\n    const sendThreadMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"TamboThreadProvider.useCallback[sendThreadMessage]\": async (message, options = {\n            threadId: PLACEHOLDER_THREAD.id\n        })=>{\n            const { threadId = currentThread.id, streamResponse, forceToolChoice } = options;\n            updateThreadStatus(threadId, _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.CHOOSING_COMPONENT);\n            addThreadMessage({\n                content: [\n                    {\n                        type: \"text\",\n                        text: message\n                    }\n                ],\n                renderedComponent: null,\n                role: \"user\",\n                threadId: threadId,\n                id: crypto.randomUUID(),\n                createdAt: new Date().toISOString(),\n                componentState: {}\n            }, false);\n            const availableComponents = (0,_util_registry__WEBPACK_IMPORTED_MODULE_5__.getAvailableComponents)(componentList, toolRegistry, componentToolAssociations);\n            const unassociatedTools = (0,_util_registry__WEBPACK_IMPORTED_MODULE_5__.getUnassociatedTools)(toolRegistry, componentToolAssociations);\n            const params = {\n                messageToAppend: {\n                    content: [\n                        {\n                            type: \"text\",\n                            text: message\n                        }\n                    ],\n                    role: \"user\"\n                },\n                contextKey: options.contextKey,\n                availableComponents: availableComponents,\n                clientTools: unassociatedTools.map({\n                    \"TamboThreadProvider.useCallback[sendThreadMessage]\": (tool)=>(0,_util_registry__WEBPACK_IMPORTED_MODULE_5__.mapTamboToolToContextTool)(tool)\n                }[\"TamboThreadProvider.useCallback[sendThreadMessage]\"]),\n                forceToolChoice: forceToolChoice\n            };\n            if (streamResponse) {\n                const advanceStreamResponse = await (0,_tambo_ai_typescript_sdk__WEBPACK_IMPORTED_MODULE_7__.advanceStream)(client, params, threadId === PLACEHOLDER_THREAD.id ? undefined : threadId);\n                return await handleAdvanceStream(advanceStreamResponse, params, threadId);\n            }\n            let advanceResponse = await (threadId === PLACEHOLDER_THREAD.id ? client.beta.threads.advance(params) : client.beta.threads.advanceById(threadId, params));\n            //handle tool calls\n            while(advanceResponse.responseMessageDto.toolCallRequest){\n                updateThreadStatus(threadId, _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.FETCHING_CONTEXT);\n                const toolCallResponse = await (0,_util_tool_caller__WEBPACK_IMPORTED_MODULE_6__.handleToolCall)(advanceResponse.responseMessageDto, toolRegistry);\n                const toolResponseString = typeof toolCallResponse.result === \"string\" ? toolCallResponse.result : JSON.stringify(toolCallResponse.result);\n                const toolCallResponseParams = {\n                    ...params,\n                    messageToAppend: {\n                        ...params.messageToAppend,\n                        content: [\n                            {\n                                type: \"text\",\n                                text: toolResponseString\n                            }\n                        ],\n                        role: \"tool\",\n                        actionType: \"tool_response\",\n                        component: advanceResponse.responseMessageDto.component,\n                        tool_call_id: advanceResponse.responseMessageDto.tool_call_id,\n                        error: toolCallResponse.error\n                    }\n                };\n                if (toolCallResponse.error) {\n                    //update toolcall message with error\n                    const toolCallMessage = {\n                        ...advanceResponse.responseMessageDto,\n                        error: toolCallResponse.error\n                    };\n                    updateThreadMessage(toolCallMessage.id, toolCallMessage, false);\n                }\n                updateThreadStatus(threadId, _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.HYDRATING_COMPONENT);\n                advanceResponse = await client.beta.threads.advanceById(advanceResponse.responseMessageDto.threadId, toolCallResponseParams);\n            }\n            const finalMessage = advanceResponse.responseMessageDto.component?.componentName ? (0,_util_generate_component__WEBPACK_IMPORTED_MODULE_4__.renderComponentIntoMessage)(advanceResponse.responseMessageDto, componentList) : advanceResponse.responseMessageDto;\n            await switchCurrentThread(advanceResponse.responseMessageDto.threadId);\n            updateThreadStatus(advanceResponse.responseMessageDto.threadId, _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.COMPLETE);\n            return finalMessage;\n        }\n    }[\"TamboThreadProvider.useCallback[sendThreadMessage]\"], [\n        componentList,\n        toolRegistry,\n        componentToolAssociations,\n        currentThread.id,\n        switchCurrentThread,\n        addThreadMessage,\n        client,\n        updateThreadStatus,\n        handleAdvanceStream\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(TamboThreadContext.Provider, {\n        value: {\n            thread: currentThread,\n            switchCurrentThread,\n            startNewThread,\n            addThreadMessage,\n            updateThreadMessage,\n            inputValue,\n            setInputValue,\n            sendThreadMessage,\n            generationStage: currentThread?.generationStage ?? _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.IDLE,\n            generationStatusMessage: currentThread?.statusMessage ?? \"\",\n            isIdle: (0,_model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.isIdleStage)(currentThread?.generationStage ?? _model_generate_component_response__WEBPACK_IMPORTED_MODULE_1__.GenerationStage.IDLE)\n        }\n    }, children);\n};\n/**\n * The useTamboThread hook provides access to the current thread context\n * to the descendants of the TamboThreadProvider.\n * @returns All state and actions for the current thread\n */ const useTamboThread = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TamboThreadContext);\n    if (context === undefined) {\n        throw new Error(\"useTamboThread must be used within a TamboThreadProvider\");\n    }\n    return context;\n}; //# sourceMappingURL=tambo-thread-provider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/providers/tambo-thread-provider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/util/generate-component.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/util/generate-component.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   renderComponentIntoMessage: () => (/* binding */ renderComponentIntoMessage)\n/* harmony export */ });\n/* harmony import */ var partial_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! partial-json */ \"(ssr)/./node_modules/partial-json/dist/index.js\");\n/* harmony import */ var partial_json__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(partial_json__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _hooks_use_current_message__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/use-current-message */ \"(ssr)/./node_modules/@tambo-ai/react/esm/hooks/use-current-message.js\");\n/* harmony import */ var _util_registry__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/registry */ \"(ssr)/./node_modules/@tambo-ai/react/esm/util/registry.js\");\n\n\n\n\n\n/**\n * Generate a message that has a component rendered into it, if the message\n * came with one.\n * @param message - The message that may contain a component\n * @param componentList - the list of available components\n * @returns The updated message with the component rendered into it\n */\nfunction renderComponentIntoMessage(message, componentList) {\n    if (!message.component?.componentName) {\n        throw new Error(\"Component not found\");\n    }\n    const parsedProps = (0,partial_json__WEBPACK_IMPORTED_MODULE_0__.parse)(JSON.stringify(message.component.props));\n    const registeredComponent = (0,_util_registry__WEBPACK_IMPORTED_MODULE_3__.getComponentFromRegistry)(message.component.componentName, componentList);\n    const validatedProps = registeredComponent.props instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodType\n        ? registeredComponent.props.parse(parsedProps)\n        : parsedProps;\n    const renderedComponent = react__WEBPACK_IMPORTED_MODULE_1___default().createElement(registeredComponent.component, validatedProps);\n    const wrappedComponent = (0,_hooks_use_current_message__WEBPACK_IMPORTED_MODULE_4__.wrapWithTamboMessageProvider)(renderedComponent, message.threadId, message.id);\n    return {\n        ...message,\n        component: {\n            ...message.component,\n            props: validatedProps,\n        },\n        renderedComponent: wrappedComponent,\n    };\n}\n//# sourceMappingURL=generate-component.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/util/generate-component.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/util/query-utils.js":
/*!**************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/util/query-utils.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combineMutationResults: () => (/* binding */ combineMutationResults),\n/* harmony export */   combineQueryResults: () => (/* binding */ combineQueryResults)\n/* harmony export */ });\n/**\n * Combines two mutation results, showing the \"loading state\" of the two\n * mutations. For instance, if either mutation is pending, the combined\n * mutation result will be pending.\n * @param resultA - The first mutation result\n * @param resultB - The second mutation result\n * @returns The combined mutation result\n */\nfunction combineMutationResults(resultA, resultB) {\n    {\n        return {\n            isPending: resultA.isPending || resultB.isPending,\n            isSuccess: resultA.isSuccess && resultB.isSuccess,\n            isError: resultA.isError || resultB.isError,\n            isIdle: resultA.isIdle && resultB.isIdle,\n            isPaused: resultA.isPaused || resultB.isPaused,\n            submittedAt: resultA.submittedAt || resultB.submittedAt,\n            status: resultA.isPending || resultB.isPending\n                ? \"pending\"\n                : resultA.isError || resultB.isError\n                    ? \"error\"\n                    : resultA.isSuccess && resultB.isSuccess\n                        ? \"success\"\n                        : \"idle\",\n            error: resultA.error ?? resultB.error,\n            failureCount: resultA.failureCount + resultB.failureCount,\n            failureReason: resultA.failureReason ?? resultB.failureReason,\n        };\n    }\n}\n/**\n * Combines two query results, showing the \"loading state\" of the two queries.\n * For instance, if either query is loading, the combined query result will be\n * loading.\n * @param resultA - The first query result\n * @param resultB - The second query result\n * @returns The combined query result\n */\nfunction combineQueryResults(resultA, resultB) {\n    return {\n        isPending: resultA.isPending || resultB.isPending,\n        isSuccess: resultA.isSuccess && resultB.isSuccess,\n        isError: resultA.isError || resultB.isError,\n        isLoading: resultA.isLoading || resultB.isLoading,\n        isFetched: resultA.isFetched && resultB.isFetched,\n        isFetchedAfterMount: resultA.isFetchedAfterMount && resultB.isFetchedAfterMount,\n        isInitialLoading: resultA.isInitialLoading || resultB.isInitialLoading,\n        isPaused: resultA.isPaused || resultB.isPaused,\n        isLoadingError: resultA.isLoadingError || resultB.isLoadingError,\n        isRefetchError: resultA.isRefetchError || resultB.isRefetchError,\n        isPlaceholderData: resultA.isPlaceholderData || resultB.isPlaceholderData,\n        isStale: resultA.isStale || resultB.isStale,\n        isRefetching: resultA.isRefetching || resultB.isRefetching,\n        isFetching: resultA.isFetching || resultB.isFetching,\n        status: resultA.isPending || resultB.isPending\n            ? \"pending\"\n            : resultA.isError || resultB.isError\n                ? \"error\"\n                : resultA.isSuccess && resultB.isSuccess\n                    ? \"success\"\n                    : \"pending\",\n        error: resultA.error ?? resultB.error,\n        failureCount: resultA.failureCount + resultB.failureCount,\n        failureReason: resultA.failureReason ?? resultB.failureReason,\n        errorUpdateCount: resultA.errorUpdateCount + resultB.errorUpdateCount,\n        fetchStatus: resultA.isFetching || resultB.isFetching\n            ? \"fetching\"\n            : resultA.isPaused || resultB.isPaused\n                ? \"paused\"\n                : \"idle\",\n        dataUpdatedAt: Math.max(resultA.dataUpdatedAt, resultB.dataUpdatedAt),\n        errorUpdatedAt: Math.max(resultA.errorUpdatedAt, resultB.errorUpdatedAt),\n    };\n}\n//# sourceMappingURL=query-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/util/query-utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/util/registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/util/registry.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertPropsToJsonSchema: () => (/* binding */ convertPropsToJsonSchema),\n/* harmony export */   getAvailableComponents: () => (/* binding */ getAvailableComponents),\n/* harmony export */   getClientContext: () => (/* binding */ getClientContext),\n/* harmony export */   getComponentFromRegistry: () => (/* binding */ getComponentFromRegistry),\n/* harmony export */   getUnassociatedTools: () => (/* binding */ getUnassociatedTools),\n/* harmony export */   mapTamboToolToContextTool: () => (/* binding */ mapTamboToolToContextTool)\n/* harmony export */ });\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod-to-json-schema */ \"(ssr)/./node_modules/zod-to-json-schema/dist/esm/index.js\");\n\n/**\n * Get all the available components from the component registry\n * @param componentRegistry - The component registry\n * @param toolRegistry - The tool registry\n * @param toolAssociations - The tool associations\n * @returns The available components\n */\nconst getAvailableComponents = (componentRegistry, toolRegistry, toolAssociations) => {\n    const availableComponents = [];\n    for (const [name, componentEntry] of Object.entries(componentRegistry)) {\n        const associatedToolNames = toolAssociations[name] || [];\n        const contextTools = [\n            ...associatedToolNames.map((toolName) => {\n                const tool = toolRegistry[toolName];\n                if (!tool)\n                    return null;\n                return mapTamboToolToContextTool(tool);\n            }),\n        ].filter((tool) => tool !== null);\n        availableComponents.push({\n            name: componentEntry.name,\n            description: componentEntry.description,\n            props: componentEntry.props,\n            contextTools,\n        });\n    }\n    return availableComponents;\n};\n/**\n * Get tools from tool registry that are not associated with any component\n * @param toolRegistry - The tool registry\n * @param toolAssociations - The tool associations\n * @returns The tools that are not associated with any component\n */\nconst getUnassociatedTools = (toolRegistry, toolAssociations) => {\n    return Object.values(toolRegistry).filter((tool) => {\n        // Check if the tool's name appears in any of the tool association arrays\n        return !Object.values(toolAssociations).flat().includes(tool.name);\n    });\n};\n/**\n * Helper function to convert component props from Zod schema to JSON Schema\n * @param component - The component to convert\n * @returns The converted props\n */\nconst convertPropsToJsonSchema = (component) => {\n    if (!component.props) {\n        return component.props;\n    }\n    // Check if props is a Zod schema (we can't directly check the type, so we check for _def)\n    if (component.props._def && typeof component.props.parse === \"function\") {\n        // Use two-step type assertion for safety\n        return (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(component.props);\n    }\n    return component.props;\n};\n/**\n * Get a component by name from the component registry\n * @param componentName - The name of the component to get\n * @param componentRegistry - The component registry\n * @returns The component registration information\n */\nconst getComponentFromRegistry = (componentName, componentRegistry) => {\n    const componentEntry = componentRegistry[componentName];\n    if (!componentEntry) {\n        throw new Error(`Tambo tried to use Component ${componentName}, but it was not found.`);\n    }\n    return componentEntry;\n};\nconst getDefaultContextAdditions = () => {\n    const utcOffsetHours = new Date().getTimezoneOffset() / 60;\n    const utcOffset = `(UTC${utcOffsetHours > 0 ? \"+\" : \"\"}${utcOffsetHours})`;\n    return [\n        `The current time in user's timezone (${utcOffset}) is: ${new Date().toLocaleString()}`,\n    ];\n};\n/**\n * Get the client context for the current thread, such as the current time in the user's timezone\n * @returns a string of context additions that will be added to the prompt when the thread is advanced.\n */\nconst getClientContext = () => {\n    const contextAdditions = getDefaultContextAdditions();\n    return contextAdditions.join(\"\\n\");\n};\n/**\n * Map a Tambo tool to a context tool\n * @param tool - The tool to map\n * @returns The context tool\n */\nconst mapTamboToolToContextTool = (tool) => {\n    const parameters = getParametersFromZodFunction(tool.toolSchema);\n    return {\n        name: tool.name,\n        description: tool.description,\n        parameters,\n    };\n};\nfunction isJsonSchema(schema) {\n    return (typeof schema === \"object\" &&\n        schema !== null &&\n        \"type\" in schema &&\n        typeof schema.type === \"string\" &&\n        schema.type === \"object\");\n}\nconst getParametersFromZodFunction = (schema) => {\n    if (isJsonSchema(schema)) {\n        return [\n            {\n                name: \"args\",\n                type: \"object\",\n                description: schema.description ?? \"\",\n                isRequired: true,\n                schema: schema,\n            },\n        ];\n    }\n    const parameters = schema.parameters();\n    return parameters.items.map((param, index) => {\n        const name = `param${index + 1}`;\n        const type = getZodBaseType(param);\n        const description = param.description ?? \"\";\n        const isRequired = !param.isOptional();\n        const schema = (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(param);\n        return {\n            name,\n            type,\n            description,\n            isRequired,\n            schema,\n        };\n    });\n};\nconst getZodBaseType = (schema) => {\n    const typeName = schema._def.typeName;\n    switch (typeName) {\n        case \"ZodString\":\n            return \"string\";\n        case \"ZodNumber\":\n            return \"number\";\n        case \"ZodBoolean\":\n            return \"boolean\";\n        case \"ZodArray\":\n            return \"array\";\n        case \"ZodEnum\":\n            return \"enum\";\n        case \"ZodDate\":\n            return \"date\";\n        case \"ZodObject\":\n            return \"object\";\n        default:\n            console.warn(\"falling back to string for\", typeName);\n            return \"string\";\n    }\n};\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/util/registry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/react/esm/util/tool-caller.js":
/*!**************************************************************!*\
  !*** ./node_modules/@tambo-ai/react/esm/util/tool-caller.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleToolCall: () => (/* binding */ handleToolCall)\n/* harmony export */ });\n/* harmony import */ var _registry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry */ \"(ssr)/./node_modules/@tambo-ai/react/esm/util/registry.js\");\n\n/**\n * Process a message from the thread, invoking the appropriate tool and returning the result.\n * @param message - The message to handle\n * @param toolRegistry - The tool registry\n * @returns The result of the tool call\n */\nconst handleToolCall = async (message, toolRegistry) => {\n    if (!message?.toolCallRequest?.toolName) {\n        throw new Error(\"Tool name is required\");\n    }\n    try {\n        const tool = findTool(message.toolCallRequest.toolName, toolRegistry);\n        return {\n            result: await runToolChoice(message.toolCallRequest, tool),\n        };\n    }\n    catch (error) {\n        console.error(\"Error in calling tool: \", error);\n        return {\n            result: `When attempting to call tool ${message.toolCallRequest.toolName} the following error occurred: ${error}. Explain to the user that the tool call failed and try again if needed.`,\n            error: error instanceof Error ? error.message : \"Unknown error\",\n        };\n    }\n};\nconst findTool = (toolName, toolRegistry) => {\n    const registryTool = toolRegistry[toolName];\n    if (!registryTool) {\n        throw new Error(`Tool ${toolName} not found in registry`);\n    }\n    const contextTool = (0,_registry__WEBPACK_IMPORTED_MODULE_0__.mapTamboToolToContextTool)(registryTool);\n    return {\n        getComponentContext: registryTool.tool,\n        definition: contextTool,\n    };\n};\nconst runToolChoice = async (toolCallRequest, tool) => {\n    // Assumes parameters are in the order they are defined in the tool\n    const parameterValues = toolCallRequest.parameters?.map((param) => param.parameterValue) ?? [];\n    return await tool.getComponentContext(...parameterValues);\n};\n//# sourceMappingURL=tool-caller.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/react/esm/util/tool-caller.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/MultipartBody.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/_shims/MultipartBody.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultipartBody: () => (/* binding */ MultipartBody)\n/* harmony export */ });\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nclass MultipartBody {\n    constructor(body) {\n        this.body = body;\n    }\n    get [Symbol.toStringTag]() {\n        return 'MultipartBody';\n    }\n}\n//# sourceMappingURL=MultipartBody.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL19zaGltcy9NdWx0aXBhcnRCb2R5Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9AdGFtYm8tYWkvdHlwZXNjcmlwdC1zZGsvX3NoaW1zL011bHRpcGFydEJvZHkubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGlzY2xhaW1lcjogbW9kdWxlcyBpbiBfc2hpbXMgYXJlbid0IGludGVuZGVkIHRvIGJlIGltcG9ydGVkIGJ5IFNESyB1c2Vycy5cbiAqL1xuZXhwb3J0IGNsYXNzIE11bHRpcGFydEJvZHkge1xuICAgIGNvbnN0cnVjdG9yKGJvZHkpIHtcbiAgICAgICAgdGhpcy5ib2R5ID0gYm9keTtcbiAgICB9XG4gICAgZ2V0IFtTeW1ib2wudG9TdHJpbmdUYWddKCkge1xuICAgICAgICByZXR1cm4gJ011bHRpcGFydEJvZHknO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPU11bHRpcGFydEJvZHkubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/MultipartBody.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/_shims/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Blob),\n/* harmony export */   File: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.File),\n/* harmony export */   FormData: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.FormData),\n/* harmony export */   Headers: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Headers),\n/* harmony export */   ReadableStream: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.ReadableStream),\n/* harmony export */   Request: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Request),\n/* harmony export */   Response: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Response),\n/* harmony export */   auto: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.auto),\n/* harmony export */   fetch: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.fetch),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFromPath),\n/* harmony export */   getDefaultAgent: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.getDefaultAgent),\n/* harmony export */   getMultipartRequestOptions: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions),\n/* harmony export */   init: () => (/* binding */ init),\n/* harmony export */   isFsReadStream: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.isFsReadStream),\n/* harmony export */   kind: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.kind),\n/* harmony export */   setShims: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.setShims)\n/* harmony export */ });\n/* harmony import */ var _registry_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/registry.mjs\");\n/* harmony import */ var _tambo_ai_typescript_sdk_shims_auto_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tambo-ai/typescript-sdk/_shims/auto/runtime */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/node-runtime.mjs\");\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\n\n\nconst init = () => {\n  if (!_registry_mjs__WEBPACK_IMPORTED_MODULE_0__.kind) _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.setShims(_tambo_ai_typescript_sdk_shims_auto_runtime__WEBPACK_IMPORTED_MODULE_1__.getRuntime(), { auto: true });\n};\n\n\ninit();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL19zaGltcy9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ3dDO0FBQzZCO0FBQzlEO0FBQ1AsT0FBTywrQ0FBVSxFQUFFLG1EQUFjLENBQUMsbUZBQWUsTUFBTSxZQUFZO0FBQ25FO0FBQytCOztBQUUvQiIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL19zaGltcy9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEaXNjbGFpbWVyOiBtb2R1bGVzIGluIF9zaGltcyBhcmVuJ3QgaW50ZW5kZWQgdG8gYmUgaW1wb3J0ZWQgYnkgU0RLIHVzZXJzLlxuICovXG5pbXBvcnQgKiBhcyBzaGltcyBmcm9tICcuL3JlZ2lzdHJ5Lm1qcyc7XG5pbXBvcnQgKiBhcyBhdXRvIGZyb20gJ0B0YW1iby1haS90eXBlc2NyaXB0LXNkay9fc2hpbXMvYXV0by9ydW50aW1lJztcbmV4cG9ydCBjb25zdCBpbml0ID0gKCkgPT4ge1xuICBpZiAoIXNoaW1zLmtpbmQpIHNoaW1zLnNldFNoaW1zKGF1dG8uZ2V0UnVudGltZSgpLCB7IGF1dG86IHRydWUgfSk7XG59O1xuZXhwb3J0ICogZnJvbSAnLi9yZWdpc3RyeS5tanMnO1xuXG5pbml0KCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/node-runtime.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/_shims/node-runtime.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntime: () => (/* binding */ getRuntime)\n/* harmony export */ });\n/* harmony import */ var node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node-fetch */ \"(ssr)/./node_modules/node-fetch/lib/index.mjs\");\n/* harmony import */ var formdata_node__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! formdata-node */ \"(ssr)/./node_modules/formdata-node/lib/esm/index.js\");\n/* harmony import */ var agentkeepalive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! agentkeepalive */ \"(ssr)/./node_modules/agentkeepalive/index.js\");\n/* harmony import */ var abort_controller__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! abort-controller */ \"(ssr)/./node_modules/abort-controller/dist/abort-controller.js\");\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var form_data_encoder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! form-data-encoder */ \"(ssr)/./node_modules/form-data-encoder/lib/esm/index.js\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var _MultipartBody_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MultipartBody.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/MultipartBody.mjs\");\n/* harmony import */ var node_stream_web__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! node:stream/web */ \"node:stream/web\");\n\n\n\n\n\n\n\n\n\nlet fileFromPathWarned = false;\nasync function fileFromPath(path, ...args) {\n    // this import fails in environments that don't handle export maps correctly, like old versions of Jest\n    const { fileFromPath: _fileFromPath } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/formdata-node\"), __webpack_require__.e(\"vendor-chunks/node-domexception\")]).then(__webpack_require__.bind(__webpack_require__, /*! formdata-node/file-from-path */ \"(ssr)/./node_modules/formdata-node/lib/esm/fileFromPath.js\"));\n    if (!fileFromPathWarned) {\n        console.warn(`fileFromPath is deprecated; use fs.createReadStream(${JSON.stringify(path)}) instead`);\n        fileFromPathWarned = true;\n    }\n    // @ts-ignore\n    return await _fileFromPath(path, ...args);\n}\nconst defaultHttpAgent = new agentkeepalive__WEBPACK_IMPORTED_MODULE_2__({ keepAlive: true, timeout: 5 * 60 * 1000 });\nconst defaultHttpsAgent = new agentkeepalive__WEBPACK_IMPORTED_MODULE_2__.HttpsAgent({ keepAlive: true, timeout: 5 * 60 * 1000 });\nasync function getMultipartRequestOptions(form, opts) {\n    const encoder = new form_data_encoder__WEBPACK_IMPORTED_MODULE_5__.FormDataEncoder(form);\n    const readable = node_stream__WEBPACK_IMPORTED_MODULE_6__.Readable.from(encoder);\n    const body = new _MultipartBody_mjs__WEBPACK_IMPORTED_MODULE_8__.MultipartBody(readable);\n    const headers = {\n        ...opts.headers,\n        ...encoder.headers,\n        'Content-Length': encoder.contentLength,\n    };\n    return { ...opts, body: body, headers };\n}\nfunction getRuntime() {\n    // Polyfill global object if needed.\n    if (typeof AbortController === 'undefined') {\n        // @ts-expect-error (the types are subtly different, but compatible in practice)\n        globalThis.AbortController = abort_controller__WEBPACK_IMPORTED_MODULE_3__.AbortController;\n    }\n    return {\n        kind: 'node',\n        fetch: node_fetch__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        Request: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Request,\n        Response: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Response,\n        Headers: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers,\n        FormData: formdata_node__WEBPACK_IMPORTED_MODULE_1__.FormData,\n        Blob: formdata_node__WEBPACK_IMPORTED_MODULE_1__.Blob,\n        File: formdata_node__WEBPACK_IMPORTED_MODULE_1__.File,\n        ReadableStream: node_stream_web__WEBPACK_IMPORTED_MODULE_7__.ReadableStream,\n        getMultipartRequestOptions,\n        getDefaultAgent: (url) => (url.startsWith('https') ? defaultHttpsAgent : defaultHttpAgent),\n        fileFromPath,\n        isFsReadStream: (value) => value instanceof node_fs__WEBPACK_IMPORTED_MODULE_4__.ReadStream,\n    };\n}\n//# sourceMappingURL=node-runtime.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/node-runtime.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/registry.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/_shims/registry.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   File: () => (/* binding */ File),\n/* harmony export */   FormData: () => (/* binding */ FormData),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   ReadableStream: () => (/* binding */ ReadableStream),\n/* harmony export */   Request: () => (/* binding */ Request),\n/* harmony export */   Response: () => (/* binding */ Response),\n/* harmony export */   auto: () => (/* binding */ auto),\n/* harmony export */   fetch: () => (/* binding */ fetch),\n/* harmony export */   fileFromPath: () => (/* binding */ fileFromPath),\n/* harmony export */   getDefaultAgent: () => (/* binding */ getDefaultAgent),\n/* harmony export */   getMultipartRequestOptions: () => (/* binding */ getMultipartRequestOptions),\n/* harmony export */   isFsReadStream: () => (/* binding */ isFsReadStream),\n/* harmony export */   kind: () => (/* binding */ kind),\n/* harmony export */   setShims: () => (/* binding */ setShims)\n/* harmony export */ });\nlet auto = false;\nlet kind = undefined;\nlet fetch = undefined;\nlet Request = undefined;\nlet Response = undefined;\nlet Headers = undefined;\nlet FormData = undefined;\nlet Blob = undefined;\nlet File = undefined;\nlet ReadableStream = undefined;\nlet getMultipartRequestOptions = undefined;\nlet getDefaultAgent = undefined;\nlet fileFromPath = undefined;\nlet isFsReadStream = undefined;\nfunction setShims(shims, options = { auto: false }) {\n    if (auto) {\n        throw new Error(`you must \\`import '@tambo-ai/typescript-sdk/shims/${shims.kind}'\\` before importing anything else from @tambo-ai/typescript-sdk`);\n    }\n    if (kind) {\n        throw new Error(`can't \\`import '@tambo-ai/typescript-sdk/shims/${shims.kind}'\\` after \\`import '@tambo-ai/typescript-sdk/shims/${kind}'\\``);\n    }\n    auto = options.auto;\n    kind = shims.kind;\n    fetch = shims.fetch;\n    Request = shims.Request;\n    Response = shims.Response;\n    Headers = shims.Headers;\n    FormData = shims.FormData;\n    Blob = shims.Blob;\n    File = shims.File;\n    ReadableStream = shims.ReadableStream;\n    getMultipartRequestOptions = shims.getMultipartRequestOptions;\n    getDefaultAgent = shims.getDefaultAgent;\n    fileFromPath = shims.fileFromPath;\n    isFsReadStream = shims.isFsReadStream;\n}\n//# sourceMappingURL=registry.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL19zaGltcy9yZWdpc3RyeS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxhQUFhO0FBQ3pEO0FBQ0EsNkVBQTZFLFdBQVc7QUFDeEY7QUFDQTtBQUNBLDBFQUEwRSxXQUFXLHFEQUFxRCxLQUFLO0FBQy9JO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B0YW1iby1haS90eXBlc2NyaXB0LXNkay9fc2hpbXMvcmVnaXN0cnkubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBsZXQgYXV0byA9IGZhbHNlO1xuZXhwb3J0IGxldCBraW5kID0gdW5kZWZpbmVkO1xuZXhwb3J0IGxldCBmZXRjaCA9IHVuZGVmaW5lZDtcbmV4cG9ydCBsZXQgUmVxdWVzdCA9IHVuZGVmaW5lZDtcbmV4cG9ydCBsZXQgUmVzcG9uc2UgPSB1bmRlZmluZWQ7XG5leHBvcnQgbGV0IEhlYWRlcnMgPSB1bmRlZmluZWQ7XG5leHBvcnQgbGV0IEZvcm1EYXRhID0gdW5kZWZpbmVkO1xuZXhwb3J0IGxldCBCbG9iID0gdW5kZWZpbmVkO1xuZXhwb3J0IGxldCBGaWxlID0gdW5kZWZpbmVkO1xuZXhwb3J0IGxldCBSZWFkYWJsZVN0cmVhbSA9IHVuZGVmaW5lZDtcbmV4cG9ydCBsZXQgZ2V0TXVsdGlwYXJ0UmVxdWVzdE9wdGlvbnMgPSB1bmRlZmluZWQ7XG5leHBvcnQgbGV0IGdldERlZmF1bHRBZ2VudCA9IHVuZGVmaW5lZDtcbmV4cG9ydCBsZXQgZmlsZUZyb21QYXRoID0gdW5kZWZpbmVkO1xuZXhwb3J0IGxldCBpc0ZzUmVhZFN0cmVhbSA9IHVuZGVmaW5lZDtcbmV4cG9ydCBmdW5jdGlvbiBzZXRTaGltcyhzaGltcywgb3B0aW9ucyA9IHsgYXV0bzogZmFsc2UgfSkge1xuICAgIGlmIChhdXRvKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgeW91IG11c3QgXFxgaW1wb3J0ICdAdGFtYm8tYWkvdHlwZXNjcmlwdC1zZGsvc2hpbXMvJHtzaGltcy5raW5kfSdcXGAgYmVmb3JlIGltcG9ydGluZyBhbnl0aGluZyBlbHNlIGZyb20gQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrYCk7XG4gICAgfVxuICAgIGlmIChraW5kKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgY2FuJ3QgXFxgaW1wb3J0ICdAdGFtYm8tYWkvdHlwZXNjcmlwdC1zZGsvc2hpbXMvJHtzaGltcy5raW5kfSdcXGAgYWZ0ZXIgXFxgaW1wb3J0ICdAdGFtYm8tYWkvdHlwZXNjcmlwdC1zZGsvc2hpbXMvJHtraW5kfSdcXGBgKTtcbiAgICB9XG4gICAgYXV0byA9IG9wdGlvbnMuYXV0bztcbiAgICBraW5kID0gc2hpbXMua2luZDtcbiAgICBmZXRjaCA9IHNoaW1zLmZldGNoO1xuICAgIFJlcXVlc3QgPSBzaGltcy5SZXF1ZXN0O1xuICAgIFJlc3BvbnNlID0gc2hpbXMuUmVzcG9uc2U7XG4gICAgSGVhZGVycyA9IHNoaW1zLkhlYWRlcnM7XG4gICAgRm9ybURhdGEgPSBzaGltcy5Gb3JtRGF0YTtcbiAgICBCbG9iID0gc2hpbXMuQmxvYjtcbiAgICBGaWxlID0gc2hpbXMuRmlsZTtcbiAgICBSZWFkYWJsZVN0cmVhbSA9IHNoaW1zLlJlYWRhYmxlU3RyZWFtO1xuICAgIGdldE11bHRpcGFydFJlcXVlc3RPcHRpb25zID0gc2hpbXMuZ2V0TXVsdGlwYXJ0UmVxdWVzdE9wdGlvbnM7XG4gICAgZ2V0RGVmYXVsdEFnZW50ID0gc2hpbXMuZ2V0RGVmYXVsdEFnZW50O1xuICAgIGZpbGVGcm9tUGF0aCA9IHNoaW1zLmZpbGVGcm9tUGF0aDtcbiAgICBpc0ZzUmVhZFN0cmVhbSA9IHNoaW1zLmlzRnNSZWFkU3RyZWFtO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVnaXN0cnkubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/registry.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/core.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/core.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIClient: () => (/* binding */ APIClient),\n/* harmony export */   APIPromise: () => (/* binding */ APIPromise),\n/* harmony export */   AbstractPage: () => (/* binding */ AbstractPage),\n/* harmony export */   PagePromise: () => (/* binding */ PagePromise),\n/* harmony export */   castToError: () => (/* binding */ castToError),\n/* harmony export */   coerceBoolean: () => (/* binding */ coerceBoolean),\n/* harmony export */   coerceFloat: () => (/* binding */ coerceFloat),\n/* harmony export */   coerceInteger: () => (/* binding */ coerceInteger),\n/* harmony export */   createForm: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.createForm),\n/* harmony export */   createResponseHeaders: () => (/* binding */ createResponseHeaders),\n/* harmony export */   debug: () => (/* binding */ debug),\n/* harmony export */   ensurePresent: () => (/* binding */ ensurePresent),\n/* harmony export */   getHeader: () => (/* binding */ getHeader),\n/* harmony export */   getRequiredHeader: () => (/* binding */ getRequiredHeader),\n/* harmony export */   hasOwn: () => (/* binding */ hasOwn),\n/* harmony export */   isEmptyObj: () => (/* binding */ isEmptyObj),\n/* harmony export */   isHeadersProtocol: () => (/* binding */ isHeadersProtocol),\n/* harmony export */   isObj: () => (/* binding */ isObj),\n/* harmony export */   isRequestOptions: () => (/* binding */ isRequestOptions),\n/* harmony export */   isRunningInBrowser: () => (/* binding */ isRunningInBrowser),\n/* harmony export */   maybeCoerceBoolean: () => (/* binding */ maybeCoerceBoolean),\n/* harmony export */   maybeCoerceFloat: () => (/* binding */ maybeCoerceFloat),\n/* harmony export */   maybeCoerceInteger: () => (/* binding */ maybeCoerceInteger),\n/* harmony export */   maybeMultipartFormRequestOptions: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.maybeMultipartFormRequestOptions),\n/* harmony export */   multipartFormRequestOptions: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions),\n/* harmony export */   readEnv: () => (/* binding */ readEnv),\n/* harmony export */   safeJSON: () => (/* binding */ safeJSON),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   toBase64: () => (/* binding */ toBase64)\n/* harmony export */ });\n/* harmony import */ var _version_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./version.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/version.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./error.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/error.mjs\");\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_shims/index.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/index.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./uploads.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/uploads.mjs\");\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _AbstractPage_client;\n\n\n\n// try running side effects outside of _shims/index to workaround https://github.com/vercel/next.js/issues/76881\n(0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.init)();\n\n\nasync function defaultParseResponse(props) {\n    const { response } = props;\n    // fetch refuses to read the body when the status code is 204.\n    if (response.status === 204) {\n        return null;\n    }\n    if (props.options.__binaryResponse) {\n        return response;\n    }\n    const contentType = response.headers.get('content-type');\n    const mediaType = contentType?.split(';')[0]?.trim();\n    const isJSON = mediaType?.includes('application/json') || mediaType?.endsWith('+json');\n    if (isJSON) {\n        const json = await response.json();\n        debug('response', response.status, response.url, response.headers, json);\n        return json;\n    }\n    const text = await response.text();\n    debug('response', response.status, response.url, response.headers, text);\n    // TODO handle blob, arraybuffer, other content types, etc.\n    return text;\n}\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */\nclass APIPromise extends Promise {\n    constructor(responsePromise, parseResponse = defaultParseResponse) {\n        super((resolve) => {\n            // this is maybe a bit weird but this has to be a no-op to not implicitly\n            // parse the response body; instead .then, .catch, .finally are overridden\n            // to parse the response\n            resolve(null);\n        });\n        this.responsePromise = responsePromise;\n        this.parseResponse = parseResponse;\n    }\n    _thenUnwrap(transform) {\n        return new APIPromise(this.responsePromise, async (props) => transform(await this.parseResponse(props), props));\n    }\n    /**\n     * Gets the raw `Response` instance instead of parsing the response\n     * data.\n     *\n     * If you want to parse the response body but still get the `Response`\n     * instance, you can use {@link withResponse()}.\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from '@tambo-ai/typescript-sdk'`:\n     * - `import '@tambo-ai/typescript-sdk/shims/node'` (if you're running on Node)\n     * - `import '@tambo-ai/typescript-sdk/shims/web'` (otherwise)\n     */\n    asResponse() {\n        return this.responsePromise.then((p) => p.response);\n    }\n    /**\n     * Gets the parsed response data and the raw `Response` instance.\n     *\n     * If you just want to get the raw `Response` instance without parsing it,\n     * you can use {@link asResponse()}.\n     *\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from '@tambo-ai/typescript-sdk'`:\n     * - `import '@tambo-ai/typescript-sdk/shims/node'` (if you're running on Node)\n     * - `import '@tambo-ai/typescript-sdk/shims/web'` (otherwise)\n     */\n    async withResponse() {\n        const [data, response] = await Promise.all([this.parse(), this.asResponse()]);\n        return { data, response };\n    }\n    parse() {\n        if (!this.parsedPromise) {\n            this.parsedPromise = this.responsePromise.then(this.parseResponse);\n        }\n        return this.parsedPromise;\n    }\n    then(onfulfilled, onrejected) {\n        return this.parse().then(onfulfilled, onrejected);\n    }\n    catch(onrejected) {\n        return this.parse().catch(onrejected);\n    }\n    finally(onfinally) {\n        return this.parse().finally(onfinally);\n    }\n}\nclass APIClient {\n    constructor({ baseURL, maxRetries = 0, timeout = 60000, // 1 minute\n    httpAgent, fetch: overriddenFetch, }) {\n        this.baseURL = baseURL;\n        this.maxRetries = validatePositiveInteger('maxRetries', maxRetries);\n        this.timeout = validatePositiveInteger('timeout', timeout);\n        this.httpAgent = httpAgent;\n        this.fetch = overriddenFetch ?? _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.fetch;\n    }\n    authHeaders(opts) {\n        return {};\n    }\n    /**\n     * Override this to add your own default headers, for example:\n     *\n     *  {\n     *    ...super.defaultHeaders(),\n     *    Authorization: 'Bearer 123',\n     *  }\n     */\n    defaultHeaders(opts) {\n        return {\n            Accept: 'application/json',\n            'Content-Type': 'application/json',\n            'User-Agent': this.getUserAgent(),\n            ...getPlatformHeaders(),\n            ...this.authHeaders(opts),\n        };\n    }\n    /**\n     * Override this to add your own headers validation:\n     */\n    validateHeaders(headers, customHeaders) { }\n    defaultIdempotencyKey() {\n        return `stainless-node-retry-${uuid4()}`;\n    }\n    get(path, opts) {\n        return this.methodRequest('get', path, opts);\n    }\n    post(path, opts) {\n        return this.methodRequest('post', path, opts);\n    }\n    patch(path, opts) {\n        return this.methodRequest('patch', path, opts);\n    }\n    put(path, opts) {\n        return this.methodRequest('put', path, opts);\n    }\n    delete(path, opts) {\n        return this.methodRequest('delete', path, opts);\n    }\n    methodRequest(method, path, opts) {\n        return this.request(Promise.resolve(opts).then(async (opts) => {\n            const body = opts && (0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isBlobLike)(opts?.body) ? new DataView(await opts.body.arrayBuffer())\n                : opts?.body instanceof DataView ? opts.body\n                    : opts?.body instanceof ArrayBuffer ? new DataView(opts.body)\n                        : opts && ArrayBuffer.isView(opts?.body) ? new DataView(opts.body.buffer)\n                            : opts?.body;\n            return { method, path, ...opts, body };\n        }));\n    }\n    getAPIList(path, Page, opts) {\n        return this.requestAPIList(Page, { method: 'get', path, ...opts });\n    }\n    calculateContentLength(body) {\n        if (typeof body === 'string') {\n            if (typeof Buffer !== 'undefined') {\n                return Buffer.byteLength(body, 'utf8').toString();\n            }\n            if (typeof TextEncoder !== 'undefined') {\n                const encoder = new TextEncoder();\n                const encoded = encoder.encode(body);\n                return encoded.length.toString();\n            }\n        }\n        else if (ArrayBuffer.isView(body)) {\n            return body.byteLength.toString();\n        }\n        return null;\n    }\n    buildRequest(inputOptions, { retryCount = 0 } = {}) {\n        const options = { ...inputOptions };\n        const { method, path, query, headers: headers = {} } = options;\n        const body = ArrayBuffer.isView(options.body) || (options.__binaryRequest && typeof options.body === 'string') ?\n            options.body\n            : (0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isMultipartBody)(options.body) ? options.body.body\n                : options.body ? JSON.stringify(options.body, null, 2)\n                    : null;\n        const contentLength = this.calculateContentLength(body);\n        const url = this.buildURL(path, query);\n        if ('timeout' in options)\n            validatePositiveInteger('timeout', options.timeout);\n        options.timeout = options.timeout ?? this.timeout;\n        const httpAgent = options.httpAgent ?? this.httpAgent ?? (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getDefaultAgent)(url);\n        const minAgentTimeout = options.timeout + 1000;\n        if (typeof httpAgent?.options?.timeout === 'number' &&\n            minAgentTimeout > (httpAgent.options.timeout ?? 0)) {\n            // Allow any given request to bump our agent active socket timeout.\n            // This may seem strange, but leaking active sockets should be rare and not particularly problematic,\n            // and without mutating agent we would need to create more of them.\n            // This tradeoff optimizes for performance.\n            httpAgent.options.timeout = minAgentTimeout;\n        }\n        if (this.idempotencyHeader && method !== 'get') {\n            if (!inputOptions.idempotencyKey)\n                inputOptions.idempotencyKey = this.defaultIdempotencyKey();\n            headers[this.idempotencyHeader] = inputOptions.idempotencyKey;\n        }\n        const reqHeaders = this.buildHeaders({ options, headers, contentLength, retryCount });\n        const req = {\n            method,\n            ...(body && { body: body }),\n            headers: reqHeaders,\n            ...(httpAgent && { agent: httpAgent }),\n            // @ts-ignore node-fetch uses a custom AbortSignal type that is\n            // not compatible with standard web types\n            signal: options.signal ?? null,\n        };\n        return { req, url, timeout: options.timeout };\n    }\n    buildHeaders({ options, headers, contentLength, retryCount, }) {\n        const reqHeaders = {};\n        if (contentLength) {\n            reqHeaders['content-length'] = contentLength;\n        }\n        const defaultHeaders = this.defaultHeaders(options);\n        applyHeadersMut(reqHeaders, defaultHeaders);\n        applyHeadersMut(reqHeaders, headers);\n        // let builtin fetch set the Content-Type for multipart bodies\n        if ((0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isMultipartBody)(options.body) && _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.kind !== 'node') {\n            delete reqHeaders['content-type'];\n        }\n        // Don't set theses headers if they were already set or removed through default headers or by the caller.\n        // We check `defaultHeaders` and `headers`, which can contain nulls, instead of `reqHeaders` to account\n        // for the removal case.\n        if (getHeader(defaultHeaders, 'x-stainless-retry-count') === undefined &&\n            getHeader(headers, 'x-stainless-retry-count') === undefined) {\n            reqHeaders['x-stainless-retry-count'] = String(retryCount);\n        }\n        if (getHeader(defaultHeaders, 'x-stainless-timeout') === undefined &&\n            getHeader(headers, 'x-stainless-timeout') === undefined &&\n            options.timeout) {\n            reqHeaders['x-stainless-timeout'] = String(Math.trunc(options.timeout / 1000));\n        }\n        this.validateHeaders(reqHeaders, headers);\n        return reqHeaders;\n    }\n    /**\n     * Used as a callback for mutating the given `FinalRequestOptions` object.\n     */\n    async prepareOptions(options) { }\n    /**\n     * Used as a callback for mutating the given `RequestInit` object.\n     *\n     * This is useful for cases where you want to add certain headers based off of\n     * the request properties, e.g. `method` or `url`.\n     */\n    async prepareRequest(request, { url, options }) { }\n    parseHeaders(headers) {\n        return (!headers ? {}\n            : Symbol.iterator in headers ?\n                Object.fromEntries(Array.from(headers).map((header) => [...header]))\n                : { ...headers });\n    }\n    makeStatusError(status, error, message, headers) {\n        return _error_mjs__WEBPACK_IMPORTED_MODULE_2__.APIError.generate(status, error, message, headers);\n    }\n    request(options, remainingRetries = null) {\n        return new APIPromise(this.makeRequest(options, remainingRetries));\n    }\n    async makeRequest(optionsInput, retriesRemaining) {\n        const options = await optionsInput;\n        const maxRetries = options.maxRetries ?? this.maxRetries;\n        if (retriesRemaining == null) {\n            retriesRemaining = maxRetries;\n        }\n        await this.prepareOptions(options);\n        const { req, url, timeout } = this.buildRequest(options, { retryCount: maxRetries - retriesRemaining });\n        await this.prepareRequest(req, { url, options });\n        debug('request', url, options, req.headers);\n        if (options.signal?.aborted) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.APIUserAbortError();\n        }\n        const controller = new AbortController();\n        const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(castToError);\n        if (response instanceof Error) {\n            if (options.signal?.aborted) {\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.APIUserAbortError();\n            }\n            if (retriesRemaining) {\n                return this.retryRequest(options, retriesRemaining);\n            }\n            if (response.name === 'AbortError') {\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.APIConnectionTimeoutError();\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.APIConnectionError({ cause: response });\n        }\n        const responseHeaders = createResponseHeaders(response.headers);\n        if (!response.ok) {\n            if (retriesRemaining && this.shouldRetry(response)) {\n                const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n                debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders);\n                return this.retryRequest(options, retriesRemaining, responseHeaders);\n            }\n            const errText = await response.text().catch((e) => castToError(e).message);\n            const errJSON = safeJSON(errText);\n            const errMessage = errJSON ? undefined : errText;\n            const retryMessage = retriesRemaining ? `(error; no more retries left)` : `(error; not retryable)`;\n            debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders, errMessage);\n            const err = this.makeStatusError(response.status, errJSON, errMessage, responseHeaders);\n            throw err;\n        }\n        return { response, options, controller };\n    }\n    requestAPIList(Page, options) {\n        const request = this.makeRequest(options, null);\n        return new PagePromise(this, request, Page);\n    }\n    buildURL(path, query) {\n        const url = isAbsoluteURL(path) ?\n            new URL(path)\n            : new URL(this.baseURL + (this.baseURL.endsWith('/') && path.startsWith('/') ? path.slice(1) : path));\n        const defaultQuery = this.defaultQuery();\n        if (!isEmptyObj(defaultQuery)) {\n            query = { ...defaultQuery, ...query };\n        }\n        if (typeof query === 'object' && query && !Array.isArray(query)) {\n            url.search = this.stringifyQuery(query);\n        }\n        return url.toString();\n    }\n    stringifyQuery(query) {\n        return Object.entries(query)\n            .filter(([_, value]) => typeof value !== 'undefined')\n            .map(([key, value]) => {\n            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n            }\n            if (value === null) {\n                return `${encodeURIComponent(key)}=`;\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.TamboAIError(`Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`);\n        })\n            .join('&');\n    }\n    async fetchWithTimeout(url, init, ms, controller) {\n        const { signal, ...options } = init || {};\n        if (signal)\n            signal.addEventListener('abort', () => controller.abort());\n        const timeout = setTimeout(() => controller.abort(), ms);\n        const fetchOptions = {\n            signal: controller.signal,\n            ...options,\n        };\n        if (fetchOptions.method) {\n            // Custom methods like 'patch' need to be uppercased\n            // See https://github.com/nodejs/undici/issues/2294\n            fetchOptions.method = fetchOptions.method.toUpperCase();\n        }\n        return (\n        // use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n        this.fetch.call(undefined, url, fetchOptions).finally(() => {\n            clearTimeout(timeout);\n        }));\n    }\n    shouldRetry(response) {\n        // Note this is not a standard header.\n        const shouldRetryHeader = response.headers.get('x-should-retry');\n        // If the server explicitly says whether or not to retry, obey.\n        if (shouldRetryHeader === 'true')\n            return true;\n        if (shouldRetryHeader === 'false')\n            return false;\n        // Retry on request timeouts.\n        if (response.status === 408)\n            return true;\n        // Retry on lock timeouts.\n        if (response.status === 409)\n            return true;\n        // Retry on rate limits.\n        if (response.status === 429)\n            return true;\n        // Retry internal errors.\n        if (response.status >= 500)\n            return true;\n        return false;\n    }\n    async retryRequest(options, retriesRemaining, responseHeaders) {\n        let timeoutMillis;\n        // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n        const retryAfterMillisHeader = responseHeaders?.['retry-after-ms'];\n        if (retryAfterMillisHeader) {\n            const timeoutMs = parseFloat(retryAfterMillisHeader);\n            if (!Number.isNaN(timeoutMs)) {\n                timeoutMillis = timeoutMs;\n            }\n        }\n        // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n        const retryAfterHeader = responseHeaders?.['retry-after'];\n        if (retryAfterHeader && !timeoutMillis) {\n            const timeoutSeconds = parseFloat(retryAfterHeader);\n            if (!Number.isNaN(timeoutSeconds)) {\n                timeoutMillis = timeoutSeconds * 1000;\n            }\n            else {\n                timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n            }\n        }\n        // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n        // just do what it says, but otherwise calculate a default\n        if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n            const maxRetries = options.maxRetries ?? this.maxRetries;\n            timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n        }\n        await sleep(timeoutMillis);\n        return this.makeRequest(options, retriesRemaining - 1);\n    }\n    calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries) {\n        const initialRetryDelay = 1.0;\n        const maxRetryDelay = 5.0;\n        const numRetries = maxRetries - retriesRemaining;\n        // Apply exponential backoff, but not more than the max.\n        const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n        // Apply some jitter, take up to at most 25 percent of the retry time.\n        const jitter = 1 - Math.random() * 0.25;\n        return sleepSeconds * jitter * 1000;\n    }\n    getUserAgent() {\n        return `${this.constructor.name}/JS ${_version_mjs__WEBPACK_IMPORTED_MODULE_3__.VERSION}`;\n    }\n}\nclass AbstractPage {\n    constructor(client, response, body, options) {\n        _AbstractPage_client.set(this, void 0);\n        __classPrivateFieldSet(this, _AbstractPage_client, client, \"f\");\n        this.options = options;\n        this.response = response;\n        this.body = body;\n    }\n    hasNextPage() {\n        const items = this.getPaginatedItems();\n        if (!items.length)\n            return false;\n        return this.nextPageInfo() != null;\n    }\n    async getNextPage() {\n        const nextInfo = this.nextPageInfo();\n        if (!nextInfo) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.TamboAIError('No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.');\n        }\n        const nextOptions = { ...this.options };\n        if ('params' in nextInfo && typeof nextOptions.query === 'object') {\n            nextOptions.query = { ...nextOptions.query, ...nextInfo.params };\n        }\n        else if ('url' in nextInfo) {\n            const params = [...Object.entries(nextOptions.query || {}), ...nextInfo.url.searchParams.entries()];\n            for (const [key, value] of params) {\n                nextInfo.url.searchParams.set(key, value);\n            }\n            nextOptions.query = undefined;\n            nextOptions.path = nextInfo.url.toString();\n        }\n        return await __classPrivateFieldGet(this, _AbstractPage_client, \"f\").requestAPIList(this.constructor, nextOptions);\n    }\n    async *iterPages() {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        let page = this;\n        yield page;\n        while (page.hasNextPage()) {\n            page = await page.getNextPage();\n            yield page;\n        }\n    }\n    async *[(_AbstractPage_client = new WeakMap(), Symbol.asyncIterator)]() {\n        for await (const page of this.iterPages()) {\n            for (const item of page.getPaginatedItems()) {\n                yield item;\n            }\n        }\n    }\n}\n/**\n * This subclass of Promise will resolve to an instantiated Page once the request completes.\n *\n * It also implements AsyncIterable to allow auto-paginating iteration on an unawaited list call, eg:\n *\n *    for await (const item of client.items.list()) {\n *      console.log(item)\n *    }\n */\nclass PagePromise extends APIPromise {\n    constructor(client, request, Page) {\n        super(request, async (props) => new Page(client, props.response, await defaultParseResponse(props), props.options));\n    }\n    /**\n     * Allow auto-paginating iteration on an unawaited list call, eg:\n     *\n     *    for await (const item of client.items.list()) {\n     *      console.log(item)\n     *    }\n     */\n    async *[Symbol.asyncIterator]() {\n        const page = await this;\n        for await (const item of page) {\n            yield item;\n        }\n    }\n}\nconst createResponseHeaders = (headers) => {\n    return new Proxy(Object.fromEntries(\n    // @ts-ignore\n    headers.entries()), {\n        get(target, name) {\n            const key = name.toString();\n            return target[key.toLowerCase()] || target[key];\n        },\n    });\n};\n// This is required so that we can determine if a given object matches the RequestOptions\n// type at runtime. While this requires duplication, it is enforced by the TypeScript\n// compiler such that any missing / extraneous keys will cause an error.\nconst requestOptionsKeys = {\n    method: true,\n    path: true,\n    query: true,\n    body: true,\n    headers: true,\n    maxRetries: true,\n    stream: true,\n    timeout: true,\n    httpAgent: true,\n    signal: true,\n    idempotencyKey: true,\n    __binaryRequest: true,\n    __binaryResponse: true,\n};\nconst isRequestOptions = (obj) => {\n    return (typeof obj === 'object' &&\n        obj !== null &&\n        !isEmptyObj(obj) &&\n        Object.keys(obj).every((k) => hasOwn(requestOptionsKeys, k)));\n};\nconst getPlatformProperties = () => {\n    if (typeof Deno !== 'undefined' && Deno.build != null) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_3__.VERSION,\n            'X-Stainless-OS': normalizePlatform(Deno.build.os),\n            'X-Stainless-Arch': normalizeArch(Deno.build.arch),\n            'X-Stainless-Runtime': 'deno',\n            'X-Stainless-Runtime-Version': typeof Deno.version === 'string' ? Deno.version : Deno.version?.deno ?? 'unknown',\n        };\n    }\n    if (typeof EdgeRuntime !== 'undefined') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_3__.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': `other:${EdgeRuntime}`,\n            'X-Stainless-Runtime': 'edge',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    // Check if Node.js\n    if (Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_3__.VERSION,\n            'X-Stainless-OS': normalizePlatform(process.platform),\n            'X-Stainless-Arch': normalizeArch(process.arch),\n            'X-Stainless-Runtime': 'node',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    const browserInfo = getBrowserInfo();\n    if (browserInfo) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_3__.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': 'unknown',\n            'X-Stainless-Runtime': `browser:${browserInfo.browser}`,\n            'X-Stainless-Runtime-Version': browserInfo.version,\n        };\n    }\n    // TODO add support for Cloudflare workers, etc.\n    return {\n        'X-Stainless-Lang': 'js',\n        'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_3__.VERSION,\n        'X-Stainless-OS': 'Unknown',\n        'X-Stainless-Arch': 'unknown',\n        'X-Stainless-Runtime': 'unknown',\n        'X-Stainless-Runtime-Version': 'unknown',\n    };\n};\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo() {\n    if (typeof navigator === 'undefined' || !navigator) {\n        return null;\n    }\n    // NOTE: The order matters here!\n    const browserPatterns = [\n        { key: 'edge', pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'chrome', pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'firefox', pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'safari', pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/ },\n    ];\n    // Find the FIRST matching browser\n    for (const { key, pattern } of browserPatterns) {\n        const match = pattern.exec(navigator.userAgent);\n        if (match) {\n            const major = match[1] || 0;\n            const minor = match[2] || 0;\n            const patch = match[3] || 0;\n            return { browser: key, version: `${major}.${minor}.${patch}` };\n        }\n    }\n    return null;\n}\nconst normalizeArch = (arch) => {\n    // Node docs:\n    // - https://nodejs.org/api/process.html#processarch\n    // Deno docs:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    if (arch === 'x32')\n        return 'x32';\n    if (arch === 'x86_64' || arch === 'x64')\n        return 'x64';\n    if (arch === 'arm')\n        return 'arm';\n    if (arch === 'aarch64' || arch === 'arm64')\n        return 'arm64';\n    if (arch)\n        return `other:${arch}`;\n    return 'unknown';\n};\nconst normalizePlatform = (platform) => {\n    // Node platforms:\n    // - https://nodejs.org/api/process.html#processplatform\n    // Deno platforms:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    // - https://github.com/denoland/deno/issues/14799\n    platform = platform.toLowerCase();\n    // NOTE: this iOS check is untested and may not work\n    // Node does not work natively on IOS, there is a fork at\n    // https://github.com/nodejs-mobile/nodejs-mobile\n    // however it is unknown at the time of writing how to detect if it is running\n    if (platform.includes('ios'))\n        return 'iOS';\n    if (platform === 'android')\n        return 'Android';\n    if (platform === 'darwin')\n        return 'MacOS';\n    if (platform === 'win32')\n        return 'Windows';\n    if (platform === 'freebsd')\n        return 'FreeBSD';\n    if (platform === 'openbsd')\n        return 'OpenBSD';\n    if (platform === 'linux')\n        return 'Linux';\n    if (platform)\n        return `Other:${platform}`;\n    return 'Unknown';\n};\nlet _platformHeaders;\nconst getPlatformHeaders = () => {\n    return (_platformHeaders ?? (_platformHeaders = getPlatformProperties()));\n};\nconst safeJSON = (text) => {\n    try {\n        return JSON.parse(text);\n    }\n    catch (err) {\n        return undefined;\n    }\n};\n// https://url.spec.whatwg.org/#url-scheme-string\nconst startsWithSchemeRegexp = /^[a-z][a-z0-9+.-]*:/i;\nconst isAbsoluteURL = (url) => {\n    return startsWithSchemeRegexp.test(url);\n};\nconst sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\nconst validatePositiveInteger = (name, n) => {\n    if (typeof n !== 'number' || !Number.isInteger(n)) {\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.TamboAIError(`${name} must be an integer`);\n    }\n    if (n < 0) {\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.TamboAIError(`${name} must be a positive integer`);\n    }\n    return n;\n};\nconst castToError = (err) => {\n    if (err instanceof Error)\n        return err;\n    if (typeof err === 'object' && err !== null) {\n        try {\n            return new Error(JSON.stringify(err));\n        }\n        catch { }\n    }\n    return new Error(err);\n};\nconst ensurePresent = (value) => {\n    if (value == null)\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.TamboAIError(`Expected a value to be given but received ${value} instead.`);\n    return value;\n};\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */\nconst readEnv = (env) => {\n    if (typeof process !== 'undefined') {\n        return process.env?.[env]?.trim() ?? undefined;\n    }\n    if (typeof Deno !== 'undefined') {\n        return Deno.env?.get?.(env)?.trim();\n    }\n    return undefined;\n};\nconst coerceInteger = (value) => {\n    if (typeof value === 'number')\n        return Math.round(value);\n    if (typeof value === 'string')\n        return parseInt(value, 10);\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.TamboAIError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nconst coerceFloat = (value) => {\n    if (typeof value === 'number')\n        return value;\n    if (typeof value === 'string')\n        return parseFloat(value);\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.TamboAIError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nconst coerceBoolean = (value) => {\n    if (typeof value === 'boolean')\n        return value;\n    if (typeof value === 'string')\n        return value === 'true';\n    return Boolean(value);\n};\nconst maybeCoerceInteger = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceInteger(value);\n};\nconst maybeCoerceFloat = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceFloat(value);\n};\nconst maybeCoerceBoolean = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceBoolean(value);\n};\n// https://stackoverflow.com/a/34491287\nfunction isEmptyObj(obj) {\n    if (!obj)\n        return true;\n    for (const _k in obj)\n        return false;\n    return true;\n}\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nfunction hasOwn(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n/**\n * Copies headers from \"newHeaders\" onto \"targetHeaders\",\n * using lower-case for all properties,\n * ignoring any keys with undefined values,\n * and deleting any keys with null values.\n */\nfunction applyHeadersMut(targetHeaders, newHeaders) {\n    for (const k in newHeaders) {\n        if (!hasOwn(newHeaders, k))\n            continue;\n        const lowerKey = k.toLowerCase();\n        if (!lowerKey)\n            continue;\n        const val = newHeaders[k];\n        if (val === null) {\n            delete targetHeaders[lowerKey];\n        }\n        else if (val !== undefined) {\n            targetHeaders[lowerKey] = val;\n        }\n    }\n}\nfunction debug(action, ...args) {\n    if (typeof process !== 'undefined' && process?.env?.['DEBUG'] === 'true') {\n        console.log(`TamboAI:DEBUG:${action}`, ...args);\n    }\n}\n/**\n * https://stackoverflow.com/a/2117523\n */\nconst uuid4 = () => {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16) | 0;\n        const v = c === 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n};\nconst isRunningInBrowser = () => {\n    return (\n    // @ts-ignore\n    typeof window !== 'undefined' &&\n        // @ts-ignore\n        typeof window.document !== 'undefined' &&\n        // @ts-ignore\n        typeof navigator !== 'undefined');\n};\nconst isHeadersProtocol = (headers) => {\n    return typeof headers?.get === 'function';\n};\nconst getRequiredHeader = (headers, header) => {\n    const foundHeader = getHeader(headers, header);\n    if (foundHeader === undefined) {\n        throw new Error(`Could not find ${header} header`);\n    }\n    return foundHeader;\n};\nconst getHeader = (headers, header) => {\n    const lowerCasedHeader = header.toLowerCase();\n    if (isHeadersProtocol(headers)) {\n        // to deal with the case where the header looks like Stainless-Event-Id\n        const intercapsHeader = header[0]?.toUpperCase() +\n            header.substring(1).replace(/([^\\w])(\\w)/g, (_m, g1, g2) => g1 + g2.toUpperCase());\n        for (const key of [header, lowerCasedHeader, header.toUpperCase(), intercapsHeader]) {\n            const value = headers.get(key);\n            if (value) {\n                return value;\n            }\n        }\n    }\n    for (const [key, value] of Object.entries(headers)) {\n        if (key.toLowerCase() === lowerCasedHeader) {\n            if (Array.isArray(value)) {\n                if (value.length <= 1)\n                    return value[0];\n                console.warn(`Received ${value.length} entries for the ${header} header, using the first entry.`);\n                return value[0];\n            }\n            return value;\n        }\n    }\n    return undefined;\n};\n/**\n * Encodes a string to Base64 format.\n */\nconst toBase64 = (str) => {\n    if (!str)\n        return '';\n    if (typeof Buffer !== 'undefined') {\n        return Buffer.from(str).toString('base64');\n    }\n    if (typeof btoa !== 'undefined') {\n        return btoa(str);\n    }\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_2__.TamboAIError('Cannot generate b64 string; Expected `Buffer` or `btoa` to be defined');\n};\nfunction isObj(obj) {\n    return obj != null && typeof obj === 'object' && !Array.isArray(obj);\n}\n//# sourceMappingURL=core.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/core.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/error.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/error.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIConnectionError: () => (/* binding */ APIConnectionError),\n/* harmony export */   APIConnectionTimeoutError: () => (/* binding */ APIConnectionTimeoutError),\n/* harmony export */   APIError: () => (/* binding */ APIError),\n/* harmony export */   APIUserAbortError: () => (/* binding */ APIUserAbortError),\n/* harmony export */   AuthenticationError: () => (/* binding */ AuthenticationError),\n/* harmony export */   BadRequestError: () => (/* binding */ BadRequestError),\n/* harmony export */   ConflictError: () => (/* binding */ ConflictError),\n/* harmony export */   InternalServerError: () => (/* binding */ InternalServerError),\n/* harmony export */   NotFoundError: () => (/* binding */ NotFoundError),\n/* harmony export */   PermissionDeniedError: () => (/* binding */ PermissionDeniedError),\n/* harmony export */   RateLimitError: () => (/* binding */ RateLimitError),\n/* harmony export */   TamboAIError: () => (/* binding */ TamboAIError),\n/* harmony export */   UnprocessableEntityError: () => (/* binding */ UnprocessableEntityError)\n/* harmony export */ });\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/core.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass TamboAIError extends Error {\n}\nclass APIError extends TamboAIError {\n    constructor(status, error, message, headers) {\n        super(`${APIError.makeMessage(status, error, message)}`);\n        this.status = status;\n        this.headers = headers;\n        this.error = error;\n    }\n    static makeMessage(status, error, message) {\n        const msg = error?.message ?\n            typeof error.message === 'string' ?\n                error.message\n                : JSON.stringify(error.message)\n            : error ? JSON.stringify(error)\n                : message;\n        if (status && msg) {\n            return `${status} ${msg}`;\n        }\n        if (status) {\n            return `${status} status code (no body)`;\n        }\n        if (msg) {\n            return msg;\n        }\n        return '(no status code or body)';\n    }\n    static generate(status, errorResponse, message, headers) {\n        if (!status || !headers) {\n            return new APIConnectionError({ message, cause: (0,_core_mjs__WEBPACK_IMPORTED_MODULE_0__.castToError)(errorResponse) });\n        }\n        const error = errorResponse;\n        if (status === 400) {\n            return new BadRequestError(status, error, message, headers);\n        }\n        if (status === 401) {\n            return new AuthenticationError(status, error, message, headers);\n        }\n        if (status === 403) {\n            return new PermissionDeniedError(status, error, message, headers);\n        }\n        if (status === 404) {\n            return new NotFoundError(status, error, message, headers);\n        }\n        if (status === 409) {\n            return new ConflictError(status, error, message, headers);\n        }\n        if (status === 422) {\n            return new UnprocessableEntityError(status, error, message, headers);\n        }\n        if (status === 429) {\n            return new RateLimitError(status, error, message, headers);\n        }\n        if (status >= 500) {\n            return new InternalServerError(status, error, message, headers);\n        }\n        return new APIError(status, error, message, headers);\n    }\n}\nclass APIUserAbortError extends APIError {\n    constructor({ message } = {}) {\n        super(undefined, undefined, message || 'Request was aborted.', undefined);\n    }\n}\nclass APIConnectionError extends APIError {\n    constructor({ message, cause }) {\n        super(undefined, undefined, message || 'Connection error.', undefined);\n        // in some environments the 'cause' property is already declared\n        // @ts-ignore\n        if (cause)\n            this.cause = cause;\n    }\n}\nclass APIConnectionTimeoutError extends APIConnectionError {\n    constructor({ message } = {}) {\n        super({ message: message ?? 'Request timed out.' });\n    }\n}\nclass BadRequestError extends APIError {\n}\nclass AuthenticationError extends APIError {\n}\nclass PermissionDeniedError extends APIError {\n}\nclass NotFoundError extends APIError {\n}\nclass ConflictError extends APIError {\n}\nclass UnprocessableEntityError extends APIError {\n}\nclass RateLimitError extends APIError {\n}\nclass InternalServerError extends APIError {\n}\n//# sourceMappingURL=error.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/error.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIConnectionError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionError),\n/* harmony export */   APIConnectionTimeoutError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionTimeoutError),\n/* harmony export */   APIError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError),\n/* harmony export */   APIUserAbortError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIUserAbortError),\n/* harmony export */   AuthenticationError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.AuthenticationError),\n/* harmony export */   BadRequestError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.BadRequestError),\n/* harmony export */   ConflictError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.ConflictError),\n/* harmony export */   InternalServerError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.InternalServerError),\n/* harmony export */   NotFoundError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.NotFoundError),\n/* harmony export */   PermissionDeniedError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.PermissionDeniedError),\n/* harmony export */   RateLimitError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.RateLimitError),\n/* harmony export */   TamboAI: () => (/* binding */ TamboAI),\n/* harmony export */   TamboAIError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.TamboAIError),\n/* harmony export */   UnprocessableEntityError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.UnprocessableEntityError),\n/* harmony export */   advanceStream: () => (/* reexport safe */ _lib_advance_stream_mjs__WEBPACK_IMPORTED_MODULE_5__.advanceStream),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_4__.fileFromPath),\n/* harmony export */   toFile: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_3__.toFile)\n/* harmony export */ });\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/core.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/error.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./uploads.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/uploads.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./uploads.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/index.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resources/beta/beta.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/beta.mjs\");\n/* harmony import */ var _lib_advance_stream_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/advance-stream.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/lib/advance-stream.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar _a;\n\n\n\n\n\n\nconst environments = {\n    production: 'https://api.tambo.co',\n    staging: 'https://hydra-api-dev.up.railway.app',\n};\n/**\n * API Client for interfacing with the Tambo AI API.\n */\nclass TamboAI extends _core_mjs__WEBPACK_IMPORTED_MODULE_0__.APIClient {\n    /**\n     * API Client for interfacing with the Tambo AI API.\n     *\n     * @param {string | null | undefined} [opts.apiKey=process.env['TAMBO_API_KEY'] ?? null]\n     * @param {Environment} [opts.environment=production] - Specifies the environment URL to use for the API.\n     * @param {string} [opts.baseURL=process.env['TAMBO_AI_BASE_URL'] ?? https://api.tambo.co] - Override the default base URL for the API.\n     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.\n     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n     * @param {number} [opts.maxRetries=0] - The maximum number of times the client will retry a request.\n     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.\n     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.\n     */\n    constructor({ baseURL = _core_mjs__WEBPACK_IMPORTED_MODULE_0__.readEnv('TAMBO_AI_BASE_URL'), apiKey = _core_mjs__WEBPACK_IMPORTED_MODULE_0__.readEnv('TAMBO_API_KEY') ?? null, ...opts } = {}) {\n        const options = {\n            apiKey,\n            ...opts,\n            baseURL,\n            environment: opts.environment ?? 'production',\n        };\n        if (baseURL && opts.environment) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.TamboAIError('Ambiguous URL; The `baseURL` option (or TAMBO_AI_BASE_URL env var) and the `environment` option are given. If you want to use the environment you must pass baseURL: null');\n        }\n        super({\n            baseURL: options.baseURL || environments[options.environment || 'production'],\n            timeout: options.timeout ?? 60000 /* 1 minute */,\n            httpAgent: options.httpAgent,\n            maxRetries: options.maxRetries,\n            fetch: options.fetch,\n        });\n        this.beta = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__.Beta(this);\n        this._options = options;\n        this.apiKey = apiKey;\n    }\n    defaultQuery() {\n        return this._options.defaultQuery;\n    }\n    defaultHeaders(opts) {\n        return {\n            ...super.defaultHeaders(opts),\n            ...this._options.defaultHeaders,\n        };\n    }\n    validateHeaders(headers, customHeaders) {\n        if (this.apiKey && headers['x-api-key']) {\n            return;\n        }\n        if (customHeaders['x-api-key'] === null) {\n            return;\n        }\n        throw new Error('Could not resolve authentication method. Expected the apiKey to be set. Or for the \"x-api-key\" headers to be explicitly omitted');\n    }\n    authHeaders(opts) {\n        if (this.apiKey == null) {\n            return {};\n        }\n        return { 'x-api-key': this.apiKey };\n    }\n}\n_a = TamboAI;\nTamboAI.TamboAI = _a;\nTamboAI.DEFAULT_TIMEOUT = 60000; // 1 minute\nTamboAI.TamboAIError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.TamboAIError;\nTamboAI.APIError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError;\nTamboAI.APIConnectionError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionError;\nTamboAI.APIConnectionTimeoutError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionTimeoutError;\nTamboAI.APIUserAbortError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIUserAbortError;\nTamboAI.NotFoundError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.NotFoundError;\nTamboAI.ConflictError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.ConflictError;\nTamboAI.RateLimitError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.RateLimitError;\nTamboAI.BadRequestError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.BadRequestError;\nTamboAI.AuthenticationError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.AuthenticationError;\nTamboAI.InternalServerError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.InternalServerError;\nTamboAI.PermissionDeniedError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.PermissionDeniedError;\nTamboAI.UnprocessableEntityError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.UnprocessableEntityError;\nTamboAI.toFile = _uploads_mjs__WEBPACK_IMPORTED_MODULE_3__.toFile;\nTamboAI.fileFromPath = _uploads_mjs__WEBPACK_IMPORTED_MODULE_4__.fileFromPath;\nTamboAI.Beta = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__.Beta;\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TamboAI);\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ21DO0FBQ0c7QUFDUztBQUNOO0FBQ0k7QUFDSTtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLHNCQUFzQixnREFBYztBQUMzQztBQUNBO0FBQ0E7QUFDQSxlQUFlLDJCQUEyQjtBQUMxQyxlQUFlLGFBQWE7QUFDNUIsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QixlQUFlLFFBQVE7QUFDdkIsZUFBZSxZQUFZO0FBQzNCLGVBQWUsUUFBUTtBQUN2QixlQUFlLGNBQWM7QUFDN0IsZUFBZSxtQkFBbUI7QUFDbEM7QUFDQSxrQkFBa0IsVUFBVSw4Q0FBWSxnQ0FBZ0MsOENBQVkscUNBQXFDLElBQUk7QUFDN0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isb0RBQW1CLGlCQUFpQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCx3QkFBd0Isc0RBQVE7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLG9EQUFtQjtBQUMxQyxtQkFBbUIsZ0RBQWU7QUFDbEMsNkJBQTZCLDBEQUF5QjtBQUN0RCxvQ0FBb0MsaUVBQWdDO0FBQ3BFLDRCQUE0Qix5REFBd0I7QUFDcEQsd0JBQXdCLHFEQUFvQjtBQUM1Qyx3QkFBd0IscURBQW9CO0FBQzVDLHlCQUF5QixzREFBcUI7QUFDOUMsMEJBQTBCLHVEQUFzQjtBQUNoRCw4QkFBOEIsMkRBQTBCO0FBQ3hELDhCQUE4QiwyREFBMEI7QUFDeEQsZ0NBQWdDLDZEQUE0QjtBQUM1RCxtQ0FBbUMsZ0VBQStCO0FBQ2xFLGlCQUFpQixnREFBYztBQUMvQix1QkFBdUIsc0RBQW9CO0FBQzNDLGVBQWUsc0RBQUk7QUFDa0M7QUFDNk47QUFDbFIsaUVBQWUsT0FBTyxFQUFDO0FBQ2tDO0FBQ3pEIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9AdGFtYm8tYWkvdHlwZXNjcmlwdC1zZGsvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG52YXIgX2E7XG5pbXBvcnQgKiBhcyBDb3JlIGZyb20gXCIuL2NvcmUubWpzXCI7XG5pbXBvcnQgKiBhcyBFcnJvcnMgZnJvbSBcIi4vZXJyb3IubWpzXCI7XG5pbXBvcnQgKiBhcyBQYWdpbmF0aW9uIGZyb20gXCIuL3BhZ2luYXRpb24ubWpzXCI7XG5pbXBvcnQgKiBhcyBVcGxvYWRzIGZyb20gXCIuL3VwbG9hZHMubWpzXCI7XG5pbXBvcnQgKiBhcyBBUEkgZnJvbSBcIi4vcmVzb3VyY2VzL2luZGV4Lm1qc1wiO1xuaW1wb3J0IHsgQmV0YSB9IGZyb20gXCIuL3Jlc291cmNlcy9iZXRhL2JldGEubWpzXCI7XG5jb25zdCBlbnZpcm9ubWVudHMgPSB7XG4gICAgcHJvZHVjdGlvbjogJ2h0dHBzOi8vYXBpLnRhbWJvLmNvJyxcbiAgICBzdGFnaW5nOiAnaHR0cHM6Ly9oeWRyYS1hcGktZGV2LnVwLnJhaWx3YXkuYXBwJyxcbn07XG4vKipcbiAqIEFQSSBDbGllbnQgZm9yIGludGVyZmFjaW5nIHdpdGggdGhlIFRhbWJvIEFJIEFQSS5cbiAqL1xuZXhwb3J0IGNsYXNzIFRhbWJvQUkgZXh0ZW5kcyBDb3JlLkFQSUNsaWVudCB7XG4gICAgLyoqXG4gICAgICogQVBJIENsaWVudCBmb3IgaW50ZXJmYWNpbmcgd2l0aCB0aGUgVGFtYm8gQUkgQVBJLlxuICAgICAqXG4gICAgICogQHBhcmFtIHtzdHJpbmcgfCBudWxsIHwgdW5kZWZpbmVkfSBbb3B0cy5hcGlLZXk9cHJvY2Vzcy5lbnZbJ1RBTUJPX0FQSV9LRVknXSA/PyBudWxsXVxuICAgICAqIEBwYXJhbSB7RW52aXJvbm1lbnR9IFtvcHRzLmVudmlyb25tZW50PXByb2R1Y3Rpb25dIC0gU3BlY2lmaWVzIHRoZSBlbnZpcm9ubWVudCBVUkwgdG8gdXNlIGZvciB0aGUgQVBJLlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBbb3B0cy5iYXNlVVJMPXByb2Nlc3MuZW52WydUQU1CT19BSV9CQVNFX1VSTCddID8/IGh0dHBzOi8vYXBpLnRhbWJvLmNvXSAtIE92ZXJyaWRlIHRoZSBkZWZhdWx0IGJhc2UgVVJMIGZvciB0aGUgQVBJLlxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBbb3B0cy50aW1lb3V0PTEgbWludXRlXSAtIFRoZSBtYXhpbXVtIGFtb3VudCBvZiB0aW1lIChpbiBtaWxsaXNlY29uZHMpIHRoZSBjbGllbnQgd2lsbCB3YWl0IGZvciBhIHJlc3BvbnNlIGJlZm9yZSB0aW1pbmcgb3V0LlxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBbb3B0cy5odHRwQWdlbnRdIC0gQW4gSFRUUCBhZ2VudCB1c2VkIHRvIG1hbmFnZSBIVFRQKHMpIGNvbm5lY3Rpb25zLlxuICAgICAqIEBwYXJhbSB7Q29yZS5GZXRjaH0gW29wdHMuZmV0Y2hdIC0gU3BlY2lmeSBhIGN1c3RvbSBgZmV0Y2hgIGZ1bmN0aW9uIGltcGxlbWVudGF0aW9uLlxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSBbb3B0cy5tYXhSZXRyaWVzPTBdIC0gVGhlIG1heGltdW0gbnVtYmVyIG9mIHRpbWVzIHRoZSBjbGllbnQgd2lsbCByZXRyeSBhIHJlcXVlc3QuXG4gICAgICogQHBhcmFtIHtDb3JlLkhlYWRlcnN9IG9wdHMuZGVmYXVsdEhlYWRlcnMgLSBEZWZhdWx0IGhlYWRlcnMgdG8gaW5jbHVkZSB3aXRoIGV2ZXJ5IHJlcXVlc3QgdG8gdGhlIEFQSS5cbiAgICAgKiBAcGFyYW0ge0NvcmUuRGVmYXVsdFF1ZXJ5fSBvcHRzLmRlZmF1bHRRdWVyeSAtIERlZmF1bHQgcXVlcnkgcGFyYW1ldGVycyB0byBpbmNsdWRlIHdpdGggZXZlcnkgcmVxdWVzdCB0byB0aGUgQVBJLlxuICAgICAqL1xuICAgIGNvbnN0cnVjdG9yKHsgYmFzZVVSTCA9IENvcmUucmVhZEVudignVEFNQk9fQUlfQkFTRV9VUkwnKSwgYXBpS2V5ID0gQ29yZS5yZWFkRW52KCdUQU1CT19BUElfS0VZJykgPz8gbnVsbCwgLi4ub3B0cyB9ID0ge30pIHtcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcbiAgICAgICAgICAgIGFwaUtleSxcbiAgICAgICAgICAgIC4uLm9wdHMsXG4gICAgICAgICAgICBiYXNlVVJMLFxuICAgICAgICAgICAgZW52aXJvbm1lbnQ6IG9wdHMuZW52aXJvbm1lbnQgPz8gJ3Byb2R1Y3Rpb24nLFxuICAgICAgICB9O1xuICAgICAgICBpZiAoYmFzZVVSTCAmJiBvcHRzLmVudmlyb25tZW50KSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3JzLlRhbWJvQUlFcnJvcignQW1iaWd1b3VzIFVSTDsgVGhlIGBiYXNlVVJMYCBvcHRpb24gKG9yIFRBTUJPX0FJX0JBU0VfVVJMIGVudiB2YXIpIGFuZCB0aGUgYGVudmlyb25tZW50YCBvcHRpb24gYXJlIGdpdmVuLiBJZiB5b3Ugd2FudCB0byB1c2UgdGhlIGVudmlyb25tZW50IHlvdSBtdXN0IHBhc3MgYmFzZVVSTDogbnVsbCcpO1xuICAgICAgICB9XG4gICAgICAgIHN1cGVyKHtcbiAgICAgICAgICAgIGJhc2VVUkw6IG9wdGlvbnMuYmFzZVVSTCB8fCBlbnZpcm9ubWVudHNbb3B0aW9ucy5lbnZpcm9ubWVudCB8fCAncHJvZHVjdGlvbiddLFxuICAgICAgICAgICAgdGltZW91dDogb3B0aW9ucy50aW1lb3V0ID8/IDYwMDAwIC8qIDEgbWludXRlICovLFxuICAgICAgICAgICAgaHR0cEFnZW50OiBvcHRpb25zLmh0dHBBZ2VudCxcbiAgICAgICAgICAgIG1heFJldHJpZXM6IG9wdGlvbnMubWF4UmV0cmllcyxcbiAgICAgICAgICAgIGZldGNoOiBvcHRpb25zLmZldGNoLFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5iZXRhID0gbmV3IEFQSS5CZXRhKHRoaXMpO1xuICAgICAgICB0aGlzLl9vcHRpb25zID0gb3B0aW9ucztcbiAgICAgICAgdGhpcy5hcGlLZXkgPSBhcGlLZXk7XG4gICAgfVxuICAgIGRlZmF1bHRRdWVyeSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX29wdGlvbnMuZGVmYXVsdFF1ZXJ5O1xuICAgIH1cbiAgICBkZWZhdWx0SGVhZGVycyhvcHRzKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdXBlci5kZWZhdWx0SGVhZGVycyhvcHRzKSxcbiAgICAgICAgICAgIC4uLnRoaXMuX29wdGlvbnMuZGVmYXVsdEhlYWRlcnMsXG4gICAgICAgIH07XG4gICAgfVxuICAgIHZhbGlkYXRlSGVhZGVycyhoZWFkZXJzLCBjdXN0b21IZWFkZXJzKSB7XG4gICAgICAgIGlmICh0aGlzLmFwaUtleSAmJiBoZWFkZXJzWyd4LWFwaS1rZXknXSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmIChjdXN0b21IZWFkZXJzWyd4LWFwaS1rZXknXSA9PT0gbnVsbCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQ291bGQgbm90IHJlc29sdmUgYXV0aGVudGljYXRpb24gbWV0aG9kLiBFeHBlY3RlZCB0aGUgYXBpS2V5IHRvIGJlIHNldC4gT3IgZm9yIHRoZSBcIngtYXBpLWtleVwiIGhlYWRlcnMgdG8gYmUgZXhwbGljaXRseSBvbWl0dGVkJyk7XG4gICAgfVxuICAgIGF1dGhIZWFkZXJzKG9wdHMpIHtcbiAgICAgICAgaWYgKHRoaXMuYXBpS2V5ID09IG51bGwpIHtcbiAgICAgICAgICAgIHJldHVybiB7fTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4geyAneC1hcGkta2V5JzogdGhpcy5hcGlLZXkgfTtcbiAgICB9XG59XG5fYSA9IFRhbWJvQUk7XG5UYW1ib0FJLlRhbWJvQUkgPSBfYTtcblRhbWJvQUkuREVGQVVMVF9USU1FT1VUID0gNjAwMDA7IC8vIDEgbWludXRlXG5UYW1ib0FJLlRhbWJvQUlFcnJvciA9IEVycm9ycy5UYW1ib0FJRXJyb3I7XG5UYW1ib0FJLkFQSUVycm9yID0gRXJyb3JzLkFQSUVycm9yO1xuVGFtYm9BSS5BUElDb25uZWN0aW9uRXJyb3IgPSBFcnJvcnMuQVBJQ29ubmVjdGlvbkVycm9yO1xuVGFtYm9BSS5BUElDb25uZWN0aW9uVGltZW91dEVycm9yID0gRXJyb3JzLkFQSUNvbm5lY3Rpb25UaW1lb3V0RXJyb3I7XG5UYW1ib0FJLkFQSVVzZXJBYm9ydEVycm9yID0gRXJyb3JzLkFQSVVzZXJBYm9ydEVycm9yO1xuVGFtYm9BSS5Ob3RGb3VuZEVycm9yID0gRXJyb3JzLk5vdEZvdW5kRXJyb3I7XG5UYW1ib0FJLkNvbmZsaWN0RXJyb3IgPSBFcnJvcnMuQ29uZmxpY3RFcnJvcjtcblRhbWJvQUkuUmF0ZUxpbWl0RXJyb3IgPSBFcnJvcnMuUmF0ZUxpbWl0RXJyb3I7XG5UYW1ib0FJLkJhZFJlcXVlc3RFcnJvciA9IEVycm9ycy5CYWRSZXF1ZXN0RXJyb3I7XG5UYW1ib0FJLkF1dGhlbnRpY2F0aW9uRXJyb3IgPSBFcnJvcnMuQXV0aGVudGljYXRpb25FcnJvcjtcblRhbWJvQUkuSW50ZXJuYWxTZXJ2ZXJFcnJvciA9IEVycm9ycy5JbnRlcm5hbFNlcnZlckVycm9yO1xuVGFtYm9BSS5QZXJtaXNzaW9uRGVuaWVkRXJyb3IgPSBFcnJvcnMuUGVybWlzc2lvbkRlbmllZEVycm9yO1xuVGFtYm9BSS5VbnByb2Nlc3NhYmxlRW50aXR5RXJyb3IgPSBFcnJvcnMuVW5wcm9jZXNzYWJsZUVudGl0eUVycm9yO1xuVGFtYm9BSS50b0ZpbGUgPSBVcGxvYWRzLnRvRmlsZTtcblRhbWJvQUkuZmlsZUZyb21QYXRoID0gVXBsb2Fkcy5maWxlRnJvbVBhdGg7XG5UYW1ib0FJLkJldGEgPSBCZXRhO1xuZXhwb3J0IHsgdG9GaWxlLCBmaWxlRnJvbVBhdGggfSBmcm9tIFwiLi91cGxvYWRzLm1qc1wiO1xuZXhwb3J0IHsgVGFtYm9BSUVycm9yLCBBUElFcnJvciwgQVBJQ29ubmVjdGlvbkVycm9yLCBBUElDb25uZWN0aW9uVGltZW91dEVycm9yLCBBUElVc2VyQWJvcnRFcnJvciwgTm90Rm91bmRFcnJvciwgQ29uZmxpY3RFcnJvciwgUmF0ZUxpbWl0RXJyb3IsIEJhZFJlcXVlc3RFcnJvciwgQXV0aGVudGljYXRpb25FcnJvciwgSW50ZXJuYWxTZXJ2ZXJFcnJvciwgUGVybWlzc2lvbkRlbmllZEVycm9yLCBVbnByb2Nlc3NhYmxlRW50aXR5RXJyb3IsIH0gZnJvbSBcIi4vZXJyb3IubWpzXCI7XG5leHBvcnQgZGVmYXVsdCBUYW1ib0FJO1xuZXhwb3J0IHsgYWR2YW5jZVN0cmVhbSB9IGZyb20gXCIuL2xpYi9hZHZhbmNlLXN0cmVhbS5tanNcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/lib/advance-stream.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/lib/advance-stream.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   advanceStream: () => (/* binding */ advanceStream)\n/* harmony export */ });\nasync function advanceStream(client, body, threadId, options) {\n    const MAX_REQUEST_RETRIES = 2;\n    let requestRetryCount = 0;\n    while (true) {\n        try {\n            const responsePromise = client.post(`/threads${threadId ? `/${threadId}` : ''}/advancestream`, {\n                body,\n                ...options,\n                headers: {\n                    ...options?.headers,\n                    Accept: 'text/event-stream',\n                },\n            });\n            const response = await responsePromise.asResponse();\n            return handleStreamResponse(response);\n        }\n        catch (error) {\n            if (requestRetryCount < MAX_REQUEST_RETRIES) {\n                requestRetryCount++;\n                console.warn(`Request failed, attempting retry ${requestRetryCount}/${MAX_REQUEST_RETRIES}`);\n                continue;\n            }\n            throw error;\n        }\n    }\n}\nasync function* handleStreamResponse(response) {\n    const decoder = new TextDecoder();\n    const MAX_CHUNK_RETRIES = 5;\n    let chunkRetryCount = 0;\n    const reader = response.body.getReader();\n    while (true) {\n        try {\n            const { done, value: chunk } = await reader.read();\n            if (done)\n                break;\n            const text = decoder.decode(chunk);\n            const messages = text.split('\\n').filter((msg) => msg.trim());\n            for (const msg of messages) {\n                if (msg === 'data: DONE') {\n                    continue;\n                }\n                if (msg.startsWith('error: ')) {\n                    throw new Error(msg.slice(7));\n                }\n                const jsonStr = msg.startsWith('data: ') ? msg.slice(6) : msg;\n                if (!jsonStr) {\n                    continue;\n                }\n                try {\n                    yield JSON.parse(jsonStr);\n                    chunkRetryCount = 0;\n                }\n                catch (e) {\n                    if (chunkRetryCount < MAX_CHUNK_RETRIES) {\n                        chunkRetryCount++;\n                        console.warn(`Failed to parse JSON chunk, skipping. Attempt ${chunkRetryCount}/${MAX_CHUNK_RETRIES}`);\n                        continue;\n                    }\n                    throw new Error('Failed to parse JSON after multiple chunks.');\n                }\n            }\n        }\n        catch (error) {\n            reader.releaseLock();\n            throw error;\n        }\n    }\n}\n//# sourceMappingURL=advance-stream.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/lib/advance-stream.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/pagination.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/pagination.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OffsetAndLimit: () => (/* binding */ OffsetAndLimit)\n/* harmony export */ });\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/core.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass OffsetAndLimit extends _core_mjs__WEBPACK_IMPORTED_MODULE_0__.AbstractPage {\n    constructor(client, response, body, options) {\n        super(client, response, body, options);\n        this.items = body.items || [];\n        this.total = body.total || 0;\n        this.count = body.count || 0;\n    }\n    getPaginatedItems() {\n        return this.items ?? [];\n    }\n    // @deprecated Please use `nextPageInfo()` instead\n    nextPageParams() {\n        const info = this.nextPageInfo();\n        if (!info)\n            return null;\n        if ('params' in info)\n            return info.params;\n        const params = Object.fromEntries(info.url.searchParams);\n        if (!Object.keys(params).length)\n            return null;\n        return params;\n    }\n    nextPageInfo() {\n        const offset = this.options.query.offset ?? 0;\n        if (!offset) {\n            return null;\n        }\n        const length = this.getPaginatedItems().length;\n        const currentCount = offset + length;\n        const totalCount = this.total;\n        if (!totalCount) {\n            return null;\n        }\n        if (currentCount < totalCount) {\n            return { params: { offset: currentCount } };\n        }\n        return null;\n    }\n}\n//# sourceMappingURL=pagination.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/pagination.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/resource.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIResource: () => (/* binding */ APIResource)\n/* harmony export */ });\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nclass APIResource {\n    constructor(client) {\n        this._client = client;\n    }\n}\n//# sourceMappingURL=resource.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL3Jlc291cmNlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B0YW1iby1haS90eXBlc2NyaXB0LXNkay9yZXNvdXJjZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmV4cG9ydCBjbGFzcyBBUElSZXNvdXJjZSB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuX2NsaWVudCA9IGNsaWVudDtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZXNvdXJjZS5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/beta.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/resources/beta/beta.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Beta: () => (/* binding */ Beta)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs\");\n/* harmony import */ var _registry_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./registry.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/registry.mjs\");\n/* harmony import */ var _projects_projects_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./projects/projects.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/projects.mjs\");\n/* harmony import */ var _threads_threads_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./threads/threads.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/threads.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\n\n\n\n\n\nclass Beta extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    constructor() {\n        super(...arguments);\n        this.registry = new _registry_mjs__WEBPACK_IMPORTED_MODULE_1__.Registry(this._client);\n        this.projects = new _projects_projects_mjs__WEBPACK_IMPORTED_MODULE_2__.Projects(this._client);\n        this.threads = new _threads_threads_mjs__WEBPACK_IMPORTED_MODULE_3__.Threads(this._client);\n    }\n}\nBeta.Registry = _registry_mjs__WEBPACK_IMPORTED_MODULE_1__.Registry;\nBeta.Projects = _projects_projects_mjs__WEBPACK_IMPORTED_MODULE_2__.Projects;\nBeta.Threads = _threads_threads_mjs__WEBPACK_IMPORTED_MODULE_3__.Threads;\nBeta.ThreadsOffsetAndLimit = _threads_threads_mjs__WEBPACK_IMPORTED_MODULE_3__.ThreadsOffsetAndLimit;\n//# sourceMappingURL=beta.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL3Jlc291cmNlcy9iZXRhL2JldGEubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDaUQ7QUFDSDtBQUNKO0FBQ2E7QUFDSDtBQUNBO0FBQ29CO0FBQ2pFLG1CQUFtQixzREFBVztBQUNyQztBQUNBO0FBQ0EsNEJBQTRCLG1EQUFvQjtBQUNoRCw0QkFBNEIsNERBQW9CO0FBQ2hELDJCQUEyQix5REFBa0I7QUFDN0M7QUFDQTtBQUNBLGdCQUFnQixtREFBUTtBQUN4QixnQkFBZ0IsNERBQVE7QUFDeEIsZUFBZSx5REFBTztBQUN0Qiw2QkFBNkIsdUVBQXFCO0FBQ2xEIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9AdGFtYm8tYWkvdHlwZXNjcmlwdC1zZGsvcmVzb3VyY2VzL2JldGEvYmV0YS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuaW1wb3J0ICogYXMgUmVnaXN0cnlBUEkgZnJvbSBcIi4vcmVnaXN0cnkubWpzXCI7XG5pbXBvcnQgeyBSZWdpc3RyeSB9IGZyb20gXCIuL3JlZ2lzdHJ5Lm1qc1wiO1xuaW1wb3J0ICogYXMgUHJvamVjdHNBUEkgZnJvbSBcIi4vcHJvamVjdHMvcHJvamVjdHMubWpzXCI7XG5pbXBvcnQgeyBQcm9qZWN0cywgfSBmcm9tIFwiLi9wcm9qZWN0cy9wcm9qZWN0cy5tanNcIjtcbmltcG9ydCAqIGFzIFRocmVhZHNBUEkgZnJvbSBcIi4vdGhyZWFkcy90aHJlYWRzLm1qc1wiO1xuaW1wb3J0IHsgVGhyZWFkcywgVGhyZWFkc09mZnNldEFuZExpbWl0LCB9IGZyb20gXCIuL3RocmVhZHMvdGhyZWFkcy5tanNcIjtcbmV4cG9ydCBjbGFzcyBCZXRhIGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xuICAgICAgICB0aGlzLnJlZ2lzdHJ5ID0gbmV3IFJlZ2lzdHJ5QVBJLlJlZ2lzdHJ5KHRoaXMuX2NsaWVudCk7XG4gICAgICAgIHRoaXMucHJvamVjdHMgPSBuZXcgUHJvamVjdHNBUEkuUHJvamVjdHModGhpcy5fY2xpZW50KTtcbiAgICAgICAgdGhpcy50aHJlYWRzID0gbmV3IFRocmVhZHNBUEkuVGhyZWFkcyh0aGlzLl9jbGllbnQpO1xuICAgIH1cbn1cbkJldGEuUmVnaXN0cnkgPSBSZWdpc3RyeTtcbkJldGEuUHJvamVjdHMgPSBQcm9qZWN0cztcbkJldGEuVGhyZWFkcyA9IFRocmVhZHM7XG5CZXRhLlRocmVhZHNPZmZzZXRBbmRMaW1pdCA9IFRocmVhZHNPZmZzZXRBbmRMaW1pdDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJldGEubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/beta.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/api-key.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/api-key.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIKey: () => (/* binding */ APIKey)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../resource.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass APIKey extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * @example\n     * ```ts\n     * const apiKeys = await client.beta.projects.apiKey.list(\n     *   'id',\n     * );\n     * ```\n     */\n    list(id, options) {\n        return this._client.get(`/projects/${id}/api-keys`, options);\n    }\n    /**\n     * @example\n     * ```ts\n     * const apiKey = await client.beta.projects.apiKey.delete(\n     *   'id',\n     *   'apiKeyId',\n     * );\n     * ```\n     */\n    delete(id, apiKeyId, options) {\n        return this._client.delete(`/projects/${id}/api-key/${apiKeyId}`, options);\n    }\n}\n//# sourceMappingURL=api-key.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL3Jlc291cmNlcy9iZXRhL3Byb2plY3RzL2FwaS1rZXkubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDb0Q7QUFDN0MscUJBQXFCLHNEQUFXO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxHQUFHO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsR0FBRyxXQUFXLFNBQVM7QUFDdkU7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYWFyb24vRG93bmxvYWRzL21jcC14NDAyL25vZGVfbW9kdWxlcy9AdGFtYm8tYWkvdHlwZXNjcmlwdC1zZGsvcmVzb3VyY2VzL2JldGEvcHJvamVjdHMvYXBpLWtleS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uLy4uL3Jlc291cmNlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIEFQSUtleSBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICAvKipcbiAgICAgKiBAZXhhbXBsZVxuICAgICAqIGBgYHRzXG4gICAgICogY29uc3QgYXBpS2V5cyA9IGF3YWl0IGNsaWVudC5iZXRhLnByb2plY3RzLmFwaUtleS5saXN0KFxuICAgICAqICAgJ2lkJyxcbiAgICAgKiApO1xuICAgICAqIGBgYFxuICAgICAqL1xuICAgIGxpc3QoaWQsIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5nZXQoYC9wcm9qZWN0cy8ke2lkfS9hcGkta2V5c2AsIG9wdGlvbnMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBAZXhhbXBsZVxuICAgICAqIGBgYHRzXG4gICAgICogY29uc3QgYXBpS2V5ID0gYXdhaXQgY2xpZW50LmJldGEucHJvamVjdHMuYXBpS2V5LmRlbGV0ZShcbiAgICAgKiAgICdpZCcsXG4gICAgICogICAnYXBpS2V5SWQnLFxuICAgICAqICk7XG4gICAgICogYGBgXG4gICAgICovXG4gICAgZGVsZXRlKGlkLCBhcGlLZXlJZCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmRlbGV0ZShgL3Byb2plY3RzLyR7aWR9L2FwaS1rZXkvJHthcGlLZXlJZH1gLCBvcHRpb25zKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcGkta2V5Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/api-key.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/projects.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/projects.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Projects: () => (/* binding */ Projects)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../resource.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs\");\n/* harmony import */ var _api_key_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api-key.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/api-key.mjs\");\n/* harmony import */ var _provider_key_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./provider-key.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/provider-key.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\n\n\n\nclass Projects extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    constructor() {\n        super(...arguments);\n        this.apiKey = new _api_key_mjs__WEBPACK_IMPORTED_MODULE_1__.APIKey(this._client);\n        this.providerKey = new _provider_key_mjs__WEBPACK_IMPORTED_MODULE_2__.ProviderKey(this._client);\n    }\n    /**\n     * @example\n     * ```ts\n     * const project = await client.beta.projects.retrieve('id');\n     * ```\n     */\n    retrieve(id, options) {\n        return this._client.get(`/projects/${id}`, options);\n    }\n    /**\n     * @example\n     * ```ts\n     * const project = await client.beta.projects.delete('id');\n     * ```\n     */\n    delete(id, options) {\n        return this._client.delete(`/projects/${id}`, options);\n    }\n    /**\n     * @example\n     * ```ts\n     * const response = await client.beta.projects.getCurrent();\n     * ```\n     */\n    getCurrent(options) {\n        return this._client.get('/projects', options);\n    }\n}\nProjects.APIKey = _api_key_mjs__WEBPACK_IMPORTED_MODULE_1__.APIKey;\nProjects.ProviderKey = _provider_key_mjs__WEBPACK_IMPORTED_MODULE_2__.ProviderKey;\n//# sourceMappingURL=projects.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/projects.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/provider-key.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/provider-key.mjs ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProviderKey: () => (/* binding */ ProviderKey)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../resource.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass ProviderKey extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * @example\n     * ```ts\n     * const providerKeys =\n     *   await client.beta.projects.providerKey.list('id');\n     * ```\n     */\n    list(id, options) {\n        return this._client.get(`/projects/${id}/provider-keys`, options);\n    }\n    /**\n     * @example\n     * ```ts\n     * const providerKey =\n     *   await client.beta.projects.providerKey.delete(\n     *     'id',\n     *     'providerKeyId',\n     *   );\n     * ```\n     */\n    delete(id, providerKeyId, options) {\n        return this._client.delete(`/projects/${id}/provider-key/${providerKeyId}`, options);\n    }\n}\n//# sourceMappingURL=provider-key.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL3Jlc291cmNlcy9iZXRhL3Byb2plY3RzL3Byb3ZpZGVyLWtleS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNvRDtBQUM3QywwQkFBMEIsc0RBQVc7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QyxHQUFHO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRCxHQUFHLGdCQUFnQixjQUFjO0FBQ2pGO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2Fhcm9uL0Rvd25sb2Fkcy9tY3AteDQwMi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL3Jlc291cmNlcy9iZXRhL3Byb2plY3RzL3Byb3ZpZGVyLWtleS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uLy4uL3Jlc291cmNlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIFByb3ZpZGVyS2V5IGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIC8qKlxuICAgICAqIEBleGFtcGxlXG4gICAgICogYGBgdHNcbiAgICAgKiBjb25zdCBwcm92aWRlcktleXMgPVxuICAgICAqICAgYXdhaXQgY2xpZW50LmJldGEucHJvamVjdHMucHJvdmlkZXJLZXkubGlzdCgnaWQnKTtcbiAgICAgKiBgYGBcbiAgICAgKi9cbiAgICBsaXN0KGlkLCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQuZ2V0KGAvcHJvamVjdHMvJHtpZH0vcHJvdmlkZXIta2V5c2AsIG9wdGlvbnMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBAZXhhbXBsZVxuICAgICAqIGBgYHRzXG4gICAgICogY29uc3QgcHJvdmlkZXJLZXkgPVxuICAgICAqICAgYXdhaXQgY2xpZW50LmJldGEucHJvamVjdHMucHJvdmlkZXJLZXkuZGVsZXRlKFxuICAgICAqICAgICAnaWQnLFxuICAgICAqICAgICAncHJvdmlkZXJLZXlJZCcsXG4gICAgICogICApO1xuICAgICAqIGBgYFxuICAgICAqL1xuICAgIGRlbGV0ZShpZCwgcHJvdmlkZXJLZXlJZCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmRlbGV0ZShgL3Byb2plY3RzLyR7aWR9L3Byb3ZpZGVyLWtleS8ke3Byb3ZpZGVyS2V5SWR9YCwgb3B0aW9ucyk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHJvdmlkZXIta2V5Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/projects/provider-key.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/registry.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/resources/beta/registry.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Registry: () => (/* binding */ Registry)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Registry extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * @example\n     * ```ts\n     * const registry = await client.beta.registry.retrieve(\n     *   'componentname',\n     * );\n     * ```\n     */\n    retrieve(componentname, options) {\n        return this._client.get(`/registry/${componentname}`, options);\n    }\n}\n//# sourceMappingURL=registry.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL3Jlc291cmNlcy9iZXRhL3JlZ2lzdHJ5Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ2lEO0FBQzFDLHVCQUF1QixzREFBVztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsY0FBYztBQUMzRDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B0YW1iby1haS90eXBlc2NyaXB0LXNkay9yZXNvdXJjZXMvYmV0YS9yZWdpc3RyeS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIFJlZ2lzdHJ5IGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIC8qKlxuICAgICAqIEBleGFtcGxlXG4gICAgICogYGBgdHNcbiAgICAgKiBjb25zdCByZWdpc3RyeSA9IGF3YWl0IGNsaWVudC5iZXRhLnJlZ2lzdHJ5LnJldHJpZXZlKFxuICAgICAqICAgJ2NvbXBvbmVudG5hbWUnLFxuICAgICAqICk7XG4gICAgICogYGBgXG4gICAgICovXG4gICAgcmV0cmlldmUoY29tcG9uZW50bmFtZSwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmdldChgL3JlZ2lzdHJ5LyR7Y29tcG9uZW50bmFtZX1gLCBvcHRpb25zKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWdpc3RyeS5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/registry.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/messages.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/messages.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Messages: () => (/* binding */ Messages)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../resource.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../core.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/core.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Messages extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * @example\n     * ```ts\n     * const message = await client.beta.threads.messages.create(\n     *   'id',\n     *   { content: [{ type: 'text' }], role: 'user' },\n     * );\n     * ```\n     */\n    create(id, body, options) {\n        return this._client.post(`/threads/${id}/messages`, { body, ...options });\n    }\n    list(id, query = {}, options) {\n        if ((0,_core_mjs__WEBPACK_IMPORTED_MODULE_1__.isRequestOptions)(query)) {\n            return this.list(id, {}, query);\n        }\n        return this._client.get(`/threads/${id}/messages`, { query, ...options });\n    }\n    /**\n     * @example\n     * ```ts\n     * await client.beta.threads.messages.delete(\n     *   'id',\n     *   'messageId',\n     * );\n     * ```\n     */\n    delete(id, messageId, options) {\n        return this._client.delete(`/threads/${id}/messages/${messageId}`, {\n            ...options,\n            headers: { Accept: '*/*', ...options?.headers },\n        });\n    }\n    /**\n     * @example\n     * ```ts\n     * const threadMessage =\n     *   await client.beta.threads.messages.updateComponentState(\n     *     'id',\n     *     'messageId',\n     *     { state: { foo: 'bar' } },\n     *   );\n     * ```\n     */\n    updateComponentState(id, messageId, body, options) {\n        return this._client.put(`/threads/${id}/messages/${messageId}/component-state`, { body, ...options });\n    }\n}\n//# sourceMappingURL=messages.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/messages.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/suggestions.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/suggestions.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Suggestions: () => (/* binding */ Suggestions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../resource.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Suggestions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Retrieves all suggestions generated for a specific message\n     *\n     * @example\n     * ```ts\n     * const suggestions =\n     *   await client.beta.threads.suggestions.list(\n     *     'thread_123456789',\n     *     'msg_123456789',\n     *   );\n     * ```\n     */\n    list(id, messageId, options) {\n        return this._client.get(`/threads/${id}/messages/${messageId}/suggestions`, options);\n    }\n    /**\n     * Generates and stores new suggestions for a specific message\n     *\n     * @example\n     * ```ts\n     * const suggestions =\n     *   await client.beta.threads.suggestions.generate(\n     *     'thread_123456789',\n     *     'msg_123456789',\n     *   );\n     * ```\n     */\n    generate(id, messageId, body, options) {\n        return this._client.post(`/threads/${id}/messages/${messageId}/suggestions`, { body, ...options });\n    }\n}\n//# sourceMappingURL=suggestions.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/suggestions.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/threads.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/threads.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Threads: () => (/* binding */ Threads),\n/* harmony export */   ThreadsOffsetAndLimit: () => (/* binding */ ThreadsOffsetAndLimit)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../resource.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../core.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/core.mjs\");\n/* harmony import */ var _messages_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./messages.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/messages.mjs\");\n/* harmony import */ var _suggestions_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./suggestions.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/suggestions.mjs\");\n/* harmony import */ var _pagination_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../pagination.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/pagination.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\n\n\n\n\n\nclass Threads extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    constructor() {\n        super(...arguments);\n        this.messages = new _messages_mjs__WEBPACK_IMPORTED_MODULE_1__.Messages(this._client);\n        this.suggestions = new _suggestions_mjs__WEBPACK_IMPORTED_MODULE_2__.Suggestions(this._client);\n    }\n    /**\n     * @example\n     * ```ts\n     * const thread = await client.beta.threads.create({\n     *   projectId: 'projectId',\n     * });\n     * ```\n     */\n    create(body, options) {\n        return this._client.post('/threads', { body, ...options });\n    }\n    /**\n     * @example\n     * ```ts\n     * const thread = await client.beta.threads.retrieve('id');\n     * ```\n     */\n    retrieve(id, options) {\n        return this._client.get(`/threads/${id}`, options);\n    }\n    /**\n     * @example\n     * ```ts\n     * const thread = await client.beta.threads.update('id', {\n     *   projectId: 'projectId',\n     * });\n     * ```\n     */\n    update(id, body, options) {\n        return this._client.put(`/threads/${id}`, { body, ...options });\n    }\n    list(projectId, query = {}, options) {\n        if ((0,_core_mjs__WEBPACK_IMPORTED_MODULE_3__.isRequestOptions)(query)) {\n            return this.list(projectId, {}, query);\n        }\n        return this._client.getAPIList(`/threads/project/${projectId}`, ThreadsOffsetAndLimit, {\n            query,\n            ...options,\n        });\n    }\n    /**\n     * @example\n     * ```ts\n     * await client.beta.threads.delete('id');\n     * ```\n     */\n    delete(id, options) {\n        return this._client.delete(`/threads/${id}`, {\n            ...options,\n            headers: { Accept: '*/*', ...options?.headers },\n        });\n    }\n    /**\n     * @example\n     * ```ts\n     * const response = await client.beta.threads.advance({\n     *   messageToAppend: {\n     *     content: [{ type: 'text' }],\n     *     role: 'user',\n     *   },\n     * });\n     * ```\n     */\n    advance(body, options) {\n        return this._client.post('/threads/advance', { body, ...options });\n    }\n    /**\n     * @example\n     * ```ts\n     * const response = await client.beta.threads.advanceById(\n     *   'id',\n     *   {\n     *     messageToAppend: {\n     *       content: [{ type: 'text' }],\n     *       role: 'user',\n     *     },\n     *   },\n     * );\n     * ```\n     */\n    advanceById(id, body, options) {\n        return this._client.post(`/threads/${id}/advance`, { body, ...options });\n    }\n}\nclass ThreadsOffsetAndLimit extends _pagination_mjs__WEBPACK_IMPORTED_MODULE_4__.OffsetAndLimit {\n}\nThreads.ThreadsOffsetAndLimit = ThreadsOffsetAndLimit;\nThreads.Messages = _messages_mjs__WEBPACK_IMPORTED_MODULE_1__.Messages;\nThreads.Suggestions = _suggestions_mjs__WEBPACK_IMPORTED_MODULE_2__.Suggestions;\n//# sourceMappingURL=threads.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/resources/beta/threads/threads.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/uploads.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/uploads.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createForm: () => (/* binding */ createForm),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFromPath),\n/* harmony export */   isBlobLike: () => (/* binding */ isBlobLike),\n/* harmony export */   isFileLike: () => (/* binding */ isFileLike),\n/* harmony export */   isMultipartBody: () => (/* binding */ isMultipartBody),\n/* harmony export */   isResponseLike: () => (/* binding */ isResponseLike),\n/* harmony export */   isUploadable: () => (/* binding */ isUploadable),\n/* harmony export */   maybeMultipartFormRequestOptions: () => (/* binding */ maybeMultipartFormRequestOptions),\n/* harmony export */   multipartFormRequestOptions: () => (/* binding */ multipartFormRequestOptions),\n/* harmony export */   toFile: () => (/* binding */ toFile)\n/* harmony export */ });\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_shims/index.mjs */ \"(ssr)/./node_modules/@tambo-ai/typescript-sdk/_shims/index.mjs\");\n\n\nconst isResponseLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.url === 'string' &&\n    typeof value.blob === 'function';\nconst isFileLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.name === 'string' &&\n    typeof value.lastModified === 'number' &&\n    isBlobLike(value);\n/**\n * The BlobLike type omits arrayBuffer() because @types/node-fetch@^2.6.4 lacks it; but this check\n * adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isBlobLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.size === 'number' &&\n    typeof value.type === 'string' &&\n    typeof value.text === 'function' &&\n    typeof value.slice === 'function' &&\n    typeof value.arrayBuffer === 'function';\nconst isUploadable = (value) => {\n    return isFileLike(value) || isResponseLike(value) || (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.isFsReadStream)(value);\n};\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */\nasync function toFile(value, name, options) {\n    // If it's a promise, resolve it.\n    value = await value;\n    // If we've been given a `File` we don't need to do anything\n    if (isFileLike(value)) {\n        return value;\n    }\n    if (isResponseLike(value)) {\n        const blob = await value.blob();\n        name || (name = new URL(value.url).pathname.split(/[\\\\/]/).pop() ?? 'unknown_file');\n        // we need to convert the `Blob` into an array buffer because the `Blob` class\n        // that `node-fetch` defines is incompatible with the web standard which results\n        // in `new File` interpreting it as a string instead of binary data.\n        const data = isBlobLike(blob) ? [(await blob.arrayBuffer())] : [blob];\n        return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.File(data, name, options);\n    }\n    const bits = await getBytes(value);\n    name || (name = getName(value) ?? 'unknown_file');\n    if (!options?.type) {\n        const type = bits[0]?.type;\n        if (typeof type === 'string') {\n            options = { ...options, type };\n        }\n    }\n    return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.File(bits, name, options);\n}\nasync function getBytes(value) {\n    let parts = [];\n    if (typeof value === 'string' ||\n        ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n        value instanceof ArrayBuffer) {\n        parts.push(value);\n    }\n    else if (isBlobLike(value)) {\n        parts.push(await value.arrayBuffer());\n    }\n    else if (isAsyncIterableIterator(value) // includes Readable, ReadableStream, etc.\n    ) {\n        for await (const chunk of value) {\n            parts.push(chunk); // TODO, consider validating?\n        }\n    }\n    else {\n        throw new Error(`Unexpected data type: ${typeof value}; constructor: ${value?.constructor\n            ?.name}; props: ${propsForError(value)}`);\n    }\n    return parts;\n}\nfunction propsForError(value) {\n    const props = Object.getOwnPropertyNames(value);\n    return `[${props.map((p) => `\"${p}\"`).join(', ')}]`;\n}\nfunction getName(value) {\n    return (getStringFromMaybeBuffer(value.name) ||\n        getStringFromMaybeBuffer(value.filename) ||\n        // For fs.ReadStream\n        getStringFromMaybeBuffer(value.path)?.split(/[\\\\/]/).pop());\n}\nconst getStringFromMaybeBuffer = (x) => {\n    if (typeof x === 'string')\n        return x;\n    if (typeof Buffer !== 'undefined' && x instanceof Buffer)\n        return String(x);\n    return undefined;\n};\nconst isAsyncIterableIterator = (value) => value != null && typeof value === 'object' && typeof value[Symbol.asyncIterator] === 'function';\nconst isMultipartBody = (body) => body && typeof body === 'object' && body.body && body[Symbol.toStringTag] === 'MultipartBody';\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */\nconst maybeMultipartFormRequestOptions = async (opts) => {\n    if (!hasUploadableValue(opts.body))\n        return opts;\n    const form = await createForm(opts.body);\n    return (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions)(form, opts);\n};\nconst multipartFormRequestOptions = async (opts) => {\n    const form = await createForm(opts.body);\n    return (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions)(form, opts);\n};\nconst createForm = async (body) => {\n    const form = new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FormData();\n    await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));\n    return form;\n};\nconst hasUploadableValue = (value) => {\n    if (isUploadable(value))\n        return true;\n    if (Array.isArray(value))\n        return value.some(hasUploadableValue);\n    if (value && typeof value === 'object') {\n        for (const k in value) {\n            if (hasUploadableValue(value[k]))\n                return true;\n        }\n    }\n    return false;\n};\nconst addFormValue = async (form, key, value) => {\n    if (value === undefined)\n        return;\n    if (value == null) {\n        throw new TypeError(`Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`);\n    }\n    // TODO: make nested formats configurable\n    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n        form.append(key, String(value));\n    }\n    else if (isUploadable(value)) {\n        const file = await toFile(value);\n        form.append(key, file);\n    }\n    else if (Array.isArray(value)) {\n        await Promise.all(value.map((entry) => addFormValue(form, key + '[]', entry)));\n    }\n    else if (typeof value === 'object') {\n        await Promise.all(Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)));\n    }\n    else {\n        throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`);\n    }\n};\n//# sourceMappingURL=uploads.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/uploads.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tambo-ai/typescript-sdk/version.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@tambo-ai/typescript-sdk/version.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = '0.50.0'; // x-release-please-version\n//# sourceMappingURL=version.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbWJvLWFpL3R5cGVzY3JpcHQtc2RrL3ZlcnNpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTywwQkFBMEI7QUFDakMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hYXJvbi9Eb3dubG9hZHMvbWNwLXg0MDIvbm9kZV9tb2R1bGVzL0B0YW1iby1haS90eXBlc2NyaXB0LXNkay92ZXJzaW9uLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVkVSU0lPTiA9ICcwLjUwLjAnOyAvLyB4LXJlbGVhc2UtcGxlYXNlLXZlcnNpb25cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24ubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tambo-ai/typescript-sdk/version.mjs\n");

/***/ })

};
;