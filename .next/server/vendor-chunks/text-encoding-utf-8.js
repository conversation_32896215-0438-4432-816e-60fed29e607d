"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/text-encoding-utf-8";
exports.ids = ["vendor-chunks/text-encoding-utf-8"];
exports.modules = {

/***/ "(rsc)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js":
/*!**************************************************************!*\
  !*** ./node_modules/text-encoding-utf-8/lib/encoding.lib.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n// This is free and unencumbered software released into the public domain.\n// See LICENSE.md for more information.\n\n//\n// Utilities\n//\n\n/**\n * @param {number} a The number to test.\n * @param {number} min The minimum value in the range, inclusive.\n * @param {number} max The maximum value in the range, inclusive.\n * @return {boolean} True if a >= min and a <= max.\n */\nfunction inRange(a, min, max) {\n  return min <= a && a <= max;\n}\n\n/**\n * @param {*} o\n * @return {Object}\n */\nfunction ToDictionary(o) {\n  if (o === undefined) return {};\n  if (o === Object(o)) return o;\n  throw TypeError('Could not convert argument to dictionary');\n}\n\n/**\n * @param {string} string Input string of UTF-16 code units.\n * @return {!Array.<number>} Code points.\n */\nfunction stringToCodePoints(string) {\n  // https://heycam.github.io/webidl/#dfn-obtain-unicode\n\n  // 1. Let S be the DOMString value.\n  var s = String(string);\n\n  // 2. Let n be the length of S.\n  var n = s.length;\n\n  // 3. Initialize i to 0.\n  var i = 0;\n\n  // 4. Initialize U to be an empty sequence of Unicode characters.\n  var u = [];\n\n  // 5. While i < n:\n  while (i < n) {\n\n    // 1. Let c be the code unit in S at index i.\n    var c = s.charCodeAt(i);\n\n    // 2. Depending on the value of c:\n\n    // c < 0xD800 or c > 0xDFFF\n    if (c < 0xD800 || c > 0xDFFF) {\n      // Append to U the Unicode character with code point c.\n      u.push(c);\n    }\n\n    // 0xDC00 ≤ c ≤ 0xDFFF\n    else if (0xDC00 <= c && c <= 0xDFFF) {\n      // Append to U a U+FFFD REPLACEMENT CHARACTER.\n      u.push(0xFFFD);\n    }\n\n    // 0xD800 ≤ c ≤ 0xDBFF\n    else if (0xD800 <= c && c <= 0xDBFF) {\n      // 1. If i = n−1, then append to U a U+FFFD REPLACEMENT\n      // CHARACTER.\n      if (i === n - 1) {\n        u.push(0xFFFD);\n      }\n      // 2. Otherwise, i < n−1:\n      else {\n        // 1. Let d be the code unit in S at index i+1.\n        var d = string.charCodeAt(i + 1);\n\n        // 2. If 0xDC00 ≤ d ≤ 0xDFFF, then:\n        if (0xDC00 <= d && d <= 0xDFFF) {\n          // 1. Let a be c & 0x3FF.\n          var a = c & 0x3FF;\n\n          // 2. Let b be d & 0x3FF.\n          var b = d & 0x3FF;\n\n          // 3. Append to U the Unicode character with code point\n          // 2^16+2^10*a+b.\n          u.push(0x10000 + (a << 10) + b);\n\n          // 4. Set i to i+1.\n          i += 1;\n        }\n\n        // 3. Otherwise, d < 0xDC00 or d > 0xDFFF. Append to U a\n        // U+FFFD REPLACEMENT CHARACTER.\n        else  {\n          u.push(0xFFFD);\n        }\n      }\n    }\n\n    // 3. Set i to i+1.\n    i += 1;\n  }\n\n  // 6. Return U.\n  return u;\n}\n\n/**\n * @param {!Array.<number>} code_points Array of code points.\n * @return {string} string String of UTF-16 code units.\n */\nfunction codePointsToString(code_points) {\n  var s = '';\n  for (var i = 0; i < code_points.length; ++i) {\n    var cp = code_points[i];\n    if (cp <= 0xFFFF) {\n      s += String.fromCharCode(cp);\n    } else {\n      cp -= 0x10000;\n      s += String.fromCharCode((cp >> 10) + 0xD800,\n                               (cp & 0x3FF) + 0xDC00);\n    }\n  }\n  return s;\n}\n\n\n//\n// Implementation of Encoding specification\n// https://encoding.spec.whatwg.org/\n//\n\n//\n// 3. Terminology\n//\n\n/**\n * End-of-stream is a special token that signifies no more tokens\n * are in the stream.\n * @const\n */ var end_of_stream = -1;\n\n/**\n * A stream represents an ordered sequence of tokens.\n *\n * @constructor\n * @param {!(Array.<number>|Uint8Array)} tokens Array of tokens that provide the\n * stream.\n */\nfunction Stream(tokens) {\n  /** @type {!Array.<number>} */\n  this.tokens = [].slice.call(tokens);\n}\n\nStream.prototype = {\n  /**\n   * @return {boolean} True if end-of-stream has been hit.\n   */\n  endOfStream: function() {\n    return !this.tokens.length;\n  },\n\n  /**\n   * When a token is read from a stream, the first token in the\n   * stream must be returned and subsequently removed, and\n   * end-of-stream must be returned otherwise.\n   *\n   * @return {number} Get the next token from the stream, or\n   * end_of_stream.\n   */\n   read: function() {\n    if (!this.tokens.length)\n      return end_of_stream;\n     return this.tokens.shift();\n   },\n\n  /**\n   * When one or more tokens are prepended to a stream, those tokens\n   * must be inserted, in given order, before the first token in the\n   * stream.\n   *\n   * @param {(number|!Array.<number>)} token The token(s) to prepend to the stream.\n   */\n  prepend: function(token) {\n    if (Array.isArray(token)) {\n      var tokens = /**@type {!Array.<number>}*/(token);\n      while (tokens.length)\n        this.tokens.unshift(tokens.pop());\n    } else {\n      this.tokens.unshift(token);\n    }\n  },\n\n  /**\n   * When one or more tokens are pushed to a stream, those tokens\n   * must be inserted, in given order, after the last token in the\n   * stream.\n   *\n   * @param {(number|!Array.<number>)} token The tokens(s) to prepend to the stream.\n   */\n  push: function(token) {\n    if (Array.isArray(token)) {\n      var tokens = /**@type {!Array.<number>}*/(token);\n      while (tokens.length)\n        this.tokens.push(tokens.shift());\n    } else {\n      this.tokens.push(token);\n    }\n  }\n};\n\n//\n// 4. Encodings\n//\n\n// 4.1 Encoders and decoders\n\n/** @const */\nvar finished = -1;\n\n/**\n * @param {boolean} fatal If true, decoding errors raise an exception.\n * @param {number=} opt_code_point Override the standard fallback code point.\n * @return {number} The code point to insert on a decoding error.\n */\nfunction decoderError(fatal, opt_code_point) {\n  if (fatal)\n    throw TypeError('Decoder error');\n  return opt_code_point || 0xFFFD;\n}\n\n//\n// 7. API\n//\n\n/** @const */ var DEFAULT_ENCODING = 'utf-8';\n\n// 7.1 Interface TextDecoder\n\n/**\n * @constructor\n * @param {string=} encoding The label of the encoding;\n *     defaults to 'utf-8'.\n * @param {Object=} options\n */\nfunction TextDecoder(encoding, options) {\n  if (!(this instanceof TextDecoder)) {\n    return new TextDecoder(encoding, options);\n  }\n  encoding = encoding !== undefined ? String(encoding).toLowerCase() : DEFAULT_ENCODING;\n  if (encoding !== DEFAULT_ENCODING) {\n    throw new Error('Encoding not supported. Only utf-8 is supported');\n  }\n  options = ToDictionary(options);\n\n  /** @private @type {boolean} */\n  this._streaming = false;\n  /** @private @type {boolean} */\n  this._BOMseen = false;\n  /** @private @type {?Decoder} */\n  this._decoder = null;\n  /** @private @type {boolean} */\n  this._fatal = Boolean(options['fatal']);\n  /** @private @type {boolean} */\n  this._ignoreBOM = Boolean(options['ignoreBOM']);\n\n  Object.defineProperty(this, 'encoding', {value: 'utf-8'});\n  Object.defineProperty(this, 'fatal', {value: this._fatal});\n  Object.defineProperty(this, 'ignoreBOM', {value: this._ignoreBOM});\n}\n\nTextDecoder.prototype = {\n  /**\n   * @param {ArrayBufferView=} input The buffer of bytes to decode.\n   * @param {Object=} options\n   * @return {string} The decoded string.\n   */\n  decode: function decode(input, options) {\n    var bytes;\n    if (typeof input === 'object' && input instanceof ArrayBuffer) {\n      bytes = new Uint8Array(input);\n    } else if (typeof input === 'object' && 'buffer' in input &&\n               input.buffer instanceof ArrayBuffer) {\n      bytes = new Uint8Array(input.buffer,\n                             input.byteOffset,\n                             input.byteLength);\n    } else {\n      bytes = new Uint8Array(0);\n    }\n\n    options = ToDictionary(options);\n\n    if (!this._streaming) {\n      this._decoder = new UTF8Decoder({fatal: this._fatal});\n      this._BOMseen = false;\n    }\n    this._streaming = Boolean(options['stream']);\n\n    var input_stream = new Stream(bytes);\n\n    var code_points = [];\n\n    /** @type {?(number|!Array.<number>)} */\n    var result;\n\n    while (!input_stream.endOfStream()) {\n      result = this._decoder.handler(input_stream, input_stream.read());\n      if (result === finished)\n        break;\n      if (result === null)\n        continue;\n      if (Array.isArray(result))\n        code_points.push.apply(code_points, /**@type {!Array.<number>}*/(result));\n      else\n        code_points.push(result);\n    }\n    if (!this._streaming) {\n      do {\n        result = this._decoder.handler(input_stream, input_stream.read());\n        if (result === finished)\n          break;\n        if (result === null)\n          continue;\n        if (Array.isArray(result))\n          code_points.push.apply(code_points, /**@type {!Array.<number>}*/(result));\n        else\n          code_points.push(result);\n      } while (!input_stream.endOfStream());\n      this._decoder = null;\n    }\n\n    if (code_points.length) {\n      // If encoding is one of utf-8, utf-16be, and utf-16le, and\n      // ignore BOM flag and BOM seen flag are unset, run these\n      // subsubsteps:\n      if (['utf-8'].indexOf(this.encoding) !== -1 &&\n          !this._ignoreBOM && !this._BOMseen) {\n        // If token is U+FEFF, set BOM seen flag.\n        if (code_points[0] === 0xFEFF) {\n          this._BOMseen = true;\n          code_points.shift();\n        } else {\n          // Otherwise, if token is not end-of-stream, set BOM seen\n          // flag and append token to output.\n          this._BOMseen = true;\n        }\n      }\n    }\n\n    return codePointsToString(code_points);\n  }\n};\n\n// 7.2 Interface TextEncoder\n\n/**\n * @constructor\n * @param {string=} encoding The label of the encoding;\n *     defaults to 'utf-8'.\n * @param {Object=} options\n */\nfunction TextEncoder(encoding, options) {\n  if (!(this instanceof TextEncoder))\n    return new TextEncoder(encoding, options);\n  encoding = encoding !== undefined ? String(encoding).toLowerCase() : DEFAULT_ENCODING;\n  if (encoding !== DEFAULT_ENCODING) {\n    throw new Error('Encoding not supported. Only utf-8 is supported');\n  }\n  options = ToDictionary(options);\n\n  /** @private @type {boolean} */\n  this._streaming = false;\n  /** @private @type {?Encoder} */\n  this._encoder = null;\n  /** @private @type {{fatal: boolean}} */\n  this._options = {fatal: Boolean(options['fatal'])};\n\n  Object.defineProperty(this, 'encoding', {value: 'utf-8'});\n}\n\nTextEncoder.prototype = {\n  /**\n   * @param {string=} opt_string The string to encode.\n   * @param {Object=} options\n   * @return {Uint8Array} Encoded bytes, as a Uint8Array.\n   */\n  encode: function encode(opt_string, options) {\n    opt_string = opt_string ? String(opt_string) : '';\n    options = ToDictionary(options);\n\n    // NOTE: This option is nonstandard. None of the encodings\n    // permitted for encoding (i.e. UTF-8, UTF-16) are stateful,\n    // so streaming is not necessary.\n    if (!this._streaming)\n      this._encoder = new UTF8Encoder(this._options);\n    this._streaming = Boolean(options['stream']);\n\n    var bytes = [];\n    var input_stream = new Stream(stringToCodePoints(opt_string));\n    /** @type {?(number|!Array.<number>)} */\n    var result;\n    while (!input_stream.endOfStream()) {\n      result = this._encoder.handler(input_stream, input_stream.read());\n      if (result === finished)\n        break;\n      if (Array.isArray(result))\n        bytes.push.apply(bytes, /**@type {!Array.<number>}*/(result));\n      else\n        bytes.push(result);\n    }\n    if (!this._streaming) {\n      while (true) {\n        result = this._encoder.handler(input_stream, input_stream.read());\n        if (result === finished)\n          break;\n        if (Array.isArray(result))\n          bytes.push.apply(bytes, /**@type {!Array.<number>}*/(result));\n        else\n          bytes.push(result);\n      }\n      this._encoder = null;\n    }\n    return new Uint8Array(bytes);\n  }\n};\n\n//\n// 8. The encoding\n//\n\n// 8.1 utf-8\n\n/**\n * @constructor\n * @implements {Decoder}\n * @param {{fatal: boolean}} options\n */\nfunction UTF8Decoder(options) {\n  var fatal = options.fatal;\n\n  // utf-8's decoder's has an associated utf-8 code point, utf-8\n  // bytes seen, and utf-8 bytes needed (all initially 0), a utf-8\n  // lower boundary (initially 0x80), and a utf-8 upper boundary\n  // (initially 0xBF).\n  var /** @type {number} */ utf8_code_point = 0,\n      /** @type {number} */ utf8_bytes_seen = 0,\n      /** @type {number} */ utf8_bytes_needed = 0,\n      /** @type {number} */ utf8_lower_boundary = 0x80,\n      /** @type {number} */ utf8_upper_boundary = 0xBF;\n\n  /**\n   * @param {Stream} stream The stream of bytes being decoded.\n   * @param {number} bite The next byte read from the stream.\n   * @return {?(number|!Array.<number>)} The next code point(s)\n   *     decoded, or null if not enough data exists in the input\n   *     stream to decode a complete code point.\n   */\n  this.handler = function(stream, bite) {\n    // 1. If byte is end-of-stream and utf-8 bytes needed is not 0,\n    // set utf-8 bytes needed to 0 and return error.\n    if (bite === end_of_stream && utf8_bytes_needed !== 0) {\n      utf8_bytes_needed = 0;\n      return decoderError(fatal);\n    }\n\n    // 2. If byte is end-of-stream, return finished.\n    if (bite === end_of_stream)\n      return finished;\n\n    // 3. If utf-8 bytes needed is 0, based on byte:\n    if (utf8_bytes_needed === 0) {\n\n      // 0x00 to 0x7F\n      if (inRange(bite, 0x00, 0x7F)) {\n        // Return a code point whose value is byte.\n        return bite;\n      }\n\n      // 0xC2 to 0xDF\n      if (inRange(bite, 0xC2, 0xDF)) {\n        // Set utf-8 bytes needed to 1 and utf-8 code point to byte\n        // − 0xC0.\n        utf8_bytes_needed = 1;\n        utf8_code_point = bite - 0xC0;\n      }\n\n      // 0xE0 to 0xEF\n      else if (inRange(bite, 0xE0, 0xEF)) {\n        // 1. If byte is 0xE0, set utf-8 lower boundary to 0xA0.\n        if (bite === 0xE0)\n          utf8_lower_boundary = 0xA0;\n        // 2. If byte is 0xED, set utf-8 upper boundary to 0x9F.\n        if (bite === 0xED)\n          utf8_upper_boundary = 0x9F;\n        // 3. Set utf-8 bytes needed to 2 and utf-8 code point to\n        // byte − 0xE0.\n        utf8_bytes_needed = 2;\n        utf8_code_point = bite - 0xE0;\n      }\n\n      // 0xF0 to 0xF4\n      else if (inRange(bite, 0xF0, 0xF4)) {\n        // 1. If byte is 0xF0, set utf-8 lower boundary to 0x90.\n        if (bite === 0xF0)\n          utf8_lower_boundary = 0x90;\n        // 2. If byte is 0xF4, set utf-8 upper boundary to 0x8F.\n        if (bite === 0xF4)\n          utf8_upper_boundary = 0x8F;\n        // 3. Set utf-8 bytes needed to 3 and utf-8 code point to\n        // byte − 0xF0.\n        utf8_bytes_needed = 3;\n        utf8_code_point = bite - 0xF0;\n      }\n\n      // Otherwise\n      else {\n        // Return error.\n        return decoderError(fatal);\n      }\n\n      // Then (byte is in the range 0xC2 to 0xF4) set utf-8 code\n      // point to utf-8 code point << (6 × utf-8 bytes needed) and\n      // return continue.\n      utf8_code_point = utf8_code_point << (6 * utf8_bytes_needed);\n      return null;\n    }\n\n    // 4. If byte is not in the range utf-8 lower boundary to utf-8\n    // upper boundary, run these substeps:\n    if (!inRange(bite, utf8_lower_boundary, utf8_upper_boundary)) {\n\n      // 1. Set utf-8 code point, utf-8 bytes needed, and utf-8\n      // bytes seen to 0, set utf-8 lower boundary to 0x80, and set\n      // utf-8 upper boundary to 0xBF.\n      utf8_code_point = utf8_bytes_needed = utf8_bytes_seen = 0;\n      utf8_lower_boundary = 0x80;\n      utf8_upper_boundary = 0xBF;\n\n      // 2. Prepend byte to stream.\n      stream.prepend(bite);\n\n      // 3. Return error.\n      return decoderError(fatal);\n    }\n\n    // 5. Set utf-8 lower boundary to 0x80 and utf-8 upper boundary\n    // to 0xBF.\n    utf8_lower_boundary = 0x80;\n    utf8_upper_boundary = 0xBF;\n\n    // 6. Increase utf-8 bytes seen by one and set utf-8 code point\n    // to utf-8 code point + (byte − 0x80) << (6 × (utf-8 bytes\n    // needed − utf-8 bytes seen)).\n    utf8_bytes_seen += 1;\n    utf8_code_point += (bite - 0x80) << (6 * (utf8_bytes_needed - utf8_bytes_seen));\n\n    // 7. If utf-8 bytes seen is not equal to utf-8 bytes needed,\n    // continue.\n    if (utf8_bytes_seen !== utf8_bytes_needed)\n      return null;\n\n    // 8. Let code point be utf-8 code point.\n    var code_point = utf8_code_point;\n\n    // 9. Set utf-8 code point, utf-8 bytes needed, and utf-8 bytes\n    // seen to 0.\n    utf8_code_point = utf8_bytes_needed = utf8_bytes_seen = 0;\n\n    // 10. Return a code point whose value is code point.\n    return code_point;\n  };\n}\n\n/**\n * @constructor\n * @implements {Encoder}\n * @param {{fatal: boolean}} options\n */\nfunction UTF8Encoder(options) {\n  var fatal = options.fatal;\n  /**\n   * @param {Stream} stream Input stream.\n   * @param {number} code_point Next code point read from the stream.\n   * @return {(number|!Array.<number>)} Byte(s) to emit.\n   */\n  this.handler = function(stream, code_point) {\n    // 1. If code point is end-of-stream, return finished.\n    if (code_point === end_of_stream)\n      return finished;\n\n    // 2. If code point is in the range U+0000 to U+007F, return a\n    // byte whose value is code point.\n    if (inRange(code_point, 0x0000, 0x007f))\n      return code_point;\n\n    // 3. Set count and offset based on the range code point is in:\n    var count, offset;\n    // U+0080 to U+07FF:    1 and 0xC0\n    if (inRange(code_point, 0x0080, 0x07FF)) {\n      count = 1;\n      offset = 0xC0;\n    }\n    // U+0800 to U+FFFF:    2 and 0xE0\n    else if (inRange(code_point, 0x0800, 0xFFFF)) {\n      count = 2;\n      offset = 0xE0;\n    }\n    // U+10000 to U+10FFFF: 3 and 0xF0\n    else if (inRange(code_point, 0x10000, 0x10FFFF)) {\n      count = 3;\n      offset = 0xF0;\n    }\n\n    // 4.Let bytes be a byte sequence whose first byte is (code\n    // point >> (6 × count)) + offset.\n    var bytes = [(code_point >> (6 * count)) + offset];\n\n    // 5. Run these substeps while count is greater than 0:\n    while (count > 0) {\n\n      // 1. Set temp to code point >> (6 × (count − 1)).\n      var temp = code_point >> (6 * (count - 1));\n\n      // 2. Append to bytes 0x80 | (temp & 0x3F).\n      bytes.push(0x80 | (temp & 0x3F));\n\n      // 3. Decrease count by one.\n      count -= 1;\n    }\n\n    // 6. Return bytes bytes, in order.\n    return bytes;\n  };\n}\n\nexports.TextEncoder = TextEncoder;\nexports.TextDecoder = TextDecoder;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/text-encoding-utf-8/lib/encoding.lib.js\n");

/***/ })

};
;