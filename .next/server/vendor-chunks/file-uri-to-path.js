/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/file-uri-to-path";
exports.ids = ["vendor-chunks/file-uri-to-path"];
exports.modules = {

/***/ "(rsc)/./node_modules/file-uri-to-path/index.js":
/*!************************************************!*\
  !*** ./node_modules/file-uri-to-path/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/**\n * Module dependencies.\n */\n\nvar sep = (__webpack_require__(/*! path */ \"path\").sep) || '/';\n\n/**\n * Module exports.\n */\n\nmodule.exports = fileUriToPath;\n\n/**\n * File URI to Path function.\n *\n * @param {String} uri\n * @return {String} path\n * @api public\n */\n\nfunction fileUriToPath (uri) {\n  if ('string' != typeof uri ||\n      uri.length <= 7 ||\n      'file://' != uri.substring(0, 7)) {\n    throw new TypeError('must pass in a file:// URI to convert to a file path');\n  }\n\n  var rest = decodeURI(uri.substring(7));\n  var firstSlash = rest.indexOf('/');\n  var host = rest.substring(0, firstSlash);\n  var path = rest.substring(firstSlash + 1);\n\n  // 2.  Scheme Definition\n  // As a special case, <host> can be the string \"localhost\" or the empty\n  // string; this is interpreted as \"the machine from which the URL is\n  // being interpreted\".\n  if ('localhost' == host) host = '';\n\n  if (host) {\n    host = sep + sep + host;\n  }\n\n  // 3.2  Drives, drive letters, mount points, file system root\n  // Drive letters are mapped into the top of a file URI in various ways,\n  // depending on the implementation; some applications substitute\n  // vertical bar (\"|\") for the colon after the drive letter, yielding\n  // \"file:///c|/tmp/test.txt\".  In some cases, the colon is left\n  // unchanged, as in \"file:///c:/tmp/test.txt\".  In other cases, the\n  // colon is simply omitted, as in \"file:///c/tmp/test.txt\".\n  path = path.replace(/^(.+)\\|/, '$1:');\n\n  // for Windows, we need to invert the path separators from what a URI uses\n  if (sep == '\\\\') {\n    path = path.replace(/\\//g, '\\\\');\n  }\n\n  if (/^.+\\:/.test(path)) {\n    // has Windows drive at beginning of path\n  } else {\n    // unix path…\n    path = sep + path;\n  }\n\n  return host + path;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/file-uri-to-path/index.js\n");

/***/ })

};
;