// Simple test script to verify paymaster API endpoint
const testPaymaster = async () => {
  try {
    const partialUserOp = {
      sender: '******************************************',
      nonce: '0x0',
      callData: '0xa9059cbb000000000000000000000000742d35cc6634c0532925a3b8d4c9db96c4b4d8b6000000000000000000000000000000000000000000000000000000000000f4240', // transfer 1 USDC
    };

    console.log('Testing paymaster endpoint...');
    console.log('Partial UserOp:', partialUserOp);

    const response = await fetch('http://localhost:3000/api/paymaster', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ partialUserOp }),
    });

    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Paymaster response:', data);
    } else {
      const error = await response.json();
      console.error('Paymaster error:', error);
    }
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  testPaymaster();
}

module.exports = { testPaymaster };
